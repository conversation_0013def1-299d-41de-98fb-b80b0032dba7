package com.yhl.scp.audit.feign;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.constants.ServletContextConstants;
import com.yhl.scp.ips.log.dto.DataChangeRecordDTO;
import com.yhl.scp.ips.log.dto.DataChangeRecordQueryDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <code>IpsFeign</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-23 15:44:47
 */
@FeignClient(value = ServletContextConstants.AUDIT, path = "/", configuration = {AuditFeignConfig.class}, url = "${audit.feign.url:}")
public interface AuditFeign {

    @ApiOperation(value = "数据变更记录分页查询")
    @PostMapping(value = "/dataChangeRecord/page")
    BaseResponse<Page<DataChangeRecordDTO>> selectDataChangeRecordsByPage(@RequestParam("pageNum") int pageNum, @RequestParam("pageSize") int pageSize, @RequestBody DataChangeRecordQueryDTO params);

    @ApiOperation(value = "网关日志分页查询")
    @PostMapping(value = "/requestLog/page")
    BaseResponse<Page<RequestLogDTO>> selectRequestLogsByPage(@RequestParam("pageNum") int pageNum, @RequestParam("pageSize") int pageSize, @RequestBody RequestLogQueryDTO queryParams);
}
