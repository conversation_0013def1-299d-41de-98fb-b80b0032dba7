package com.yhl.scp.audit.feign;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Logger;
import feign.codec.Decoder;
import feign.jackson.JacksonDecoder;
import org.springframework.cloud.openfeign.support.PageJacksonModule;
import org.springframework.cloud.openfeign.support.SortJacksonModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName FeignConfig
 * @Description FeignConfig
 * @Date 2022-11-16 13:43:07
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
@Configuration
public class AuditFeignConfig {

    @Bean
    public Logger.Level getLogger() {
        return Logger.Level.FULL;
    }

    @Bean
    public Decoder feignDecoder(ObjectMapper objectMapper) {
        // 注册分页模块
        objectMapper.registerModule(new PageJacksonModule());
        objectMapper.registerModule(new SortJacksonModule());
        return new JacksonDecoder(objectMapper);
    }

}