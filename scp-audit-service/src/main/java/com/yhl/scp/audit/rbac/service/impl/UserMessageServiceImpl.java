package com.yhl.scp.audit.rbac.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.audit.rbac.convertor.UserMessageConvertor;
import com.yhl.scp.audit.rbac.domain.entity.UserMessageDO;
import com.yhl.scp.audit.rbac.domain.service.UserMessageDomainService;
import com.yhl.scp.audit.rbac.infrastructure.dao.UserMessageDao;
import com.yhl.scp.audit.rbac.infrastructure.po.UserMessagePO;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.rbac.service.UserMessageService;
import com.yhl.scp.ips.rbac.vo.UserMessageVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>UserMessageServiceImpl</code>
 * <p>
 * UserMessageServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 09:47:49
 */
@Slf4j
@Service
public class UserMessageServiceImpl extends AbstractService implements UserMessageService {

    @Resource
    private UserMessageDao userMessageDao;

    @Resource
    private UserMessageDomainService userMessageDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(UserMessageDTO userMessageDTO) {
        // 0.数据转换
        UserMessageDO userMessageDO = UserMessageConvertor.INSTANCE.dto2Do(userMessageDTO);
        UserMessagePO userMessagePO = UserMessageConvertor.INSTANCE.dto2Po(userMessageDTO);
        // 1.数据校验
        userMessageDomainService.validation(userMessageDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(userMessagePO);
        userMessageDao.insert(userMessagePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(UserMessageDTO userMessageDTO) {
        // 0.数据转换
        UserMessageDO userMessageDO = UserMessageConvertor.INSTANCE.dto2Do(userMessageDTO);
        UserMessagePO userMessagePO = UserMessageConvertor.INSTANCE.dto2Po(userMessageDTO);
        // 1.数据校验
        userMessageDomainService.validation(userMessageDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(userMessagePO);
        userMessageDao.update(userMessagePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<UserMessageDTO> list) {
        List<UserMessagePO> newList = UserMessageConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        userMessageDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<UserMessageDTO> list) {
        List<UserMessagePO> newList = UserMessageConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        userMessageDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return userMessageDao.deleteBatch(idList);
        }
        return userMessageDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public UserMessageVO selectByPrimaryKey(String id) {
        UserMessagePO po = userMessageDao.selectByPrimaryKey(id);
        return UserMessageConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_auth_user_message")
    public List<UserMessageVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_auth_user_message")
    public List<UserMessageVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<UserMessageVO> dataList = userMessageDao.selectByCondition(sortParam, queryCriteriaParam);
        UserMessageServiceImpl target = springBeanUtils.getBean(UserMessageServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<UserMessageVO> selectByParams(Map<String, Object> params) {
        List<UserMessagePO> list = userMessageDao.selectByParams(params);
        return UserMessageConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<UserMessageVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<UserMessageVO> invocation(List<UserMessageVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
