package com.yhl.scp.audit.mq.receiver;

import com.alibaba.fastjson.JSONObject;
import com.yhl.scp.audit.dataChangeLog.service.DataChangeRecordService;
import com.yhl.scp.audit.entity.RequestLogMongoDTO;
import com.yhl.scp.audit.requestLog.service.RequestLogService;
import com.yhl.scp.ips.conf.dto.UrlTableDTO;
import com.yhl.scp.ips.conf.service.UrlTableService;
import com.yhl.scp.ips.constant.MqConstants;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.rbac.service.UserMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class MqMessageReceiver {

    @Resource
    private DataChangeRecordService dataChangeRecordService;

    @Resource
    private UrlTableService urlTableService;

    @Resource
    private RequestLogService requestLogService;

    @Resource
    private UserMessageService userMessageService;

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = "${spring.profiles.active}." + MqConstants.DATA_RECORD_CHANGE_QUEUE, ignoreDeclarationExceptions = "true"), exchange = @Exchange(value = "${spring.profiles.active}." + MqConstants.BPIM_EVENT_EXCHANGE, ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC), key = "${spring.profiles.active}." + MqConstants.DATA_RECORD_CHANGE_ROUTING_KEY))
    public void receiveDataRecordChangeMessage(String data) {
        try {
            dataChangeRecordService.handle(data);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = "${spring.profiles.active}." + MqConstants.URL_TABLE_CHANGE_QUEUE, ignoreDeclarationExceptions = "true"), exchange = @Exchange(value = "${spring.profiles.active}." + MqConstants.BPIM_EVENT_EXCHANGE, ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC), key = "${spring.profiles.active}." + MqConstants.URL_TABLE_ROUTING_KEY))
    public void receiveUrlTableMessage(String data) {
        try {
            if (StringUtils.isBlank(data)) {
                return;
            }
            List<UrlTableDTO> urlTableDTOS = JSONObject.parseArray(data, UrlTableDTO.class);
            urlTableService.handleUrlTables(urlTableDTOS);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = "${spring.profiles.active}." + MqConstants.REQUEST_LOG_QUEUE, ignoreDeclarationExceptions = "true"), exchange = @Exchange(value = "${spring.profiles.active}." + MqConstants.BPIM_EVENT_EXCHANGE, ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC), key = "${spring.profiles.active}." + MqConstants.REQUEST_LOG_ROUTING_KEY))
    public void receiveRequestLogMessage(String data) {
        try {
            if (StringUtils.isBlank(data)) {
                return;
            }
            RequestLogMongoDTO requestLogMongoDTO = JSONObject.parseObject(data, RequestLogMongoDTO.class);
            log.info("接收到RabbitMQ数据（request-log）消息，requestURI：{}", requestLogMongoDTO.getRequestUri());
            requestLogService.saveData(requestLogMongoDTO);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = "${spring.profiles.active}." + MqConstants.USER_MESSAGE_QUEUE, ignoreDeclarationExceptions = "true"), exchange = @Exchange(value = "${spring.profiles.active}." + MqConstants.BPIM_EVENT_EXCHANGE, ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC), key = "${spring.profiles.active}." + MqConstants.USER_MESSAGE_KEY))
    public void receiveUserMassage(String data) {
        try {
            log.info("接收到RabbitMQ数据（user-message）消息,开始处理");
            if (StringUtils.isBlank(data)) {
                log.warn("RabbitMQ数据（user-message）消息为空");
                return;
            }
            List<UserMessageDTO> userMessageDTOS = JSONObject.parseArray(data, UserMessageDTO.class);
            log.info("接收到RabbitMQ数据（user-message）消息,总条数:{}", userMessageDTOS.size());
            userMessageService.doCreateBatch(userMessageDTOS);
            log.info("处理RabbitMQ数据（user-message）消息结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}