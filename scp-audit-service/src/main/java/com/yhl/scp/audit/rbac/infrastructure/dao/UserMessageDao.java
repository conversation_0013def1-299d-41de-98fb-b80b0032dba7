package com.yhl.scp.audit.rbac.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.audit.rbac.infrastructure.po.UserMessagePO;
import com.yhl.scp.ips.rbac.vo.UserMessageVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>UserMessageDao</code>
 * <p>
 * UserMessageDao
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 09:47:49
 */
public interface UserMessageDao extends BaseDao<UserMessagePO, UserMessageVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link UserMessageVO}
     */
    List<UserMessageVO> selectVOByParams(@Param("params") Map<String, Object> params);

}
