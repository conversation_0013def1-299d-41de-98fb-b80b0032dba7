package com.yhl.scp.audit.requestLog.service.impl;

import com.yhl.scp.audit.entity.RequestLogMongoDTO;
import com.yhl.scp.audit.requestLog.dao.RequestLogRepository;
import com.yhl.scp.audit.requestLog.service.RequestLogService;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogQueryDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class RequestLogServiceImpl implements RequestLogService {

    private static final String FIELD_REQUEST_TIME = "requestTime";
    @Resource
    private RequestLogRepository requestLogRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private RabbitTemplate rabbitTemplate;

    @Override
    public void insert(RequestLogDTO requestLogDTO) {
    }

    @Override
    public void saveData(RequestLogMongoDTO requestLogMongoDTO) {
        requestLogRepository.save(requestLogMongoDTO);
    }

    @Override
    public PageImpl<RequestLogDTO> selectByPage(Integer pageNum, Integer pageSize, RequestLogQueryDTO queryParams) {
        Criteria criteria = new Criteria();
        List<Criteria> criteriaList = new ArrayList<>();
        if (queryParams != null) {
            if (StringUtils.isNotEmpty(queryParams.getUserId())) {
                criteriaList.add(Criteria.where("userId").is(queryParams.getUserId()));
            }
            if (Objects.nonNull(queryParams.getResponseStatus())) {
                criteriaList.add(Criteria.where("responseStatus").is(queryParams.getResponseStatus()));
            }
            if (StringUtils.isNotEmpty(queryParams.getRequestUri())) {
                criteriaList.add(Criteria.where("requestUri").regex(queryParams.getRequestUri(), "i"));
            }
            if (StringUtils.isNotEmpty(queryParams.getUserAgent())) {
                criteriaList.add(Criteria.where("userAgent").regex(queryParams.getUserAgent(), "i"));
            }
            if (queryParams.getBeginTime() != null) {
                criteriaList.add(Criteria.where(FIELD_REQUEST_TIME).gte(queryParams.getBeginTime()));
            }
            if (queryParams.getEndTime() != null) {
                criteriaList.add(Criteria.where(FIELD_REQUEST_TIME).lte(queryParams.getEndTime()));
            }
        }
        if (!criteriaList.isEmpty()) {
            criteria.andOperator(criteriaList.toArray(new Criteria[0]));
        }
        Query mongoQuery = new Query(criteria);
        long total = mongoTemplate.count(mongoQuery, RequestLogMongoDTO.class);
        mongoQuery.with(Sort.by(Sort.Direction.DESC, FIELD_REQUEST_TIME));
        mongoQuery.skip((long) (pageNum - 1) * pageSize)
                .limit(pageSize);
        List<RequestLogMongoDTO> result = mongoTemplate.find(mongoQuery, RequestLogMongoDTO.class);
        return new PageImpl<>(convertResult(result), PageRequest.of(pageNum - 1, pageSize), total);
    }

    private List<RequestLogDTO> convertResult(List<RequestLogMongoDTO> requestLogMongoDTOList) {
        List<RequestLogDTO> requestLogDTOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(requestLogMongoDTOList)) {
            for (RequestLogMongoDTO requestLogMongoDTO : requestLogMongoDTOList) {
                RequestLogDTO requestLogDTO = RequestLogDTO.builder()
                        .id(requestLogMongoDTO.getId())
                        .requestTime(requestLogMongoDTO.getRequestTime())
                        .userId(requestLogMongoDTO.getUserId())
                        .token(requestLogMongoDTO.getToken())
                        .requestUri(requestLogMongoDTO.getRequestUri())
                        .responseStatus(requestLogMongoDTO.getResponseStatus())
                        .requestPayLoad(requestLogMongoDTO.getRequestPayLoad())
                        .responsePayLoad(requestLogMongoDTO.getResponsePayLoad())
                        .costTime(requestLogMongoDTO.getCostTime())
                        .requestHeaders(requestLogMongoDTO.getRequestHeaders())
                        .userAgent(requestLogMongoDTO.getUserAgent())
                        .createTime(requestLogMongoDTO.getCreateTime())
                        .build();
                requestLogDTOList.add(requestLogDTO);
            }
        }
        return requestLogDTOList;
    }
}
