package com.yhl.scp.audit.conf.domain.factory;

import com.yhl.scp.audit.conf.domain.entity.UrlTableDO;
import com.yhl.scp.audit.conf.infrastructure.dao.UrlTableDao;
import com.yhl.scp.ips.conf.dto.UrlTableDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class UrlTableFactory {

    @Resource
    private UrlTableDao urlTableDao;

    UrlTableDO create(UrlTableDTO dto) {
        // TODO
        return null;
    }

}
