package com.yhl.scp.audit;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration;
import org.springframework.boot.autoconfigure.mail.MailSenderAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.session.FlushMode;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <code>AuditApplication</code>
 * <p>
 * Audit服务启动类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-10 09:39:08
 */
@SpringBootApplication(scanBasePackages = {"com.yhl"}, exclude = {LiquibaseAutoConfiguration.class, MailSenderAutoConfiguration.class})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.yhl")
@EnableTransactionManagement
@MapperScan("com.yhl.**.dao")
@AutoConfigureBefore(ServiceRegistryAutoConfiguration.class)
@ServletComponentScan
@EnableRedisHttpSession(flushMode = FlushMode.IMMEDIATE, maxInactiveIntervalInSeconds = 14400)
@EnableAsync
public class AuditApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuditApplication.class, args);
    }

}