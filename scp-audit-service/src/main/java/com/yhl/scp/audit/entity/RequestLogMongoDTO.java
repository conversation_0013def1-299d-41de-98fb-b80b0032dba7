package com.yhl.scp.audit.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel(value = "log_request_info")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document("log_request_info")
public class RequestLogMongoDTO implements Serializable {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @Id
    private String id;
    /**
     * 请求时间
     */
    @ApiModelProperty(value = "请求时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date requestTime;
    /**
     * 用户
     */
    @ApiModelProperty(value = "用户")
    private String userId;
    /**
     * token
     */
    @ApiModelProperty(value = "token")
    private String token;
    /**
     * 请求uri
     */
    @ApiModelProperty(value = "请求uri")
    private String requestUri;
    /**
     * 响应状态
     */
    @ApiModelProperty(value = "响应状态")
    private Integer responseStatus;
    /**
     * 请求报文（request payload）
     */
    @ApiModelProperty(value = "请求报文（request payload）")
    private String requestPayLoad;
    /**
     * 响应报文(response payload)
     */
    @ApiModelProperty(value = "响应报文(response payload)")
    private String responsePayLoad;
    /**
     * 请求耗时
     */
    @ApiModelProperty(value = "请求耗时")
    private Long costTime;
    /**
     * 请求头
     */
    @ApiModelProperty(value = "请求头")
    private String requestHeaders;
    /**
     * 浏览器（user-agent）
     */
    @ApiModelProperty(value = "浏览器（user-agent）")
    private String userAgent;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
