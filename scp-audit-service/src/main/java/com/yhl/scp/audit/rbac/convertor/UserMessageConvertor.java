package com.yhl.scp.audit.rbac.convertor;

import com.yhl.scp.audit.rbac.domain.entity.UserMessageDO;
import com.yhl.scp.audit.rbac.infrastructure.po.UserMessagePO;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.rbac.vo.UserMessageVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>UserMessageConvertor</code>
 * <p>
 * UserMessageConvertor
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 09:47:49
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserMessageConvertor {

    UserMessageConvertor INSTANCE = Mappers.getMapper(UserMessageConvertor.class);

    UserMessageDO dto2Do(UserMessageDTO obj);

    List<UserMessageDO> dto2Dos(List<UserMessageDTO> list);

    UserMessageDTO do2Dto(UserMessageDO obj);

    List<UserMessageDTO> do2Dtos(List<UserMessageDO> list);

    UserMessageDTO vo2Dto(UserMessageVO obj);

    List<UserMessageDTO> vo2Dtos(List<UserMessageVO> list);

    UserMessageVO po2Vo(UserMessagePO obj);

    List<UserMessageVO> po2Vos(List<UserMessagePO> list);

    UserMessagePO dto2Po(UserMessageDTO obj);

    List<UserMessagePO> dto2Pos(List<UserMessageDTO> obj);

    UserMessageVO do2Vo(UserMessageDO obj);

    UserMessagePO do2Po(UserMessageDO obj);

    UserMessageDO po2Do(UserMessagePO obj);

}
