package com.yhl.scp.audit.conf.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.audit.conf.convertor.UrlTableConvertor;
import com.yhl.scp.audit.conf.domain.entity.UrlTableDO;
import com.yhl.scp.audit.conf.domain.service.UrlTableDomainService;
import com.yhl.scp.audit.conf.infrastructure.dao.UrlTableDao;
import com.yhl.scp.audit.conf.infrastructure.po.UrlTablePO;
import com.yhl.scp.ips.conf.dto.UrlTableDTO;
import com.yhl.scp.ips.conf.service.UrlTableService;
import com.yhl.scp.ips.conf.vo.UrlTableVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UrlTableServiceImpl extends AbstractService implements UrlTableService {

    @Resource
    private UrlTableDao urlTableDao;

    @Resource
    private UrlTableDomainService urlTableDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(UrlTableDTO urlTableDTO) {
        // 0.数据转换
        UrlTablePO urlTablePO = UrlTableConvertor.INSTANCE.dto2Po(urlTableDTO);
        // 2.数据持久化
        BasePOUtils.insertFiller(urlTablePO);
        urlTableDao.insert(urlTablePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(UrlTableDTO urlTableDTO) {
        // 0.数据转换
        UrlTableDO urlTableDO = UrlTableConvertor.INSTANCE.dto2Do(urlTableDTO);
        UrlTablePO urlTablePO = UrlTableConvertor.INSTANCE.dto2Po(urlTableDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        urlTableDomainService.validation(urlTableDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(urlTablePO);
        urlTableDao.update(urlTablePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<UrlTableDTO> list) {
        List<UrlTablePO> newList = UrlTableConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        urlTableDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<UrlTableDTO> list) {
        List<UrlTablePO> newList = UrlTableConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        urlTableDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return urlTableDao.deleteBatch(idList);
        }
        return urlTableDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public UrlTableVO selectByPrimaryKey(String id) {
        UrlTablePO po = urlTableDao.selectByPrimaryKey(id);
        return UrlTableConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "URL_TABLE")
    public List<UrlTableVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "URL_TABLE")
    public List<UrlTableVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<UrlTableVO> dataList = urlTableDao.selectByCondition(sortParam, queryCriteriaParam);
        UrlTableServiceImpl target = springBeanUtils.getBean(UrlTableServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<UrlTableVO> selectByParams(Map<String, Object> params) {
        List<UrlTablePO> list = urlTableDao.selectByParams(params);
        return UrlTableConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<UrlTableVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void handleUrlTables(List<UrlTableDTO> urlTableDTOS) {
        List<UrlTableVO> existUrlTables = this.selectAll();
        Map<String, UrlTableVO> urlTableMap = CollectionUtils.isEmpty(existUrlTables) ? MapUtil.newHashMap() : existUrlTables.stream().collect(Collectors.toMap(UrlTableVO::getRequestUrl, Function.identity(), (v1, v2) -> v1));
        for (UrlTableDTO urlTableDTO : urlTableDTOS) {
            if (urlTableMap.containsKey(urlTableDTO.getRequestUrl())) {
                continue;
            }
            this.doCreate(urlTableDTO);
        }
    }

    @Override
    public BaseResponse<List<String>> selectAllTables() {
        List<String> result = Lists.newArrayList();
        List<UrlTableVO> urlTableVOS = this.selectAll();
        if (CollectionUtils.isEmpty(urlTableVOS)) {
            return BaseResponse.success(result);
        }
        result = urlTableVOS.stream().map(UrlTableVO::getDataTableName).filter(StringUtils::isNotBlank).distinct().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
        return BaseResponse.success(result);
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<UrlTableVO> invocation(List<UrlTableVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }
}
