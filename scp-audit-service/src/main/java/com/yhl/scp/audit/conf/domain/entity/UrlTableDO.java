package com.yhl.scp.audit.conf.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class UrlTableDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 346439406603659471L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 请求URL
     */
    private String requestUrl;
    /**
     * 表名
     */
    private String dataTableName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date modifyTime;

}
