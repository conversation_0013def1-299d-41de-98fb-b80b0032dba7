package com.yhl.scp.audit.requestLog.service;

import com.yhl.scp.audit.entity.RequestLogMongoDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogQueryDTO;
import org.springframework.data.domain.PageImpl;

/**
 * <AUTHOR>
 */
public interface RequestLogService {

    public void insert(RequestLogDTO requestLogDTO);

    public void saveData(RequestLogMongoDTO requestLogDTO);

    PageImpl<RequestLogDTO> selectByPage(Integer pageNum, Integer pageSize, RequestLogQueryDTO queryParams);
}
