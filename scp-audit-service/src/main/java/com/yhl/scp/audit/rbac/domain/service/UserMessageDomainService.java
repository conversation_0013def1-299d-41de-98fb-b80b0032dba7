package com.yhl.scp.audit.rbac.domain.service;

import com.yhl.scp.audit.rbac.domain.entity.UserMessageDO;
import com.yhl.scp.audit.rbac.infrastructure.dao.UserMessageDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>UserMessageDomainService</code>
 * <p>
 * UserMessageDomainService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 09:47:49
 */
@Service
public class UserMessageDomainService {

    @Resource
    private UserMessageDao userMessageDao;

    /**
     * 数据校验
     *
     * @param userMessageDO 领域对象
     */
    public void validation(UserMessageDO userMessageDO) {
        checkNotNull(userMessageDO);
        checkUniqueCode(userMessageDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param userMessageDO 领域对象
     */
    private void checkNotNull(UserMessageDO userMessageDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param userMessageDO 领域对象
     */
    private void checkUniqueCode(UserMessageDO userMessageDO) {
        // TODO
    }

}
