<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.audit.conf.infrastructure.dao.UrlTableDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.audit.conf.infrastructure.po.UrlTablePO">
        <!--@Table conf_url_table-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="request_url" jdbcType="VARCHAR" property="requestUrl"/>
        <result column="data_table_name" jdbcType="VARCHAR" property="dataTableName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.conf.vo.UrlTableVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id, request_url, data_table_name, create_time, modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.requestUrl != null and params.requestUrl != ''">
                and request_url = #{params.requestUrl,jdbcType=VARCHAR}
            </if>
            <if test="params.dataTableName != null and params.dataTableName != ''">
                and data_table_name = #{params.dataTableName,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from conf_url_table
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from conf_url_table
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from conf_url_table
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from conf_url_table
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.audit.conf.infrastructure.po.UrlTablePO">
        insert into conf_url_table(id,
                                   request_url,
                                   data_table_name,
                                   create_time,
                                   modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{requestUrl,jdbcType=VARCHAR},
                #{dataTableName,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.audit.conf.infrastructure.po.UrlTablePO">
        insert into conf_url_table(id,
                                   request_url,
                                   data_table_name,
                                   create_time,
                                   modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{requestUrl,jdbcType=VARCHAR},
                #{dataTableName,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into conf_url_table(
        id,
        request_url,
        data_table_name,
        create_time,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
            #{entity.requestUrl,jdbcType=VARCHAR},
            #{entity.dataTableName,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into conf_url_table(
        id,
        request_url,
        data_table_name,
        create_time,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.requestUrl,jdbcType=VARCHAR},
            #{entity.dataTableName,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.audit.conf.infrastructure.po.UrlTablePO">
        update conf_url_table
        set request_url     = #{requestUrl,jdbcType=VARCHAR},
            data_table_name = #{dataTableName,jdbcType=VARCHAR},
            modify_time     = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.audit.conf.infrastructure.po.UrlTablePO">
        update conf_url_table
        <set>
            <if test="item.requestUrl != null and item.requestUrl != ''">
                request_url = #{item.requestUrl,jdbcType=VARCHAR},
            </if>
            <if test="item.dataTableName != null and item.dataTableName != ''">
                data_table_name = #{item.dataTableName,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update conf_url_table
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="request_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requestUrl,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="data_table_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dataTableName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update conf_url_table
            <set>
                <if test="item.requestUrl != null and item.requestUrl != ''">
                    request_url = #{item.requestUrl,jdbcType=VARCHAR},
                </if>
                <if test="item.dataTableName != null and item.dataTableName != ''">
                    data_table_name = #{item.dataTableName,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from conf_url_table
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from conf_url_table where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
