package com.yhl.scp.audit.conf.convertor;

import com.yhl.scp.audit.conf.domain.entity.UrlTableDO;
import com.yhl.scp.audit.conf.infrastructure.po.UrlTablePO;
import com.yhl.scp.ips.conf.dto.UrlTableDTO;
import com.yhl.scp.ips.conf.vo.UrlTableVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UrlTableConvertor {

    UrlTableConvertor INSTANCE = Mappers.getMapper(UrlTableConvertor.class);

    UrlTableDO dto2Do(UrlTableDTO obj);

    UrlTableDTO do2Dto(UrlTableDO obj);

    List<UrlTableDO> dto2Dos(List<UrlTableDTO> list);

    List<UrlTableDTO> do2Dtos(List<UrlTableDO> list);

    UrlTableVO do2Vo(UrlTableDO obj);

    UrlTableVO po2Vo(UrlTablePO obj);

    List<UrlTableVO> po2Vos(List<UrlTablePO> list);

    UrlTablePO do2Po(UrlTableDO obj);

    UrlTableDO po2Do(UrlTablePO obj);

    UrlTablePO dto2Po(UrlTableDTO obj);

    List<UrlTablePO> dto2Pos(List<UrlTableDTO> obj);

}
