package com.yhl.scp.audit.dataChangeLog.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yhl.scp.audit.dataChangeLog.dao.DataChangeRecordRepository;
import com.yhl.scp.audit.dataChangeLog.service.DataChangeRecordService;
import com.yhl.scp.audit.entity.DataChangeRecordMongoDTO;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.log.dto.DataChangeRecordDTO;
import com.yhl.scp.ips.log.dto.DataChangeRecordQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataChangeRecordServiceImpl implements DataChangeRecordService {

    @Resource
    private DataChangeRecordRepository dataChangeRecordRepository;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 处理数据
     *
     * @param data
     */
    @Override
    public void handle(String data) {
        DataChangeRecordMongoDTO recordDTO = JSONObject.parseObject(data, DataChangeRecordMongoDTO.class);
        dataChangeRecordRepository.save(recordDTO);
    }

    @Override
    public PageImpl<DataChangeRecordDTO> selectByPage(Integer pageNum, Integer pageSize, DataChangeRecordQueryDTO queryDTO) {
        Criteria criteria = new Criteria();
        List<Criteria> criteriaList = Lists.newArrayList();
        if (Objects.nonNull(queryDTO)) {
            if (Objects.nonNull(queryDTO.getBeginTime())) {
                criteriaList.add(Criteria.where("createTime").gte(queryDTO.getBeginTime()));
            }
            if (Objects.nonNull(queryDTO.getEndTime())) {
                criteriaList.add(Criteria.where("createTime").lte(queryDTO.getEndTime()));
            }
            String dataTableName = queryDTO.getDataTableName();
            if (StringUtils.isNotBlank(queryDTO.getRequestUrl())) {
                String tableName = ipsFeign.getTableNameByUrlTable(queryDTO.getRequestUrl());
                if (StringUtils.isNotBlank(tableName)) {
                    dataTableName = tableName.trim().replace("\"", "");
                }
            }
            if (StringUtils.isNotBlank(dataTableName)) {
                criteriaList.add(Criteria.where("dataTableName").is(dataTableName));
            }
            if (StringUtils.isNotBlank(queryDTO.getPrimaryId())) {
                criteriaList.add(Criteria.where("primaryId").is(queryDTO.getPrimaryId()));
            }
            if (StringUtils.isNotBlank(queryDTO.getUserId())) {
                criteriaList.add(Criteria.where("operateUser").is(queryDTO.getUserId()));
            }
        }
        if (CollectionUtils.isNotEmpty(criteriaList)) {
            criteria.andOperator(criteriaList.toArray(new Criteria[0]));
        }
        Query mongoQuery = new Query(criteria);
        long total = mongoTemplate.count(mongoQuery, DataChangeRecordMongoDTO.class);
        mongoQuery.with(Sort.by(Sort.Direction.DESC, "createTime"));
        mongoQuery.skip((long) (pageNum - 1) * pageSize).limit(pageSize);
        List<DataChangeRecordMongoDTO> result = mongoTemplate.find(mongoQuery, DataChangeRecordMongoDTO.class);
        return new PageImpl<>(convertResult(result), PageRequest.of(pageNum - 1, pageSize), total);
    }

    /**
     * 转换结果
     *
     * @param mongoDTOList
     * @return
     */
    private List<DataChangeRecordDTO> convertResult(List<DataChangeRecordMongoDTO> mongoDTOList) {
        List<DataChangeRecordDTO> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(mongoDTOList)) {
            for (DataChangeRecordMongoDTO mongoDTO : mongoDTOList) {
                DataChangeRecordDTO dto = new DataChangeRecordDTO();
                dto.setId(mongoDTO.getId());
                dto.setDataTableName(mongoDTO.getDataTableName());
                dto.setPrimaryId(mongoDTO.getPrimaryId());
                dto.setBeforeData(mongoDTO.getBeforeData());
                dto.setAfterData(mongoDTO.getAfterData());
                dto.setOperateUser(mongoDTO.getOperateUser());
                dto.setOperateType(mongoDTO.getOperateType());
                dto.setCreateTime(mongoDTO.getCreateTime());
                dto.setModifyTime(mongoDTO.getModifyTime());
                dto.setRowIndex(mongoDTO.getRowIndex());
                dto.setVersionValue(mongoDTO.getVersionValue());
                result.add(dto);
            }
        }
        return result;
    }
}
