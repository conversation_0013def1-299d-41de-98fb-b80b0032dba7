<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.audit.rbac.infrastructure.dao.UserMessageDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.audit.rbac.infrastructure.po.UserMessagePO">
        <!--@Table auth_user_message-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="role_id" jdbcType="VARCHAR" property="roleId"/>
        <result column="message_source" jdbcType="VARCHAR" property="messageSource"/>
        <result column="message_type" jdbcType="VARCHAR" property="messageType"/>
        <result column="message_title" jdbcType="VARCHAR" property="messageTitle"/>
        <result column="message_content" jdbcType="VARCHAR" property="messageContent"/>
        <result column="message_link" jdbcType="VARCHAR" property="messageLink"/>
        <result column="message_emergency" jdbcType="VARCHAR" property="messageEmergency"/>
        <result column="read_status" jdbcType="VARCHAR" property="readStatus"/>
        <result column="extra_info" jdbcType="VARCHAR" property="extraInfo"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.rbac.vo.UserMessageVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,user_id,role_id,message_source,message_type,message_title,message_content,message_link,message_emergency,
        read_status,extra_info,remark,enabled,creator,create_time,modify_time,modifier
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.userId != null and params.userId != ''">
                and user_id = #{params.userId,jdbcType=VARCHAR}
            </if>
            <if test="params.roleId != null and params.roleId != ''">
                and role_id = #{params.roleId,jdbcType=VARCHAR}
            </if>
            <if test="params.messageSource != null and params.messageSource != ''">
                and message_source = #{params.messageSource,jdbcType=VARCHAR}
            </if>
            <if test="params.messageType != null and params.messageType != ''">
                and message_type = #{params.messageType,jdbcType=VARCHAR}
            </if>
            <if test="params.messageTitle != null and params.messageTitle != ''">
                and message_title = #{params.messageTitle,jdbcType=VARCHAR}
            </if>
            <if test="params.messageContent != null and params.messageContent != ''">
                and message_content = #{params.messageContent,jdbcType=VARCHAR}
            </if>
            <if test="params.messageLink != null and params.messageLink != ''">
                and message_link = #{params.messageLink,jdbcType=VARCHAR}
            </if>
            <if test="params.messageEmergency != null and params.messageEmergency != ''">
                and message_emergency = #{params.messageEmergency,jdbcType=VARCHAR}
            </if>
            <if test="params.readStatus != null and params.readStatus != ''">
                and read_status = #{params.readStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.extraInfo != null and params.extraInfo != ''">
                and extra_info = #{params.extraInfo,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_user_message
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_user_message
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_auth_user_message
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_user_message
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_auth_user_message
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增（带主键） -->
    <insert id="insert" parameterType="com.yhl.scp.audit.rbac.infrastructure.po.UserMessagePO">
        insert into auth_user_message(id,
                                      user_id,
                                      role_id,
                                      message_source,
                                      message_type,
                                      message_title,
                                      message_content,
                                      message_link,
                                      message_emergency,
                                      read_status,
                                      extra_info,
                                      remark,
                                      enabled,
                                      creator,
                                      create_time,
                                      modifier,
                                      modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{userId,jdbcType=VARCHAR},
                #{roleId,jdbcType=VARCHAR},
                #{messageSource,jdbcType=VARCHAR},
                #{messageType,jdbcType=VARCHAR},
                #{messageTitle,jdbcType=VARCHAR},
                #{messageContent,jdbcType=VARCHAR},
                #{messageLink,jdbcType=VARCHAR},
                #{messageEmergency,jdbcType=VARCHAR},
                #{readStatus,jdbcType=VARCHAR},
                #{extraInfo,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 批量新增（带主键） -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into auth_user_message(
        id,
        user_id,
        role_id,
        message_source,
        message_type,
        message_title,
        message_content,
        message_link,
        message_emergency,
        read_status,
        extra_info,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.userId,jdbcType=VARCHAR},
            #{entity.roleId,jdbcType=VARCHAR},
            #{entity.messageSource,jdbcType=VARCHAR},
            #{entity.messageType,jdbcType=VARCHAR},
            #{entity.messageTitle,jdbcType=VARCHAR},
            #{entity.messageContent,jdbcType=VARCHAR},
            #{entity.messageLink,jdbcType=VARCHAR},
            #{entity.messageEmergency,jdbcType=VARCHAR},
            #{entity.readStatus,jdbcType=VARCHAR},
            #{entity.extraInfo,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.audit.rbac.infrastructure.po.UserMessagePO">
        update auth_user_message
        set user_id           = #{userId,jdbcType=VARCHAR},
            role_id           = #{roleId,jdbcType=VARCHAR},
            message_source    = #{messageSource,jdbcType=VARCHAR},
            message_type      = #{messageType,jdbcType=VARCHAR},
            message_title     = #{messageTitle,jdbcType=VARCHAR},
            message_content   = #{messageContent,jdbcType=VARCHAR},
            message_link      = #{messageLink,jdbcType=VARCHAR},
            message_emergency = #{messageEmergency,jdbcType=VARCHAR},
            read_status       = #{readStatus,jdbcType=VARCHAR},
            extra_info        = #{extraInfo,jdbcType=VARCHAR},
            remark            = #{remark,jdbcType=VARCHAR},
            enabled           = #{enabled,jdbcType=VARCHAR},
            modifier          = #{modifier,jdbcType=VARCHAR},
            modify_time       = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.audit.rbac.infrastructure.po.UserMessagePO">
        update auth_user_message
        <set>
            <if test="item.userId != null and item.userId != ''">
                user_id = #{item.userId,jdbcType=VARCHAR},
            </if>
            <if test="item.roleId != null and item.roleId != ''">
                role_id = #{item.roleId,jdbcType=VARCHAR},
            </if>
            <if test="item.messageSource != null and item.messageSource != ''">
                message_source = #{item.messageSource,jdbcType=VARCHAR},
            </if>
            <if test="item.messageType != null and item.messageType != ''">
                message_type = #{item.messageType,jdbcType=VARCHAR},
            </if>
            <if test="item.messageTitle != null and item.messageTitle != ''">
                message_title = #{item.messageTitle,jdbcType=VARCHAR},
            </if>
            <if test="item.messageContent != null and item.messageContent != ''">
                message_content = #{item.messageContent,jdbcType=VARCHAR},
            </if>
            <if test="item.messageLink != null and item.messageLink != ''">
                message_link = #{item.messageLink,jdbcType=VARCHAR},
            </if>
            <if test="item.messageEmergency != null and item.messageEmergency != ''">
                message_emergency = #{item.messageEmergency,jdbcType=VARCHAR},
            </if>
            <if test="item.readStatus != null and item.readStatus != ''">
                read_status = #{item.readStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.extraInfo != null and item.extraInfo != ''">
                extra_info = #{item.extraInfo,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update auth_user_message
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.userId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="role_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.roleId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageSource,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_title = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageTitle,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageContent,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_link = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageLink,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_emergency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageEmergency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="read_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.readStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="extra_info = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.extraInfo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update auth_user_message
            <set>
                <if test="item.userId != null and item.userId != ''">
                    user_id = #{item.userId,jdbcType=VARCHAR},
                </if>
                <if test="item.roleId != null and item.roleId != ''">
                    role_id = #{item.roleId,jdbcType=VARCHAR},
                </if>
                <if test="item.messageSource != null and item.messageSource != ''">
                    message_source = #{item.messageSource,jdbcType=VARCHAR},
                </if>
                <if test="item.messageType != null and item.messageType != ''">
                    message_type = #{item.messageType,jdbcType=VARCHAR},
                </if>
                <if test="item.messageTitle != null and item.messageTitle != ''">
                    message_title = #{item.messageTitle,jdbcType=VARCHAR},
                </if>
                <if test="item.messageContent != null and item.messageContent != ''">
                    message_content = #{item.messageContent,jdbcType=VARCHAR},
                </if>
                <if test="item.messageLink != null and item.messageLink != ''">
                    message_link = #{item.messageLink,jdbcType=VARCHAR},
                </if>
                <if test="item.messageEmergency != null and item.messageEmergency != ''">
                    message_emergency = #{item.messageEmergency,jdbcType=VARCHAR},
                </if>
                <if test="item.readStatus != null and item.readStatus != ''">
                    read_status = #{item.readStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.extraInfo != null and item.extraInfo != ''">
                    extra_info = #{item.extraInfo,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from auth_user_message
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from auth_user_message where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
