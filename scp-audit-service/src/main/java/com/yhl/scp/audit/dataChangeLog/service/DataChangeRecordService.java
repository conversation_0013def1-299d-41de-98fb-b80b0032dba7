package com.yhl.scp.audit.dataChangeLog.service;

import com.yhl.scp.ips.log.dto.DataChangeRecordDTO;
import com.yhl.scp.ips.log.dto.DataChangeRecordQueryDTO;
import org.springframework.data.domain.PageImpl;

/**
 * 行数据变更记录服务
 *
 * <AUTHOR>
 */
public interface DataChangeRecordService {

    void handle(String data);

    PageImpl<DataChangeRecordDTO> selectByPage(Integer pageNum, Integer pageSize, DataChangeRecordQueryDTO queryDTO);
}
