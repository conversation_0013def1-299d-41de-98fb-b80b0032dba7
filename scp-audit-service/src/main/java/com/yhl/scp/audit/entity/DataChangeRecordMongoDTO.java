package com.yhl.scp.audit.entity;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel(value = "log_data_change_record DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Document("log_data_change_record")
public class DataChangeRecordMongoDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -31349948421167238L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @Id
    private String id;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String dataTableName;
    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    private String primaryId;
    /**
     * 修改前数据
     */
    @ApiModelProperty(value = "修改前数据")
    private String beforeData;
    /**
     * 修改后数据
     */
    @ApiModelProperty(value = "修改后数据")
    private String afterData;
    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operateUser;
    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operateType;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date modifyTime;
}
