package com.yhl.scp.audit.rbac.domain.factory;

import com.yhl.scp.audit.rbac.domain.entity.UserMessageDO;
import com.yhl.scp.audit.rbac.infrastructure.dao.UserMessageDao;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>UserMessageFactory</code>
 * <p>
 * UserMessageFactory
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 09:47:49
 */
@Component
public class UserMessageFactory {

    @Resource
    private UserMessageDao userMessageDao;

    UserMessageDO create(UserMessageDTO dto) {
        // TODO
        return null;
    }

}
