package com.yhl.scp.audit.rbac.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>UserMessageDO</code>
 * <p>
 * UserMessageDO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 09:47:49
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserMessageDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -99816440759317102L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 角色id
     */
    private String roleId;
    /**
     * 消息来源
     */
    private String messageSource;
    /**
     * 消息类型
     */
    private String messageType;
    /**
     * 消息标题
     */
    private String messageTitle;
    /**
     * 消息内容
     */
    private String messageContent;
    /**
     * 消息链接
     */
    private String messageLink;
    /**
     * 消息紧急程度
     */
    private String messageEmergency;
    /**
     * 是否已读
     */
    private String readStatus;
    /**
     * 额外信息
     */
    private String extraInfo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否启用
     */
    private String enabled;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Date modifyTime;

}
