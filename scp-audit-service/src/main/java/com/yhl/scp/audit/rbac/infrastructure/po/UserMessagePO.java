package com.yhl.scp.audit.rbac.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;

/**
 * <code>UserMessagePO</code>
 * <p>
 * UserMessagePO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 09:47:49
 */
public class UserMessagePO extends BasePO implements Serializable {

    private static final long serialVersionUID = -23577862518673551L;

    /**
     * 用户id
     */
    private String userId;
    /**
     * 角色id
     */
    private String roleId;
    /**
     * 消息来源
     */
    private String messageSource;
    /**
     * 消息类型
     */
    private String messageType;
    /**
     * 消息标题
     */
    private String messageTitle;
    /**
     * 消息内容
     */
    private String messageContent;
    /**
     * 消息链接
     */
    private String messageLink;
    /**
     * 消息紧急程度
     */
    private String messageEmergency;
    /**
     * 是否已读
     */
    private String readStatus;
    /**
     * 额外信息
     */
    private String extraInfo;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getMessageSource() {
        return messageSource;
    }

    public void setMessageSource(String messageSource) {
        this.messageSource = messageSource;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getMessageLink() {
        return messageLink;
    }

    public void setMessageLink(String messageLink) {
        this.messageLink = messageLink;
    }

    public String getMessageEmergency() {
        return messageEmergency;
    }

    public void setMessageEmergency(String messageEmergency) {
        this.messageEmergency = messageEmergency;
    }

    public String getReadStatus() {
        return readStatus;
    }

    public void setReadStatus(String readStatus) {
        this.readStatus = readStatus;
    }

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }

}
