package com.yhl.scp.audit.feign.controller;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.audit.dataChangeLog.service.DataChangeRecordService;
import com.yhl.scp.audit.feign.AuditFeign;
import com.yhl.scp.audit.requestLog.service.RequestLogService;
import com.yhl.scp.ips.log.dto.DataChangeRecordDTO;
import com.yhl.scp.ips.log.dto.DataChangeRecordQueryDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogQueryDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <code>DcpFeignController</code>
 * <p>
 * DcpFeign控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-05-27 08:55:14
 */
@Slf4j
@Api(tags = "Audit-Feign")
@RestController
public class AuditFeignController implements AuditFeign {

    @Resource
    private DataChangeRecordService dataChangeRecordService;

    @Resource
    private RequestLogService requestLogService;

    @Override
    public BaseResponse<Page<DataChangeRecordDTO>> selectDataChangeRecordsByPage(int pageNum, int pageSize, DataChangeRecordQueryDTO params) {
        PageImpl<DataChangeRecordDTO> changeRecords = dataChangeRecordService.selectByPage(pageNum, pageSize, params);
        return BaseResponse.success(changeRecords);
    }

    @Override
    public BaseResponse<Page<RequestLogDTO>> selectRequestLogsByPage(int pageNum, int pageSize, RequestLogQueryDTO queryParams) {
        PageImpl<RequestLogDTO> requestLogDTOList = requestLogService.selectByPage(pageNum, pageSize, queryParams);
        return BaseResponse.success(requestLogDTOList);
    }
}