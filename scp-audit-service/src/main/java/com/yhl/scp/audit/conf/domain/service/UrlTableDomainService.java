package com.yhl.scp.audit.conf.domain.service;

import com.yhl.scp.audit.conf.domain.entity.UrlTableDO;
import com.yhl.scp.audit.conf.infrastructure.dao.UrlTableDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class UrlTableDomainService {

    @Resource
    private UrlTableDao urlTableDao;

    /**
     * 数据校验
     *
     * @param urlTableDO 领域对象
     */
    public void validation(UrlTableDO urlTableDO) {
        checkNotNull(urlTableDO);
        checkUniqueCode(urlTableDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param urlTableDO 领域对象
     */
    private void checkNotNull(UrlTableDO urlTableDO) {

    }

    /**
     * 唯一性校验
     *
     * @param urlTableDO 领域对象
     */
    private void checkUniqueCode(UrlTableDO urlTableDO) {

    }

}
