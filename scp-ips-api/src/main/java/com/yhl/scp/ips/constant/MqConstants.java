package com.yhl.scp.ips.constant;


/**
 * <AUTHOR>
 */
public class MqConstants {

    public static final String BPIM_EVENT_EXCHANGE = "bpim_event_exchange";

    public static final String DATA_RECORD_CHANGE_ROUTING_KEY = "routing.data.record.change";

    public static final String DATA_RECORD_CHANGE_QUEUE = "queue.data.record.change";


    public static final String URL_TABLE_ROUTING_KEY = "routing.url.table";

    public static final String URL_TABLE_CHANGE_QUEUE = "queue.url.table";


    public static final String REQUEST_LOG_ROUTING_KEY = "routing.request.log";

    public static final String REQUEST_LOG_QUEUE = "queue.request.log";


    public static final String USER_MESSAGE_KEY = "routing.user.message";

    public static final String USER_MESSAGE_QUEUE = "queue.user.message";


    public static final String COMMON_MESSAGE_KEY = "routing.common.message";

    public static final String COMMON_MESSAGE_QUEUE = "queue.common.message";

}
