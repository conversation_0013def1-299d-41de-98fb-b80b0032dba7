package com.yhl.scp.dfp.stock.domain.factory;

import com.yhl.scp.dfp.stock.domain.entity.RealtimeInventoryBatchDetailDO;
import com.yhl.scp.dfp.stock.dto.RealtimeInventoryBatchDetailDTO;
import com.yhl.scp.dfp.stock.infrastructure.dao.RealtimeInventoryBatchDetailDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>RealtimeInventoryBatchDetailFactory</code>
 * <p>
 * 实时库存批次明细领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:52:25
 */
@Component
public class RealtimeInventoryBatchDetailFactory {

    @Resource
    private RealtimeInventoryBatchDetailDao realtimeInventoryBatchDetailDao;

    RealtimeInventoryBatchDetailDO create(RealtimeInventoryBatchDetailDTO dto) {
        // TODO
        return null;
    }

}
