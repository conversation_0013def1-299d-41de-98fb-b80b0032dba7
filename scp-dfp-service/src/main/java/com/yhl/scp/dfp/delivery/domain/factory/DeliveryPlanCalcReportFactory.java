package com.yhl.scp.dfp.delivery.domain.factory;

import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanCalcReportDO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanCalcReportDTO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanCalcReportDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>DeliveryPlanCalcReportFactory</code>
 * <p>
 * 发货计划计算数据汇总报表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:00:52
 */
@Component
public class DeliveryPlanCalcReportFactory {

    @Resource
    private DeliveryPlanCalcReportDao deliveryPlanCalcReportDao;

    DeliveryPlanCalcReportDO create(DeliveryPlanCalcReportDTO dto) {
        // TODO
        return null;
    }

}
