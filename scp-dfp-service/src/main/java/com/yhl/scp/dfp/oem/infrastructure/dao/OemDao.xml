<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.oem.infrastructure.dao.OemDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.oem.infrastructure.po.OemPO">
        <!--@Table mds_oem-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="sale_org_id" jdbcType="VARCHAR" property="saleOrgId"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="location_code" jdbcType="VARCHAR" property="locationCode"/>
        <result column="location_area1" jdbcType="VARCHAR" property="locationArea1"/>
        <result column="location_area2" jdbcType="VARCHAR" property="locationArea2"/>
        <result column="location_area3" jdbcType="VARCHAR" property="locationArea3"/>
        <result column="location_area4" jdbcType="VARCHAR" property="locationArea4"/>
        <result column="payment_term" jdbcType="VARCHAR" property="paymentTerm"/>
        <result column="transit_clause" jdbcType="VARCHAR" property="transitClause"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="market_type" jdbcType="VARCHAR" property="marketType"/>
        <result column="edi_location" jdbcType="VARCHAR" property="ediLocation"/>
        <result column="plant_code" jdbcType="VARCHAR" property="plantCode"/>
        <result column="target_stock_location" jdbcType="VARCHAR" property="targetStockLocation"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="ebs_customer_id" jdbcType="VARCHAR" property="ebsCustomerId"/>
        <result column="ebs_site_id" jdbcType="VARCHAR" property="ebsSiteId"/>
        <result column="ship_to_site_use_id" jdbcType="VARCHAR" property="shipToSiteUseId"/>
        <result column="site_country" jdbcType="VARCHAR" property="siteCountry"/>
        <result column="edi_flag" jdbcType="VARCHAR" property="ediFlag"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="tally_order_mode" jdbcType="VARCHAR" property="tallyOrderMode"/>
        <result column="pick_up_type" jdbcType="VARCHAR" property="pickUpType"/>
        <result column="erp_customer_address_id" jdbcType="VARCHAR" property="erpCustomerAddressId"/>
        <result column="transfer_target_stock_location" jdbcType="VARCHAR" property="transferTargetStockLocation"/>
        <result column="customer_abbreviation" jdbcType="VARCHAR" property="customerAbbreviation"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.oem.vo.OemVO">
        <result column="sale_org_code" jdbcType="VARCHAR" property="saleOrgCode"/>
        <result column="sale_org_name" jdbcType="VARCHAR" property="saleOrgName"/>
        <result column="erp_site_id" jdbcType="VARCHAR" property="erpSiteId"/>
        <result column="erp_site_code" jdbcType="VARCHAR" property="erpSiteCode"/>
        <result column="erp_site_address" jdbcType="VARCHAR" property="erpSiteAddress"/>
        <result column="erp_ship_to_site_use_id" jdbcType="VARCHAR" property="erpShipToSiteUseId"/>
        <result column="erp_customer_id" jdbcType="VARCHAR" property="erpCustomerId"/>
        <result column="erp_customer_code" jdbcType="VARCHAR" property="erpCustomerCode"/>
        <result column="erp_edi_location" jdbcType="VARCHAR" property="erpEdiLocation"/>
        <result column="erp_plant_code" jdbcType="VARCHAR" property="erpPlantCode"/>
    </resultMap>
    <resultMap id="EdiVOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.oem.vo.OemVO">
        <result column="product_edi_flag" jdbcType="VARCHAR" property="productEdiFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,sale_org_id,oem_code,oem_name,customer_code,customer_name,location_code,location_area1,location_area2,
        location_area3,location_area4,payment_term,transit_clause,business_type,market_type,target_stock_location,
        remark,enabled,creator,create_time,modifier,modify_time,version_value,edi_location,plant_code,ebs_customer_id,
        ebs_site_id,ship_to_site_use_id,site_country,last_update_time,edi_flag,tally_order_mode, pick_up_type,
        erp_customer_address_id, transfer_target_stock_location, customer_abbreviation
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,sale_org_code,sale_org_name,erp_site_code,erp_site_address,
        erp_ship_to_site_use_id,erp_customer_id,erp_customer_code,erp_site_id,erp_edi_location,erp_plant_code
    </sql>
    <sql id="EDI_VO_Column_List">
        <include refid="Base_Column_List"/>,product_edi_flag
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.saleOrgId != null and params.saleOrgId != ''">
                and sale_org_id = #{params.saleOrgId,jdbcType=VARCHAR}
            </if>
            <if test="params.saleOrgCode != null and params.saleOrgCode != ''">
                and sale_org_code = #{params.saleOrgCode,jdbcType=VARCHAR}
            </if>
            <if test="params.saleOrgName != null and params.saleOrgName != ''">
                and sale_org_name = #{params.saleOrgName,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCodes != null and params.oemCodes.size() > 0">
                and oem_code in
                <foreach collection="params.oemCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.oemName != null and params.oemName != ''">
                and oem_name = #{params.oemName,jdbcType=VARCHAR}
            </if>
            <if test="params.customerCode != null and params.customerCode != ''">
                and customer_code = #{params.customerCode,jdbcType=VARCHAR}
            </if>
            <if test="params.customerName != null and params.customerName != ''">
                and customer_name = #{params.customerName,jdbcType=VARCHAR}
            </if>
            <if test="params.locationCode != null and params.locationCode != ''">
                and location_code = #{params.locationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.locationArea1 != null and params.locationArea1 != ''">
                and location_area1 = #{params.locationArea1,jdbcType=VARCHAR}
            </if>
            <if test="params.locationArea2 != null and params.locationArea2 != ''">
                and location_area2 = #{params.locationArea2,jdbcType=VARCHAR}
            </if>
            <if test="params.locationArea3 != null and params.locationArea3 != ''">
                and location_area3 = #{params.locationArea3,jdbcType=VARCHAR}
            </if>
            <if test="params.locationArea4 != null and params.locationArea4 != ''">
                and location_area4 = #{params.locationArea4,jdbcType=VARCHAR}
            </if>
            <if test="params.paymentTerm != null and params.paymentTerm != ''">
                and payment_term = #{params.paymentTerm,jdbcType=VARCHAR}
            </if>
            <if test="params.transitClause != null and params.transitClause != ''">
                and transit_clause = #{params.transitClause,jdbcType=VARCHAR}
            </if>
            <if test="params.businessType != null and params.businessType != ''">
                and business_type = #{params.businessType,jdbcType=VARCHAR}
            </if>
            <if test="params.marketType != null and params.marketType != ''">
                and market_type = #{params.marketType,jdbcType=VARCHAR}
            </if>
            <if test="params.targetStockLocation != null and params.targetStockLocation != ''">
                and target_stock_location = #{params.targetStockLocation,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.ediLocation != null and params.ediLocation != ''">
                and edi_location = #{params.ediLocation,jdbcType=VARCHAR}
            </if>
            <if test="params.plantCode != null and params.plantCode != ''">
                and plant_code = #{params.plantCode,jdbcType=VARCHAR}
            </if>
            <if test="params.ebsCustomerId != null and params.ebsCustomerId != ''">
                and ebs_customer_id = #{params.ebsCustomerId,jdbcType=VARCHAR}
            </if>
            <if test="params.ebsSiteId != null and params.ebsSiteId != ''">
                and ebs_site_id = #{params.ebsSiteId,jdbcType=VARCHAR}
            </if>
            <if test="params.shipToSiteUseId != null and params.shipToSiteUseId != ''">
                and ship_to_site_use_id = #{params.shipToSiteUseId,jdbcType=VARCHAR}
            </if>
            <if test="params.siteCountry != null and params.siteCountry != ''">
                and site_country = #{params.siteCountry,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateTime != null">
                and last_update_time = #{params.lastUpdateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.ediFlag != null and params.ediFlag != ''">
                and edi_flag = #{params.ediFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCodeList != null and params.oemCodeList.size() > 0">
                and oem_code in
                <foreach collection="params.oemCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.targetStockLocations != null and params.targetStockLocations.size() > 0">
                and target_stock_location in
                <foreach collection="params.targetStockLocations" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.tallyOrderMode != null and params.tallyOrderMode != ''">
                and tally_order_mode = #{params.tallyOrderMode,jdbcType=VARCHAR}
            </if>
            <if test="params.pickUpType != null and params.pickUpType != ''">
                and pick_up_type = #{params.pickUpType,jdbcType=VARCHAR}
            </if>
            <if test="params.erpCustomerAddressId != null and params.erpCustomerAddressId != ''">
                and erp_customer_address_id = #{params.erpCustomerAddressId,jdbcType=VARCHAR}
            </if>
            <if test="params.erpShipToSiteUseId != null and params.erpShipToSiteUseId != ''">
                and erp_ship_to_site_use_id = #{params.erpShipToSiteUseId,jdbcType=VARCHAR}
            </if>
            <if test="params.erpCustomerId != null and params.erpCustomerId != ''">
                and erp_customer_id = #{params.erpCustomerId,jdbcType=VARCHAR}
            </if>
            <if test="params.erpCustomerCode != null and params.erpCustomerCode != ''">
                and erp_customer_code = #{params.erpCustomerCode,jdbcType=VARCHAR}
            </if>
            <if test="params.erpCustomerCodes != null and params.erpCustomerCodes.size() > 0">
                and erp_customer_code in
                <foreach collection="params.erpCustomerCodes" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.erpSiteId != null and params.erpSiteId != ''">
                and erp_site_id = #{params.erpSiteId,jdbcType=VARCHAR}
            </if>
            <if test="params.erpEdiLocation != null and params.erpEdiLocation != ''">
                and erp_edi_location = #{params.erpEdiLocation,jdbcType=VARCHAR}
            </if>
            <if test="params.erpPlantCode != null and params.erpPlantCode != ''">
                and erp_plant_code = #{params.erpPlantCode,jdbcType=VARCHAR}
            </if>
            <if test="params.transferTargetStockLocation != null and params.transferTargetStockLocation != ''">
                and transfer_target_stock_location = #{params.transferTargetStockLocation,jdbcType=VARCHAR}
            </if>
            <if test="params.customerAbbreviation != null and params.customerAbbreviation != ''">
                and customer_abbreviation = #{params.customerAbbreviation,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_oem
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_oem
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询-权限 -->
    <select id="selectVOByConditionParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectByOemCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem
        where oem_code = #{oemCode,jdbcType=VARCHAR}
        order by modify_time limit 1
    </select>
    <select id="selectByOemCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem
        where oem_code in
        <foreach collection="oemCodes" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectPageByVersionIdAndCondition" parameterType="com.yhl.scp.dfp.release.dto.ReleaseOemDTO"
            resultType="com.yhl.scp.dfp.release.vo.ReleaseOemVO">
        select oem_code as oemCode, oem_name as oemName, risk_level as oemRiskLevel
        from v_fdp_release_oem_risk_level
        where 1 = 1
        and parent_version_id = #{versionId,jdbcType=VARCHAR}
        and oem_code is not null
        <if test="oemCodes != null and oemCodes.size() > 0">
            and oem_code in
            <foreach collection="oemCodes" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="oemNames != null and oemNames.size() > 0">
            and oem_name in
            <foreach collection="oemNames" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="oemRiskLevels != null and oemRiskLevels.size() > 0">
            and risk_level in
            <foreach collection="oemRiskLevels" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectByCustomerCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem
        where customer_code in
        <foreach collection="customerCodes" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getOemName" resultType="com.yhl.scp.dfp.oem.infrastructure.po.OemPO">
        select * from mds_oem
        where oem_code = #{oemCode,jdbcType=VARCHAR}
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.oem.infrastructure.po.OemPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_oem(
        id,
        sale_org_id,
        oem_code,
        oem_name,
        customer_code,
        customer_name,
        location_code,
        location_area1,
        location_area2,
        location_area3,
        location_area4,
        payment_term,
        transit_clause,
        business_type,
        market_type,
        target_stock_location,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        edi_location,
        plant_code,
        ebs_customer_id,
        ebs_site_id,
        ship_to_site_use_id,
        site_country,
        last_update_time,
        edi_flag,
        version_value,
        tally_order_mode,
        pick_up_type,
        erp_customer_address_id,
        transfer_target_stock_location,
        customer_abbreviation)
        values (
        #{id,jdbcType=VARCHAR},
        #{saleOrgId,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{oemName,jdbcType=VARCHAR},
        #{customerCode,jdbcType=VARCHAR},
        #{customerName,jdbcType=VARCHAR},
        #{locationCode,jdbcType=VARCHAR},
        #{locationArea1,jdbcType=VARCHAR},
        #{locationArea2,jdbcType=VARCHAR},
        #{locationArea3,jdbcType=VARCHAR},
        #{locationArea4,jdbcType=VARCHAR},
        #{paymentTerm,jdbcType=VARCHAR},
        #{transitClause,jdbcType=VARCHAR},
        #{businessType,jdbcType=VARCHAR},
        #{marketType,jdbcType=VARCHAR},
        #{targetStockLocation,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{ediLocation,jdbcType=VARCHAR},
        #{plantCode,jdbcType=VARCHAR},
        #{ebsCustomerId,jdbcType=VARCHAR},
        #{ebsSiteId,jdbcType=VARCHAR},
        #{shipToSiteUseId,jdbcType=VARCHAR},
        #{siteCountry,jdbcType=VARCHAR},
        #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{ediFlag,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{tallyOrderMode,jdbcType=VARCHAR},
        #{pickUpType,jdbcType=VARCHAR},
        #{erpCustomerAddressId,jdbcType=VARCHAR},
        #{transferTargetStockLocation,jdbcType=VARCHAR},
        #{customerAbbreviation,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.oem.infrastructure.po.OemPO">
        insert into mds_oem(id,
                            sale_org_id,
                            oem_code,
                            oem_name,
                            customer_code,
                            customer_name,
                            location_code,
                            location_area1,
                            location_area2,
                            location_area3,
                            location_area4,
                            payment_term,
                            transit_clause,
                            business_type,
                            market_type,
                            target_stock_location,
                            remark,
                            enabled,
                            creator,
                            create_time,
                            modifier,
                            modify_time,
                            edi_location,
                            plant_code,
                            ebs_customer_id,
                            ebs_site_id,
                            ship_to_site_use_id,
                            site_country,
                            last_update_time,
                            edi_flag,
                            version_value,
        					tally_order_mode,
        					pick_up_type,
					        erp_customer_address_id,
        					transfer_target_stock_location,
        					customer_abbreviation)
        values (#{id,jdbcType=VARCHAR},
                #{saleOrgId,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{oemName,jdbcType=VARCHAR},
                #{customerCode,jdbcType=VARCHAR},
                #{customerName,jdbcType=VARCHAR},
                #{locationCode,jdbcType=VARCHAR},
                #{locationArea1,jdbcType=VARCHAR},
                #{locationArea2,jdbcType=VARCHAR},
                #{locationArea3,jdbcType=VARCHAR},
                #{locationArea4,jdbcType=VARCHAR},
                #{paymentTerm,jdbcType=VARCHAR},
                #{transitClause,jdbcType=VARCHAR},
                #{businessType,jdbcType=VARCHAR},
                #{marketType,jdbcType=VARCHAR},
                #{targetStockLocation,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{ediLocation,jdbcType=VARCHAR},
                #{plantCode,jdbcType=VARCHAR},
                #{ebsCustomerId,jdbcType=VARCHAR},
                #{ebsSiteId,jdbcType=VARCHAR},
                #{shipToSiteUseId,jdbcType=VARCHAR},
                #{siteCountry,jdbcType=VARCHAR},
                #{lastUpdateTime,jdbcType=TIMESTAMP},
                #{ediFlag,jdbcType=VARCHAR},
                #{versionValue,jdbcType=INTEGER},
                #{tallyOrderMode,jdbcType=VARCHAR},
        		#{pickUpType,jdbcType=VARCHAR},
		        #{erpCustomerAddressId,jdbcType=VARCHAR},
        		#{transferTargetStockLocation,jdbcType=VARCHAR},
        		#{customerAbbreviation,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_oem(
        id,
        sale_org_id,
        oem_code,
        oem_name,
        customer_code,
        customer_name,
        location_code,
        location_area1,
        location_area2,
        location_area3,
        location_area4,
        payment_term,
        transit_clause,
        business_type,
        market_type,
        target_stock_location,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        edi_location,
        plant_code,
        ebs_customer_id,
        ebs_site_id,
        ship_to_site_use_id,
        site_country,
        last_update_time,
        edi_flag,
        version_value,
        tally_order_mode,
        pick_up_type,
        erp_customer_address_id,
        transfer_target_stock_location,
        customer_abbreviation)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.saleOrgId,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.oemName,jdbcType=VARCHAR},
            #{entity.customerCode,jdbcType=VARCHAR},
            #{entity.customerName,jdbcType=VARCHAR},
            #{entity.locationCode,jdbcType=VARCHAR},
            #{entity.locationArea1,jdbcType=VARCHAR},
            #{entity.locationArea2,jdbcType=VARCHAR},
            #{entity.locationArea3,jdbcType=VARCHAR},
            #{entity.locationArea4,jdbcType=VARCHAR},
            #{entity.paymentTerm,jdbcType=VARCHAR},
            #{entity.transitClause,jdbcType=VARCHAR},
            #{entity.businessType,jdbcType=VARCHAR},
            #{entity.marketType,jdbcType=VARCHAR},
            #{entity.targetStockLocation,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.ediLocation,jdbcType=VARCHAR},
            #{entity.plantCode,jdbcType=VARCHAR},
            #{entity.ebsCustomerId,jdbcType=VARCHAR},
            #{entity.ebsSiteId,jdbcType=VARCHAR},
            #{entity.shipToSiteUseId,jdbcType=VARCHAR},
            #{entity.siteCountry,jdbcType=VARCHAR},
            #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
            #{entity.ediFlag,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.tallyOrderMode,jdbcType=VARCHAR},
        	#{entity.pickUpType,jdbcType=VARCHAR},
	        #{entity.erpCustomerAddressId,jdbcType=VARCHAR},
        	#{entity.transferTargetStockLocation,jdbcType=VARCHAR},
        	#{entity.customerAbbreviation,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_oem(
        id,
        sale_org_id,
        oem_code,
        oem_name,
        customer_code,
        customer_name,
        location_code,
        location_area1,
        location_area2,
        location_area3,
        location_area4,
        payment_term,
        transit_clause,
        business_type,
        market_type,
        target_stock_location,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        edi_location,
        plant_code,
        ebs_customer_id,
        ebs_site_id,
        ship_to_site_use_id,
        site_country,
        last_update_time,
        edi_flag,
        version_value,
        tally_order_mode,
        pick_up_type,
        erp_customer_address_id,
        transfer_target_stock_location,
        customer_abbreviation)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
            #{entity.saleOrgId,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.oemName,jdbcType=VARCHAR},
            #{entity.customerCode,jdbcType=VARCHAR},
            #{entity.customerName,jdbcType=VARCHAR},
            #{entity.locationCode,jdbcType=VARCHAR},
            #{entity.locationArea1,jdbcType=VARCHAR},
            #{entity.locationArea2,jdbcType=VARCHAR},
            #{entity.locationArea3,jdbcType=VARCHAR},
            #{entity.locationArea4,jdbcType=VARCHAR},
            #{entity.paymentTerm,jdbcType=VARCHAR},
            #{entity.transitClause,jdbcType=VARCHAR},
            #{entity.businessType,jdbcType=VARCHAR},
            #{entity.marketType,jdbcType=VARCHAR},
            #{entity.targetStockLocation,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.ediLocation,jdbcType=VARCHAR},
            #{entity.plantCode,jdbcType=VARCHAR},
            #{entity.ebsCustomerId,jdbcType=VARCHAR},
            #{entity.ebsSiteId,jdbcType=VARCHAR},
            #{entity.shipToSiteUseId,jdbcType=VARCHAR},
            #{entity.siteCountry,jdbcType=VARCHAR},
            #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
            #{entity.ediFlag,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.tallyOrderMode,jdbcType=VARCHAR},
       		#{entity.pickUpType,jdbcType=VARCHAR},
	        #{entity.erpCustomerAddressId,jdbcType=VARCHAR},
        	#{entity.transferTargetStockLocation,jdbcType=VARCHAR},
        	#{entity.customerAbbreviation,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.oem.infrastructure.po.OemPO">
        update mds_oem
        set sale_org_id           = #{saleOrgId,jdbcType=VARCHAR},
            oem_code              = #{oemCode,jdbcType=VARCHAR},
            oem_name              = #{oemName,jdbcType=VARCHAR},
            customer_code         = #{customerCode,jdbcType=VARCHAR},
            customer_name         = #{customerName,jdbcType=VARCHAR},
            location_code         = #{locationCode,jdbcType=VARCHAR},
            location_area1        = #{locationArea1,jdbcType=VARCHAR},
            location_area2        = #{locationArea2,jdbcType=VARCHAR},
            location_area3        = #{locationArea3,jdbcType=VARCHAR},
            location_area4        = #{locationArea4,jdbcType=VARCHAR},
            payment_term          = #{paymentTerm,jdbcType=VARCHAR},
            transit_clause        = #{transitClause,jdbcType=VARCHAR},
            business_type         = #{businessType,jdbcType=VARCHAR},
            market_type           = #{marketType,jdbcType=VARCHAR},
            target_stock_location = #{targetStockLocation,jdbcType=VARCHAR},
            remark                = #{remark,jdbcType=VARCHAR},
            enabled               = #{enabled,jdbcType=VARCHAR},
            modifier              = #{modifier,jdbcType=VARCHAR},
            modify_time           = #{modifyTime,jdbcType=TIMESTAMP},
            edi_location          = #{ediLocation,jdbcType=VARCHAR},
            plant_code            = #{plantCode,jdbcType=VARCHAR},
            ebs_customer_id       = #{ebsCustomerId,jdbcType=VARCHAR},
            ebs_site_id           = #{ebsSiteId,jdbcType=VARCHAR},
            ship_to_site_use_id   = #{shipToSiteUseId,jdbcType=VARCHAR},
            site_country          = #{siteCountry,jdbcType=VARCHAR},
            last_update_time      = #{lastUpdateTime,jdbcType=TIMESTAMP},
            edi_flag              = #{ediFlag,jdbcType=VARCHAR},
            tally_order_mode      = #{tallyOrderMode,jdbcType=VARCHAR},
            pick_up_type      	  = #{pickUpType,jdbcType=VARCHAR},
            erp_customer_address_id      	  = #{erpCustomerAddressId,jdbcType=VARCHAR},
            transfer_target_stock_location = #{transferTargetStockLocation,jdbcType=VARCHAR},
            customer_abbreviation = #{customerAbbreviation,jdbcType=VARCHAR},
            version_value         = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.oem.infrastructure.po.OemPO">
        update mds_oem
        <set>
            <if test="item.saleOrgId != null and item.saleOrgId != ''">
                sale_org_id = #{item.saleOrgId,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oemName != null and item.oemName != ''">
                oem_name = #{item.oemName,jdbcType=VARCHAR},
            </if>
            <if test="item.customerCode != null and item.customerCode != ''">
                customer_code = #{item.customerCode,jdbcType=VARCHAR},
            </if>
            <if test="item.customerName != null and item.customerName != ''">
                customer_name = #{item.customerName,jdbcType=VARCHAR},
            </if>
            <if test="item.locationCode != null and item.locationCode != ''">
                location_code = #{item.locationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.locationArea1 != null and item.locationArea1 != ''">
                location_area1 = #{item.locationArea1,jdbcType=VARCHAR},
            </if>
            <if test="item.locationArea2 != null and item.locationArea2 != ''">
                location_area2 = #{item.locationArea2,jdbcType=VARCHAR},
            </if>
            <if test="item.locationArea3 != null and item.locationArea3 != ''">
                location_area3 = #{item.locationArea3,jdbcType=VARCHAR},
            </if>
            <if test="item.locationArea4 != null and item.locationArea4 != ''">
                location_area4 = #{item.locationArea4,jdbcType=VARCHAR},
            </if>
            <if test="item.paymentTerm != null and item.paymentTerm != ''">
                payment_term = #{item.paymentTerm,jdbcType=VARCHAR},
            </if>
            <if test="item.transitClause != null and item.transitClause != ''">
                transit_clause = #{item.transitClause,jdbcType=VARCHAR},
            </if>
            <if test="item.businessType != null and item.businessType != ''">
                business_type = #{item.businessType,jdbcType=VARCHAR},
            </if>
            <if test="item.marketType != null and item.marketType != ''">
                market_type = #{item.marketType,jdbcType=VARCHAR},
            </if>
            <if test="item.targetStockLocation != null and item.targetStockLocation != ''">
                target_stock_location = #{item.targetStockLocation,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.ediLocation != null and item.ediLocation != ''">
                edi_location = #{item.ediLocation,jdbcType=VARCHAR},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.ebsCustomerId != null and item.ebsCustomerId != ''">
                ebs_customer_id = #{item.ebsCustomerId,jdbcType=VARCHAR},
            </if>
            <if test="item.ebsSiteId != null and item.ebsSiteId != ''">
                ebs_site_id = #{item.ebsSiteId,jdbcType=VARCHAR},
            </if>
            <if test="item.shipToSiteUseId != null and item.shipToSiteUseId != ''">
                ship_to_site_use_id = #{item.shipToSiteUseId,jdbcType=VARCHAR},
            </if>
            <if test="item.siteCountry != null and item.siteCountry != ''">
                site_country = #{item.siteCountry,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateTime != null">
                last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.ediFlag != null and item.ediFlag != ''">
                edi_flag = #{item.ediFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.tallyOrderMode != null and item.tallyOrderMode != ''">
                tally_order_mode = #{item.tallyOrderMode,jdbcType=VARCHAR},
            </if>
            <if test="item.pickUpType != null and item.pickUpType != ''">
                pick_up_type = #{item.pickUpType,jdbcType=VARCHAR},
            </if>
            <if test="item.erpCustomerAddressId != null and item.erpCustomerAddressId != ''">
                erp_customer_address_id = #{item.erpCustomerAddressId,jdbcType=VARCHAR},
            </if>
            <if test="item.transferTargetStockLocation != null and item.transferTargetStockLocation != ''">
                transfer_target_stock_location = #{item.transferTargetStockLocation,jdbcType=VARCHAR},
            </if>
            <if test="item.customerAbbreviation != null and item.customerAbbreviation != ''">
                customer_abbreviation = #{item.customerAbbreviation,jdbcType=VARCHAR},
            </if>
            version_value = version_value + 1
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_oem
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="sale_org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.saleOrgId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.oemName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.customerCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.customerName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="location_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.locationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="location_area1 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.locationArea1,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="location_area2 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.locationArea2,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="location_area3 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.locationArea3,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="location_area4 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.locationArea4,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="payment_term = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.paymentTerm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transit_clause = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.transitClause,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="business_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.businessType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="market_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.marketType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="target_stock_location = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.targetStockLocation,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="edi_location = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.ediLocation,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.plantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ebs_customer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.ebsCustomerId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ebs_site_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.ebsSiteId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ship_to_site_use_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.shipToSiteUseId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="site_country = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.siteCountry,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="edi_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.ediFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="pick_up_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.pickUpType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tally_order_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.tallyOrderMode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="erp_customer_address_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.erpCustomerAddressId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transfer_target_stock_location = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.transferTargetStockLocation,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_abbreviation = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.customerAbbreviation,jdbcType=VARCHAR}
                </foreach>
            </trim>
            version_value = version_value + 1,
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_oem
            <set>
                <if test="item.saleOrgId != null and item.saleOrgId != ''">
                    sale_org_id = #{item.saleOrgId,jdbcType=VARCHAR},
                </if>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.oemName != null and item.oemName != ''">
                    oem_name = #{item.oemName,jdbcType=VARCHAR},
                </if>
                <if test="item.customerCode != null and item.customerCode != ''">
                    customer_code = #{item.customerCode,jdbcType=VARCHAR},
                </if>
                <if test="item.customerName != null and item.customerName != ''">
                    customer_name = #{item.customerName,jdbcType=VARCHAR},
                </if>
                <if test="item.locationCode != null and item.locationCode != ''">
                    location_code = #{item.locationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.locationArea1 != null and item.locationArea1 != ''">
                    location_area1 = #{item.locationArea1,jdbcType=VARCHAR},
                </if>
                <if test="item.locationArea2 != null and item.locationArea2 != ''">
                    location_area2 = #{item.locationArea2,jdbcType=VARCHAR},
                </if>
                <if test="item.locationArea3 != null and item.locationArea3 != ''">
                    location_area3 = #{item.locationArea3,jdbcType=VARCHAR},
                </if>
                <if test="item.locationArea4 != null and item.locationArea4 != ''">
                    location_area4 = #{item.locationArea4,jdbcType=VARCHAR},
                </if>
                <if test="item.paymentTerm != null and item.paymentTerm != ''">
                    payment_term = #{item.paymentTerm,jdbcType=VARCHAR},
                </if>
                <if test="item.transitClause != null and item.transitClause != ''">
                    transit_clause = #{item.transitClause,jdbcType=VARCHAR},
                </if>
                <if test="item.businessType != null and item.businessType != ''">
                    business_type = #{item.businessType,jdbcType=VARCHAR},
                </if>
                <if test="item.marketType != null and item.marketType != ''">
                    market_type = #{item.marketType,jdbcType=VARCHAR},
                </if>
                <if test="item.targetStockLocation != null and item.targetStockLocation != ''">
                    target_stock_location = #{item.targetStockLocation,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.ediLocation != null and item.ediLocation != ''">
                    edi_location = #{item.ediLocation,jdbcType=VARCHAR},
                </if>
                <if test="item.plantCode != null and item.plantCode != ''">
                    plant_code = #{item.plantCode,jdbcType=VARCHAR},
                </if>
                <if test="item.ebsCustomerId != null and item.ebsCustomerId != ''">
                    ebs_customer_id = #{item.ebsCustomerId,jdbcType=VARCHAR},
                </if>
                <if test="item.ebsSiteId != null and item.ebsSiteId != ''">
                    ebs_site_id = #{item.ebsSiteId,jdbcType=VARCHAR},
                </if>
                <if test="item.shipToSiteUseId != null and item.shipToSiteUseId != ''">
                    ship_to_site_use_id = #{item.shipToSiteUseId,jdbcType=VARCHAR},
                </if>
                <if test="item.siteCountry != null and item.siteCountry != ''">
                    site_country = #{item.siteCountry,jdbcType=VARCHAR},
                </if>
                <if test="item.ediFlag != null and item.ediFlag != ''">
                    edi_flag = #{item.ediFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateTime != null">
                    last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.tallyOrderMode != null and item.tallyOrderMode != ''">
                	tally_order_mode = #{item.tallyOrderMode,jdbcType=VARCHAR},
            	</if>
            	<if test="item.pickUpType != null and item.pickUpType != ''">
                	pick_up_type = #{item.pickUpType,jdbcType=VARCHAR},
            	</if>
            	<if test="item.erpCustomerAddressId != null and item.erpCustomerAddressId != ''">
                	erp_customer_address_id = #{item.erpCustomerAddressId,jdbcType=VARCHAR},
            	</if>
            	<if test="item.transferTargetStockLocation != null and item.transferTargetStockLocation != ''">
                    transfer_target_stock_location = #{item.transferTargetStockLocation,jdbcType=VARCHAR},
                </if>
                <if test="item.customerAbbreviation != null and item.customerAbbreviation != ''">
                    customer_abbreviation = #{item.customerAbbreviation,jdbcType=VARCHAR},
                </if>
                version_value = version_value + 1
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_oem
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_oem
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from mds_oem
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem
    </select>
    <select id="selectCustomers" parameterType="java.util.Map" resultMap="BaseResultMap">
        select distinct customer_code, customer_name
        from mds_oem
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="getAddressTwoLike" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem where 1 = 1
        <if test="locationArea2 != null and locationArea2 != ''">
            and location_area2 like #{locationArea2,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectProductEdiFlag" resultMap="EdiVOResultMap">
        SELECT
            oem.*,
            t.product_edi_flag
        FROM
            mds_oem oem
                LEFT JOIN (
                SELECT
                    ms.oem_code,
                    MAX( CASE WHEN mpsp.edi_flag = 'YES' THEN 'YES' ELSE 'NO' END ) AS product_edi_flag
                FROM
                    mds_oem ms
                        LEFT JOIN mds_oem_vehicle_model movm ON ms.oem_code = movm.oem_code
                        LEFT JOIN mds_product_stock_point mpsp ON movm.oem_vehicle_model_code = mpsp.vehicle_model_code
                GROUP BY
                    ms.oem_code
            ) t ON oem.oem_code = t.oem_code
        where 1=1
        <if test="ediFlag != null and ediFlag != ''">
            and edi_flag = #{ediFlag,jdbcType=VARCHAR}
        </if>
        <if test="productEdiFlag != null and productEdiFlag != ''">
            or product_edi_flag = #{productEdiFlag,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>
