package com.yhl.scp.dfp.delivery.convertor;

import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanCalcReportDO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanCalcReportDTO;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanCalcReportPO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanCalcReportVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>DeliveryPlanCalcReportConvertor</code>
 * <p>
 * 发货计划计算数据汇总报表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:00:52
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DeliveryPlanCalcReportConvertor {

    DeliveryPlanCalcReportConvertor INSTANCE = Mappers.getMapper(DeliveryPlanCalcReportConvertor.class);

    DeliveryPlanCalcReportDO dto2Do(DeliveryPlanCalcReportDTO obj);

    List<DeliveryPlanCalcReportDO> dto2Dos(List<DeliveryPlanCalcReportDTO> list);

    DeliveryPlanCalcReportDTO do2Dto(DeliveryPlanCalcReportDO obj);

    List<DeliveryPlanCalcReportDTO> do2Dtos(List<DeliveryPlanCalcReportDO> list);

    DeliveryPlanCalcReportDTO vo2Dto(DeliveryPlanCalcReportVO obj);

    List<DeliveryPlanCalcReportDTO> vo2Dtos(List<DeliveryPlanCalcReportVO> list);

    DeliveryPlanCalcReportVO po2Vo(DeliveryPlanCalcReportPO obj);

    List<DeliveryPlanCalcReportVO> po2Vos(List<DeliveryPlanCalcReportPO> list);

    DeliveryPlanCalcReportPO dto2Po(DeliveryPlanCalcReportDTO obj);

    List<DeliveryPlanCalcReportPO> dto2Pos(List<DeliveryPlanCalcReportDTO> obj);

    DeliveryPlanCalcReportVO do2Vo(DeliveryPlanCalcReportDO obj);

    DeliveryPlanCalcReportPO do2Po(DeliveryPlanCalcReportDO obj);

    DeliveryPlanCalcReportDO po2Do(DeliveryPlanCalcReportPO obj);

}
