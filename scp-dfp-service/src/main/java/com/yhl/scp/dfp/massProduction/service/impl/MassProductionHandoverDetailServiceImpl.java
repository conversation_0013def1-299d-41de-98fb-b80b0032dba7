package com.yhl.scp.dfp.massProduction.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableMap;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.CustomThreadPoolFactory;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.clean.convertor.CleanDemandDataConvertor;
import com.yhl.scp.dfp.clean.convertor.CleanForecastDataConvertor;
import com.yhl.scp.dfp.clean.dto.CleanDemandDataDTO;
import com.yhl.scp.dfp.clean.dto.CleanForecastDataDTO;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanDemandDataDetailDao;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanForecastDataDetailDao;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataDetailPO;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanForecastDataDetailPO;
import com.yhl.scp.dfp.clean.service.CleanDemandDataService;
import com.yhl.scp.dfp.clean.service.CleanForecastDataService;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataVO;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataVO;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDetailDao;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataDetailPO;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastVersionService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedDTO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanDetailDao;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanDetailPO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanVersionService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanDoPublishVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastEstablishmentDao;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO;
import com.yhl.scp.dfp.demand.service.DemandForecastVersionService;
import com.yhl.scp.dfp.demand.service.DemandVersionService;
import com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.loading.convertor.LoadingDemandSubmissionConvertor;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDTO;
import com.yhl.scp.dfp.loading.infrastructure.dao.LoadingDemandSubmissionDetailDao;
import com.yhl.scp.dfp.loading.infrastructure.po.LoadingDemandSubmissionDetailPO;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionFutureVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.massProduction.convertor.MassProductionHandoverDetailConvertor;
import com.yhl.scp.dfp.massProduction.domain.entity.MassProductionHandoverDetailDO;
import com.yhl.scp.dfp.massProduction.domain.service.MassProductionHandoverDetailDomainService;
import com.yhl.scp.dfp.massProduction.dto.MassProductionHandoverDetailDTO;
import com.yhl.scp.dfp.massProduction.dto.MassProductionHandoverLogDTO;
import com.yhl.scp.dfp.massProduction.enums.ApprovalStatusEnum;
import com.yhl.scp.dfp.massProduction.enums.MoveStatusEnum;
import com.yhl.scp.dfp.massProduction.enums.MoveTypeEnum;
import com.yhl.scp.dfp.massProduction.infrastructure.dao.MassProductionHandoverDao;
import com.yhl.scp.dfp.massProduction.infrastructure.dao.MassProductionHandoverDetailDao;
import com.yhl.scp.dfp.massProduction.infrastructure.po.MassProductionHandoverDetailPO;
import com.yhl.scp.dfp.massProduction.infrastructure.po.MassProductionHandoverPO;
import com.yhl.scp.dfp.massProduction.service.MassProductionHandoverDetailService;
import com.yhl.scp.dfp.massProduction.service.MassProductionHandoverLogService;
import com.yhl.scp.dfp.massProduction.vo.MassProductionHandoverDetailVO;
import com.yhl.scp.dfp.massProduction.vo.MassProductionProductBaseVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>MassProductionHandoverDetailServiceImpl</code>
 * <p>
 * 量产移交信息详情表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 16:20:22
 */
@Slf4j
@Service
public class MassProductionHandoverDetailServiceImpl extends AbstractService implements MassProductionHandoverDetailService {

    @Resource
    private MassProductionHandoverDetailDao massProductionHandoverDetailDao;

    @Resource
    private MassProductionHandoverDetailDomainService massProductionHandoverDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    
    @Resource
    private RedisUtil redisUtil;
    
    @Resource
    private DeliveryPlanVersionService deliveryPlanVersionService;
    
    @Resource
    private DeliveryPlanService deliveryPlanService;
    
    @Resource
    private DeliveryPlanDetailDao deliveryPlanDetailDao;
    
    @Resource
    private ConsistenceDemandForecastVersionService consistenceDemandForecastVersionService;
    
    @Resource
    private ConsistenceDemandForecastDataService consistenceDemandForecastDataService;
    
    @Resource
    private ConsistenceDemandForecastDataDetailDao consistenceDemandForecastDataDetailDao;
    
    @Resource
    private OriginDemandVersionService originDemandVersionService;
    
    @Resource
    private LoadingDemandSubmissionService loadingDemandSubmissionService;
    
    @Resource
    private LoadingDemandSubmissionDetailDao loadingDemandSubmissionDetailDao;
    
    @Resource
    private DemandVersionService demandVersionService;
    
    @Resource
    private CleanDemandDataService cleanDemandDataService;
    
    @Resource
    private CleanDemandDataDetailDao cleanDemandDataDetailDao;
    
    @Resource
    private DemandForecastVersionService demandForecastVersionService;
    
    @Resource
    private CleanForecastDataService cleanForecastDataService;
    
    @Resource
    private CleanForecastDataDetailDao cleanForecastDataDetailDao;
    
    @Resource
    private DemandForecastEstablishmentDao demandForecastEstablishmentDao;
    
    @Resource
    private NewMdsFeign newMdsFeign;
    
    @Resource
    private MassProductionHandoverLogService massProductionHandoverLogService;
    
    @Resource
    private MassProductionHandoverDao massProductionHandoverDao;


    @Override
    public BaseResponse<Void> doCreate(MassProductionHandoverDetailDTO massProductionHandoverDetailDTO) {
        // 0.数据转换
        MassProductionHandoverDetailDO massProductionHandoverDetailDO = MassProductionHandoverDetailConvertor.INSTANCE.dto2Do(massProductionHandoverDetailDTO);
        MassProductionHandoverDetailPO massProductionHandoverDetailPO = MassProductionHandoverDetailConvertor.INSTANCE.dto2Po(massProductionHandoverDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        massProductionHandoverDetailDomainService.validation(massProductionHandoverDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(massProductionHandoverDetailPO);
        massProductionHandoverDetailDao.insert(massProductionHandoverDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MassProductionHandoverDetailDTO massProductionHandoverDetailDTO) {
        // 0.数据转换
        MassProductionHandoverDetailDO massProductionHandoverDetailDO = MassProductionHandoverDetailConvertor.INSTANCE.dto2Do(massProductionHandoverDetailDTO);
        MassProductionHandoverDetailPO massProductionHandoverDetailPO = MassProductionHandoverDetailConvertor.INSTANCE.dto2Po(massProductionHandoverDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        massProductionHandoverDetailDomainService.validation(massProductionHandoverDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(massProductionHandoverDetailPO);
        massProductionHandoverDetailDao.update(massProductionHandoverDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MassProductionHandoverDetailDTO> list) {
        List<MassProductionHandoverDetailPO> newList = MassProductionHandoverDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        massProductionHandoverDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MassProductionHandoverDetailDTO> list) {
        List<MassProductionHandoverDetailPO> newList = MassProductionHandoverDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        massProductionHandoverDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return massProductionHandoverDetailDao.deleteBatch(idList);
        }
        return massProductionHandoverDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MassProductionHandoverDetailVO selectByPrimaryKey(String id) {
        MassProductionHandoverDetailPO po = massProductionHandoverDetailDao.selectByPrimaryKey(id);
        return MassProductionHandoverDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_mass_production_handover_detail")
    public List<MassProductionHandoverDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_mass_production_handover_detail")
    public List<MassProductionHandoverDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MassProductionHandoverDetailVO> dataList = massProductionHandoverDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        MassProductionHandoverDetailServiceImpl target = springBeanUtils.getBean(MassProductionHandoverDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MassProductionHandoverDetailVO> selectByParams(Map<String, Object> params) {
        List<MassProductionHandoverDetailPO> list = massProductionHandoverDetailDao.selectByParams(params);
        return MassProductionHandoverDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MassProductionHandoverDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MASS_PRODUCTION_HANDOVER_DETAIL.getCode();
    }

    @Override
    public List<MassProductionHandoverDetailVO> invocation(List<MassProductionHandoverDetailVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

	@Override
	public List<MassProductionProductBaseVO> selectBaseInfoByPorductCodes(List<String> productCodes) {
		//
		return null;
	}

	@Override
	public void deleteByMassProductionHandoverId(String massProductionHandoverId) {
		massProductionHandoverDetailDao.deleteByMassProductionHandoverId(massProductionHandoverId);
	}

	@Override
	public BaseResponse<List<String>> doUpdateDepliveryPlan(String massProductionHandoverId, String orderPlanner, List<DeliveryPlanPublishedDTO> oldDeliveryPlanPublishedDTOS) {
		//防重复提交
		String redisKey = String.join("#", "MASS_PRODUCTION_HANDOVER_DELIVERY", massProductionHandoverId);
		String redisPublishKey = String.join("#", "DELIVERY_PLAN_PUBLISH_LOCK", SystemHolder.getUserId());
		//初始化日志
		MassProductionHandoverLogDTO handoverLog = initMassProductionHandoverLog(massProductionHandoverId,
				orderPlanner, MoveTypeEnum.UPDATE_DELIVERY_PLAN.getCode());
        try {
            //1.获取最新版的发货计划
            DeliveryPlanVersionVO deliveryPlanVersion = deliveryPlanVersionService.selectLatestVersionByParams(new HashMap<>());
            List<MassProductionHandoverDetailVO> massProductionHandoverDetails= this.selectByParams(ImmutableMap.of(
        			"enabled", YesOrNoEnum.YES.getCode(), 
        			"updateOrderPlannerFlag", YesOrNoEnum.NO.getCode(),
        			"massProductionHandoverId" , massProductionHandoverId));
            if(CollectionUtils.isEmpty(massProductionHandoverDetails)) {
            	handoverLog.setRemark("物料主数据已进行项目移交量产");
            	return BaseResponse.error("请刷新页面,选择物料进行操作!");
            }
            List<String> productCodes = massProductionHandoverDetails.stream().map(MassProductionHandoverDetailVO::getProductCode)
            		.distinct().collect(Collectors.toList());
            handoverLog.setProductCode(String.join(",", productCodes));
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))){
            	handoverLog.setRemark("正在进行发货计划更新");
                return BaseResponse.error("正在进行发货计划更新，请稍后提交");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId(), 360);
            //2.处理发货计划数据
            List<DeliveryPlanVO> deliveryPlans = deliveryPlanService.selectByParams(ImmutableMap.of(
        			"enabled", YesOrNoEnum.YES.getCode(), 
        			"versionId", deliveryPlanVersion.getId(), 
        			"demandCategory", ProductionDemandTypeEnum.PROJECT_DEMAND.getCode(), 
        			"productCodes" , productCodes));
            if(CollectionUtils.isEmpty(deliveryPlans)) {
//            	handoverLog.setRemark("产品编码" + String.join(",", productCodes) + "不存在发货计划，请检查");
//            	return BaseResponse.error("产品编码" + String.join(",", productCodes) + "不存在发货计划，请检查");
            	return BaseResponse.success();
            }
            //校验是否可发布
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisPublishKey))){
            	handoverLog.setRemark("当前已有发货计划在发布，请稍后提交");
                return BaseResponse.error("当前已有发货计划在发布，请稍后提交");
            }
            redisUtil.set(redisPublishKey, SystemHolder.getUserId(), 360);
            
            List<String> deliveryPlanDataIds = deliveryPlans.stream().map(DeliveryPlanVO::getId).collect(Collectors.toList());
            List<DeliveryPlanDetailPO> deliveryPlanDetails = deliveryPlanDetailDao.selectByParams(ImmutableMap.of(
        			"enabled", YesOrNoEnum.YES.getCode(), 
        			"deliveryPlanDataIds", deliveryPlanDataIds, 
        			"demandTimeStart", DateUtils.getDayFirstTime(new Date()), 
        			"demandTimeEnd", DateUtils.getDayLastTime(DateUtils.moveDay(new Date(), 30))));
            deliveryPlanDetails.forEach( e-> e.setDemandQuantity(0));
            BasePOUtils.updateBatchFiller(deliveryPlanDetails);
            deliveryPlanDetailDao.updateBatch(deliveryPlanDetails);
            //3.进行发布
            DeliveryPlanDoPublishVO doPublish = deliveryPlanService.doPublish(deliveryPlanDataIds,
                    SystemHolder.getUserId(), SystemHolder.getUserName());
            BaseResponse<List<String>> response = doPublish.getSyncMesResp();
            handoverLog.setRemark(response.getMsg());
            if(response.getSuccess()) {
            	handoverLog.setMoveStatus(MoveStatusEnum.SUCCESS.getCode());
            };
            oldDeliveryPlanPublishedDTOS.addAll(doPublish.getOldDeliveryPlanPublishedDTOS());
            return response;
        } catch (Exception e) {
        	handoverLog.setRemark("更新发货计划失败");
            log.error("更新发货计划失败", e);
            throw new BusinessException("更新发货计划失败, {0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
            redisUtil.delete(redisPublishKey);
            massProductionHandoverLogService.doCreate(handoverLog);
        }
	}

	private MassProductionHandoverLogDTO initMassProductionHandoverLog(String massProductionHandoverId,
			String orderPlanner, String moveType) {
		MassProductionHandoverLogDTO handoverLog = new MassProductionHandoverLogDTO();
		handoverLog.setMoveType(moveType);
		handoverLog.setProjectPlanner(SystemHolder.getUserId());
		handoverLog.setOutputPlanner(orderPlanner);
		handoverLog.setMoveStatus(MoveStatusEnum.FAIL.getCode());
		handoverLog.setMassProductionHandoverId(massProductionHandoverId);
		return handoverLog;
	}

	@Override
	public BaseResponse<Void> doUpdateDemandForecast(String massProductionHandoverId, String orderPlanner) {
		//防重复提交
		String redisKey = String.join("#", "MASS_PRODUCTION_HANDOVER_FORECAST", massProductionHandoverId);
		//初始化日志
		MassProductionHandoverLogDTO handoverLog = initMassProductionHandoverLog(massProductionHandoverId,
				orderPlanner, MoveTypeEnum.UPDATE_DEMAND_FORECAST.getCode());
        try {
        	//获取需求预测数据
    		List<MassProductionHandoverDetailVO> massProductionHandoverDetails= this.selectByParams(ImmutableMap.of(
    				"enabled", YesOrNoEnum.YES.getCode(), 
        			"updateOrderPlannerFlag", YesOrNoEnum.NO.getCode(),
        			"massProductionHandoverId" , massProductionHandoverId));
            if(CollectionUtils.isEmpty(massProductionHandoverDetails)) {
            	handoverLog.setRemark("物料主数据已进行项目移交量产");
            	return BaseResponse.error("请刷新页面,选择物料进行操作!");
            }
            List<String> productCodes = massProductionHandoverDetails.stream().map(MassProductionHandoverDetailVO::getProductCode)
            		.distinct().collect(Collectors.toList());
            handoverLog.setProductCode(String.join(",", productCodes));
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))){
            	handoverLog.setRemark("正在进行更新需求预测");
                return BaseResponse.error("正在进行更新需求预测，请稍后提交");
            }
            //1.获取最新已发布版本的一致性需求预测
            ConsistenceDemandForecastVersionVO versionInfo = consistenceDemandForecastVersionService.selectOneMaxVersionByParams(ImmutableMap.of(
        			"enabled", YesOrNoEnum.YES.getCode(), 
        			"versionStatus" , PublishStatusEnum.PUBLISHED.getCode()));
    		if(versionInfo == null) {
    			handoverLog.setRemark("未获取到最新已发布版本的一致性需求预测版本");
    			return BaseResponse.error("未获取到最新已发布版本的一致性需求预测版本!");
    		}
            List<ConsistenceDemandForecastDataVO> forecastDataList = consistenceDemandForecastDataService.selectByParams(ImmutableMap.of(
        			"enabled", YesOrNoEnum.YES.getCode(), 
        			"versionId", versionInfo.getId(), 
        			"demandCategory", ProductionDemandTypeEnum.PROJECT_DEMAND.getCode(), 
        			"productCodeList" , productCodes));
            if(CollectionUtils.isEmpty(forecastDataList)) {
            	handoverLog.setRemark("未匹配到对应的一致性需求预测数据");
            	handoverLog.setMoveStatus(MoveStatusEnum.SUCCESS.getCode());
            	return BaseResponse.success(BaseResponse.OP_SUCCESS);
            }
            //获取对明细数据，将业务预测量forecast_quantity，客户预测量customer_forecasts_quantity，算法预测量algorithm_forecasts_quantity改为0
            List<String> forecastDataIds = forecastDataList.stream().map(ConsistenceDemandForecastDataVO::getId).collect(Collectors.toList());
            List<ConsistenceDemandForecastDataDetailPO> forecastDataDetailList = consistenceDemandForecastDataDetailDao.selectByParams(ImmutableMap.of(
        			"enabled", YesOrNoEnum.YES.getCode(), 
        			"consistenceDemandForecastDataIdList" , forecastDataIds));
            forecastDataDetailList.forEach( e-> {
            	e.setForecastQuantity(BigDecimal.ZERO);
            	e.setCustomerForecastsQuantity(BigDecimal.ZERO);
            	e.setAlgorithmForecastsQuantity(BigDecimal.ZERO);
            });
            BasePOUtils.updateBatchFiller(forecastDataDetailList);
            consistenceDemandForecastDataDetailDao.updateBatch(forecastDataDetailList);
            handoverLog.setRemark("一致性需求预测数据已清零");
            handoverLog.setMoveStatus(MoveStatusEnum.SUCCESS.getCode());
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("更新需求预测失败", e);
            handoverLog.setRemark("更新需求预测失败");
            throw new BusinessException("更新需求预测失败, {0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
            massProductionHandoverLogService.doCreate(handoverLog);
        }
	}

	@Override
	public BaseResponse<Void> doUpdateDemandSubmission(String massProductionHandoverId, String orderPlanner) {
		//防重复提交
		String redisKey = String.join("#", "MASS_PRODUCTION_HANDOVER_SUBMISSION", massProductionHandoverId);
		//初始化日志
		MassProductionHandoverLogDTO handoverLog = initMassProductionHandoverLog(massProductionHandoverId,
				orderPlanner, MoveTypeEnum.UPDATE_DEMAND_SUBMISSION.getCode());
		ThreadPoolExecutor threadPoolExecutor = CustomThreadPoolFactory.instance();
        try {
        	List<MassProductionHandoverDetailVO> massProductionHandoverDetails= this.selectByParams(ImmutableMap.of(
            		"enabled", YesOrNoEnum.YES.getCode(), 
        			"updateOrderPlannerFlag", YesOrNoEnum.NO.getCode(),
        			"massProductionHandoverId" , massProductionHandoverId));
            if(CollectionUtils.isEmpty(massProductionHandoverDetails)) {
            	handoverLog.setRemark("物料主数据已进行项目移交量产");
            	return BaseResponse.error("请刷新页面,选择物料进行操作!");
            }
            List<String> productCodes = massProductionHandoverDetails.stream().map(MassProductionHandoverDetailVO::getProductCode)
            		.distinct().collect(Collectors.toList());
            handoverLog.setProductCode(String.join(",", productCodes));
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))){
            	handoverLog.setRemark("正在进行更新装车需求提报");
                return BaseResponse.error("正在进行更新装车需求提报，请稍后提交");
            }
            //需求类型demand_category为项目需求PROJECT_DEMAND的数据，将所选数据对应明细表中的数量改为0，需求类型改为”量产需求“
            //1.处理最新版本的装车需求提报数据
            CompletableFuture<Boolean> loadingDemandSubmissionFuture = CompletableFuture.supplyAsync(() -> {
            	String originDemandVersionId = originDemandVersionService.selectLatestVersionId();
                List<LoadingDemandSubmissionVO> loadingDemandSubmissionList = loadingDemandSubmissionService.selectByParams(ImmutableMap.of(
            			"enabled", YesOrNoEnum.YES.getCode(), 
            			"versionId", originDemandVersionId, 
            			"demandCategory", ProductionDemandTypeEnum.PROJECT_DEMAND.getCode(), 
            			"productCodes" , productCodes));
                if(CollectionUtils.isNotEmpty(loadingDemandSubmissionList)) {
                	List<String> submissionIds = loadingDemandSubmissionList.stream().map(LoadingDemandSubmissionVO::getId).collect(Collectors.toList());
                	List<LoadingDemandSubmissionDetailPO> detailList = loadingDemandSubmissionDetailDao.selectByParams(ImmutableMap.of(
            			"enabled", YesOrNoEnum.YES.getCode(), 
            			"submissionIds", submissionIds));
                	//修改需求类型
                	List<LoadingDemandSubmissionDTO> submissionList = LoadingDemandSubmissionConvertor.INSTANCE.vo2Dtos(loadingDemandSubmissionList);
                	submissionList.forEach( e-> e.setDemandCategory(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode()));
                	Lists.partition(submissionList, 2000).forEach(subList -> {
                		loadingDemandSubmissionService.doUpdateBatch(subList);
                	});
                	
                	//修改数量
                	detailList.forEach( e-> e.setDemandQuantity(BigDecimal.ZERO));
                	BasePOUtils.updateBatchFiller(detailList);
                	Lists.partition(detailList, 2000).forEach(subList -> {
                		loadingDemandSubmissionDetailDao.updateBatch(subList);
                	});
                }
        		return true;
            }, threadPoolExecutor);
            
            //2.处理最新版本的日需求汇总数据
            CompletableFuture<Boolean> cleanDemandDataFuture = CompletableFuture.supplyAsync(() -> {
            	DemandVersionVO dayDemandVersion = demandVersionService.selectLastVersionByVersionTypeAndPlanPeriod(VersionTypeEnum.CLEAN_DEMAND.getCode(), null);
                List<CleanDemandDataVO> cleanDemandDataList = cleanDemandDataService.selectByParams(ImmutableMap.of(
            			"enabled", YesOrNoEnum.YES.getCode(), 
            			"versionId", dayDemandVersion.getId(), 
            			"demandCategory", ProductionDemandTypeEnum.PROJECT_DEMAND.getCode(), 
            			"productCodes" , productCodes));
                if(CollectionUtils.isNotEmpty(cleanDemandDataList)) {
                	List<String> cleanDemandDataIds = cleanDemandDataList.stream().map(CleanDemandDataVO::getId).collect(Collectors.toList());
                	List<CleanDemandDataDetailPO> detailList = cleanDemandDataDetailDao.selectByParams(ImmutableMap.of(
            			"enabled", YesOrNoEnum.YES.getCode(), 
            			"cleanDemandDataIds", cleanDemandDataIds));
                	//修改需求类型
                	List<CleanDemandDataDTO> datalist = CleanDemandDataConvertor.INSTANCE.vo2Dtos(cleanDemandDataList);
                	datalist.forEach( e-> e.setDemandCategory(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode()));
                	Lists.partition(datalist, 2000).forEach(subList -> {
                		cleanDemandDataService.doUpdateBatch(subList);
                	});
                	//修改数量
                	detailList.forEach( e-> e.setDemandQuantity(BigDecimal.ZERO));
                	BasePOUtils.updateBatchFiller(detailList);
                	Lists.partition(detailList, 2000).forEach(subList -> {
                		cleanDemandDataDetailDao.updateBatch(subList);
                	});
                }
        		return true;
            }, threadPoolExecutor);
            
            //3.处理最新版本的月预测数据
            CompletableFuture<Boolean> cleanForecastDataFuture = CompletableFuture.supplyAsync(() -> {
            	DemandVersionVO monthDemandVersion = demandVersionService.selectLastVersionByVersionTypeAndPlanPeriod(VersionTypeEnum.CLEAN_FORECAST.getCode(), null);
                List<CleanForecastDataVO> cleanForecastDatas = cleanForecastDataService.selectByParams(ImmutableMap.of(
            			"enabled", YesOrNoEnum.YES.getCode(), 
            			"versionId", monthDemandVersion.getId(), 
            			"demandCategory", ProductionDemandTypeEnum.PROJECT_DEMAND.getCode(), 
            			"productCodeList" , productCodes));
                if(CollectionUtils.isNotEmpty(cleanForecastDatas)) {
                	List<String> cleanForecastDataIdList = cleanForecastDatas.stream().map(CleanForecastDataVO::getId).collect(Collectors.toList());
                	List<CleanForecastDataDetailPO> detailList = cleanForecastDataDetailDao.selectByParams(ImmutableMap.of(
            			"enabled", YesOrNoEnum.YES.getCode(), 
            			"cleanForecastDataIdList", cleanForecastDataIdList));
                	
                	//修改需求类型
                	List<CleanForecastDataDTO> datalist = CleanForecastDataConvertor.INSTANCE.vo2Dtos(cleanForecastDatas);
                	datalist.forEach( e-> e.setDemandCategory(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode()));
                	Lists.partition(datalist, 2000).forEach(subList -> {
                		cleanForecastDataService.doUpdateBatch(subList);
                	});
                	//修改数量
                	detailList.forEach( e-> e.setForecastQuantity(BigDecimal.ZERO));
                	BasePOUtils.updateBatchFiller(detailList);
                	Lists.partition(detailList, 2000).forEach(subList -> {
                		cleanForecastDataDetailDao.updateBatch(subList);
                	});
                }
        		return true;
            }, threadPoolExecutor);
            
            //4.处理最新版本的需求预测编制数据
            CompletableFuture<Boolean> demandForecastEstablishmentFuture = CompletableFuture.supplyAsync(() -> {
            	DemandForecastVersionVO demandForecastVersion = demandForecastVersionService.selectLastVersionByPlanPeriod(null);
                List<DemandForecastEstablishmentPO> establishmentList = demandForecastEstablishmentDao.selectByParams(ImmutableMap.of(
            			"enabled", YesOrNoEnum.YES.getCode(), 
            			"forecastVersionId", demandForecastVersion.getId(), 
            			"demandCategory", ProductionDemandTypeEnum.PROJECT_DEMAND.getCode(), 
            			"productCodeList" , productCodes));
                if(CollectionUtils.isNotEmpty(establishmentList)) {
                	establishmentList.forEach( e-> {
                		e.setDemandCategory(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode());
                		e.setCustomerForecast(BigDecimal.ZERO);
                		e.setAlgorithmForecast(BigDecimal.ZERO);
                		e.setDemandForecast(BigDecimal.ZERO);
                	});
                	BasePOUtils.updateBatchFiller(establishmentList);
                	Lists.partition(establishmentList, 2000).forEach(subList -> {
                		demandForecastEstablishmentDao.updateBatch(subList);
                	});
                }
        		return true;
            }, threadPoolExecutor);
            loadingDemandSubmissionFuture.join();
            cleanDemandDataFuture.join();
            cleanForecastDataFuture.join();
            demandForecastEstablishmentFuture.join();
            handoverLog.setRemark("装车需求提报及相关数据已清零");
            handoverLog.setMoveStatus(MoveStatusEnum.SUCCESS.getCode());
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("更新装车需求提报失败", e);
            handoverLog.setRemark("装车需求提报及相关数据更新失败");
            throw new BusinessException("更新装车需求提报失败, {0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
            massProductionHandoverLogService.doCreate(handoverLog);
            CustomThreadPoolFactory.closeOrShutdown(threadPoolExecutor);
        }
	}

	@Override
	public BaseResponse<Void> doUpdateProduct(String massProductionHandoverId, String orderPlanner) {
		//防重复提交
		String redisKey = String.join("#", "MASS_PRODUCTION_HANDOVER_PRODUCT", massProductionHandoverId);
		//初始化日志
		MassProductionHandoverLogDTO handoverLog = initMassProductionHandoverLog(massProductionHandoverId,
				orderPlanner, MoveTypeEnum.UPDATE_ORDER_PLANNER.getCode());
        try {
        	List<MassProductionHandoverDetailVO> massProductionHandoverDetails= this.selectByParams(ImmutableMap.of(
            		"enabled", YesOrNoEnum.YES.getCode(), 
        			"updateOrderPlannerFlag", YesOrNoEnum.NO.getCode(),
        			"massProductionHandoverId" , massProductionHandoverId));
            if(CollectionUtils.isEmpty(massProductionHandoverDetails)) {
            	handoverLog.setRemark("物料主数据已进行项目移交量产");
            	return BaseResponse.error("请刷新页面,选择物料进行操作!");
            }
            List<String> productCodes = massProductionHandoverDetails.stream().map(MassProductionHandoverDetailVO::getProductCode)
            		.distinct().collect(Collectors.toList());
            handoverLog.setProductCode(String.join(",", productCodes));
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))){
            	handoverLog.setRemark("正在进行更新人员权限");
                return BaseResponse.error("正在进行更新人员权限，请稍后提交");
            }
            //修改物料量产计划员，物料状态
            newMdsFeign.updateOrderPlanner(SystemHolder.getScenario(), productCodes, orderPlanner);
            //修改明细是否修改人员权限
            massProductionHandoverDetails.forEach( e-> e.setUpdateOrderPlannerFlag(YesOrNoEnum.YES.getCode()));
            List<MassProductionHandoverDetailDTO> detailList = MassProductionHandoverDetailConvertor.
            		INSTANCE.vo2Dtos(massProductionHandoverDetails);
            this.doUpdateBatch(detailList);
            //状态更新，将状态修改为已移交
            MassProductionHandoverPO massProductionHandover = massProductionHandoverDao.selectByPrimaryKey(massProductionHandoverId);
            massProductionHandover.setApprovalStatus(ApprovalStatusEnum.HAND_OVER.getCode());
            BasePOUtils.insertFiller(massProductionHandover);
            massProductionHandoverDao.updateSelective(massProductionHandover);
            
            handoverLog.setRemark("产品权限已移交");
            handoverLog.setMoveStatus(MoveStatusEnum.SUCCESS.getCode());
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("更新人员权限失败", e);
            handoverLog.setRemark("更新人员权限失败");
            throw new BusinessException("更新人员权限失败, {0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
            massProductionHandoverLogService.doCreate(handoverLog);
        }
	}

	@Override
	public void doDeleteByMassProductionHandoverIds(List<String> massProductionHandoverIds) {
		if(CollectionUtils.isEmpty(massProductionHandoverIds)) {
			return;
		}
		massProductionHandoverDetailDao.doDeleteByMassProductionHandoverIds(massProductionHandoverIds);
	}

	@Override
	public List<MassProductionHandoverDetailVO> selectCanUpdateOrderPlanner(String massProductionHandoverId) {
		MassProductionHandoverPO oldHandover = massProductionHandoverDao.selectByPrimaryKey(massProductionHandoverId);
		if(!ApprovalStatusEnum.APPROVED.getCode().equals(oldHandover.getApprovalStatus())) {
    		throw new BusinessException("当前状态不支持量产移交操作");
    	}
		return this.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"updateOrderPlannerFlag", YesOrNoEnum.NO.getCode(), 
    			"massProductionHandoverId" , massProductionHandoverId));
	}

}
