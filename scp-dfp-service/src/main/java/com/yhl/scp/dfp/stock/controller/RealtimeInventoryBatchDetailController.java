package com.yhl.scp.dfp.stock.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.stock.dto.RealtimeInventoryBatchDetailDTO;
import com.yhl.scp.dfp.stock.service.RealtimeInventoryBatchDetailService;
import com.yhl.scp.dfp.stock.vo.RealtimeInventoryBatchDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>RealtimeInventoryBatchDetailController</code>
 * <p>
 * 实时库存批次明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:52:24
 */
@Slf4j
@Api(tags = "实时库存批次明细控制器")
@RestController
@RequestMapping("realtimeInventoryBatchDetail")
public class RealtimeInventoryBatchDetailController extends BaseController {

    @Resource
    private RealtimeInventoryBatchDetailService realtimeInventoryBatchDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<RealtimeInventoryBatchDetailVO>> page() {
        List<RealtimeInventoryBatchDetailVO> realtimeInventoryBatchDetailList = realtimeInventoryBatchDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<RealtimeInventoryBatchDetailVO> pageInfo = new PageInfo<>(realtimeInventoryBatchDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody RealtimeInventoryBatchDetailDTO realtimeInventoryBatchDetailDTO) {
        return realtimeInventoryBatchDetailService.doCreate(realtimeInventoryBatchDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody RealtimeInventoryBatchDetailDTO realtimeInventoryBatchDetailDTO) {
        return realtimeInventoryBatchDetailService.doUpdate(realtimeInventoryBatchDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        realtimeInventoryBatchDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<RealtimeInventoryBatchDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, realtimeInventoryBatchDetailService.selectByPrimaryKey(id));
    }

}
