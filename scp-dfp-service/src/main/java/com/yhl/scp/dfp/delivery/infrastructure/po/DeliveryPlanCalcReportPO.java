package com.yhl.scp.dfp.delivery.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>DeliveryPlanCalcReportPO</code>
 * <p>
 * 发货计划计算数据汇总报表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:00:52
 */
public class DeliveryPlanCalcReportPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 421110416614587025L;

        /**
     * id
     */
        private String id;
        /**
     * 主机厂编码
     */
        private String oemCode;
        /**
     * 产品编码
     */
        private String productCode;
        /**
     * 期初库存
     */
        private Integer openingInventory;
        /**
     * 主机厂期初库存
     */
        private Integer oemOpeningInventory;
        /**
     * 在途数量
     */
        private Integer receive;
        /**
     * 备注
     */
        private String remark;
        /**
     * 是否生效（生效/失效）
     */
        private String enabled;
        /**
     * 创建人
     */
        private String creator;
        /**
     * 创建时间
     */
        private Date createTime;
        /**
     * 修改人
     */
        private String modifier;
        /**
     * 修改时间
     */
        private Date modifyTime;
        /**
     * 版本
     */
        private Integer versionValue;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Integer getOpeningInventory() {
        return openingInventory;
    }

    public void setOpeningInventory(Integer openingInventory) {
        this.openingInventory = openingInventory;
    }

    public Integer getOemOpeningInventory() {
        return oemOpeningInventory;
    }

    public void setOemOpeningInventory(Integer oemOpeningInventory) {
        this.oemOpeningInventory = oemOpeningInventory;
    }

    public Integer getReceive() {
        return receive;
    }

    public void setReceive(Integer receive) {
        this.receive = receive;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
