package com.yhl.scp.dfp.delivery.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanCalcReportConvertor;
import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanCalcReportDO;
import com.yhl.scp.dfp.delivery.domain.service.DeliveryPlanCalcReportDomainService;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanCalcReportDTO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanCalcReportDao;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanCalcReportPO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanCalcReportService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanCalcReportVO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.ips.utils.BasePOUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>DeliveryPlanCalcReportServiceImpl</code>
 * <p>
 * 发货计划计算数据汇总报表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:00:52
 */
@Slf4j
@Service
public class DeliveryPlanCalcReportServiceImpl extends AbstractService implements DeliveryPlanCalcReportService {

    @Resource
    private DeliveryPlanCalcReportDao deliveryPlanCalcReportDao;

    @Resource
    private DeliveryPlanCalcReportDomainService deliveryPlanCalcReportDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(DeliveryPlanCalcReportDTO deliveryPlanCalcReportDTO) {
        // 0.数据转换
        DeliveryPlanCalcReportDO deliveryPlanCalcReportDO = DeliveryPlanCalcReportConvertor.INSTANCE.dto2Do(deliveryPlanCalcReportDTO);
        DeliveryPlanCalcReportPO deliveryPlanCalcReportPO = DeliveryPlanCalcReportConvertor.INSTANCE.dto2Po(deliveryPlanCalcReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryPlanCalcReportDomainService.validation(deliveryPlanCalcReportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(deliveryPlanCalcReportPO);
        deliveryPlanCalcReportDao.insert(deliveryPlanCalcReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DeliveryPlanCalcReportDTO deliveryPlanCalcReportDTO) {
        // 0.数据转换
        DeliveryPlanCalcReportDO deliveryPlanCalcReportDO = DeliveryPlanCalcReportConvertor.INSTANCE.dto2Do(deliveryPlanCalcReportDTO);
        DeliveryPlanCalcReportPO deliveryPlanCalcReportPO = DeliveryPlanCalcReportConvertor.INSTANCE.dto2Po(deliveryPlanCalcReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryPlanCalcReportDomainService.validation(deliveryPlanCalcReportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(deliveryPlanCalcReportPO);
        deliveryPlanCalcReportDao.update(deliveryPlanCalcReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DeliveryPlanCalcReportDTO> list) {
        List<DeliveryPlanCalcReportPO> newList = DeliveryPlanCalcReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        deliveryPlanCalcReportDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DeliveryPlanCalcReportDTO> list) {
        List<DeliveryPlanCalcReportPO> newList = DeliveryPlanCalcReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        deliveryPlanCalcReportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return deliveryPlanCalcReportDao.deleteBatch(idList);
        }
        return deliveryPlanCalcReportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DeliveryPlanCalcReportVO selectByPrimaryKey(String id) {
        DeliveryPlanCalcReportPO po = deliveryPlanCalcReportDao.selectByPrimaryKey(id);
        return DeliveryPlanCalcReportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_delivery_plan_calc_report")
    public List<DeliveryPlanCalcReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_delivery_plan_calc_report")
    public List<DeliveryPlanCalcReportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DeliveryPlanCalcReportVO> dataList = deliveryPlanCalcReportDao.selectByCondition(sortParam, queryCriteriaParam);
        DeliveryPlanCalcReportServiceImpl target = springBeanUtils.getBean(DeliveryPlanCalcReportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DeliveryPlanCalcReportVO> selectByParams(Map<String, Object> params) {
        List<DeliveryPlanCalcReportPO> list = deliveryPlanCalcReportDao.selectByParams(params);
        return DeliveryPlanCalcReportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DeliveryPlanCalcReportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DELIVERY_PLAN_CALC_REPORT.getCode();
    }

    @Override
    public List<DeliveryPlanCalcReportVO> invocation(List<DeliveryPlanCalcReportVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
