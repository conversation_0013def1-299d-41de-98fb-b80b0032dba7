package com.yhl.scp.dfp.mdsController.controller;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>DfpMdsFeignController</code>
 * <p>
 * DfpMdsFeignController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-22 11:49:36
 */
@Api(tags = "DFP调用MDS相关接口")
@RestController
@RequestMapping("")
public class DfpMdsFeignController {

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private OemStockPointMapService oemStockPointMapService;

    @ApiOperation("获取库存点所有数据")
    @GetMapping("newStockPoint/dropDown")
    public BaseResponse<List<NewStockPointVO>> dropDownNewStockPoint() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        return BaseResponse.success(newMdsFeign.selectStockPointByParams(SystemHolder.getScenario(), queryMap));
    }

    @ApiOperation("获取库存点物品所有数据")
    @GetMapping("newProductStockPoint/dropDown")
    public BaseResponse<List<LabelValue<String>>> dropDownNewProductStockPoints(@RequestParam("oemCode") String oemCode) {
        List<LabelValue<String>> result = new ArrayList<>();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        queryMap.put("oemCode", oemCode);
        List<OemStockPointMapVO> oemStockPointMapVOS = oemStockPointMapService.selectByParams(queryMap);
        if (CollectionUtils.isEmpty(oemStockPointMapVOS)) {
            return BaseResponse.success(result);
        }
        List<String> stockPointCodes = oemStockPointMapVOS.stream().map(OemStockPointMapVO::getStockPointCode).distinct().collect(Collectors.toList());
        Map<String, Object> productParams = new HashMap<>();
        productParams.put("enabled", YesOrNoEnum.YES.getCode());
        productParams.put("stockPointCodes", stockPointCodes);
        result.addAll(newMdsFeign.selectProductStockPointLabelValueByParams(SystemHolder.getScenario(), productParams));
        return BaseResponse.success(result);
    }

    @ApiOperation("获取本厂编码数据")
    @GetMapping("newProductStockPoint/selectProductCodeLike")
    public BaseResponse<List<LabelValue<String>>> dropDownProductCodeLike() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        return BaseResponse.success(newMdsFeign.selectProductCodeLikeByParams(SystemHolder.getScenario(), queryMap));
    }
    
}