package com.yhl.scp.dfp.transport.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesTransportRouting;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.transport.convertor.TransportRoutingConvertor;
import com.yhl.scp.dfp.transport.domain.entity.TransportRoutingDO;
import com.yhl.scp.dfp.transport.domain.service.TransportRoutingDomainService;
import com.yhl.scp.dfp.transport.dto.TransportRoutingDTO;
import com.yhl.scp.dfp.transport.dto.TransportRoutingDetailDTO;
import com.yhl.scp.dfp.transport.dto.TransportRoutingInterfaceLogDTO;
import com.yhl.scp.dfp.transport.infrastructure.dao.TransportRoutingDao;
import com.yhl.scp.dfp.transport.infrastructure.po.TransportRoutingPO;
import com.yhl.scp.dfp.transport.service.TransportRoutingDetailService;
import com.yhl.scp.dfp.transport.service.TransportRoutingInterfaceLogService;
import com.yhl.scp.dfp.transport.service.TransportRoutingService;
import com.yhl.scp.dfp.transport.vo.TransportRoutingDetailVO;
import com.yhl.scp.dfp.transport.vo.TransportRoutingInterfaceLogVO;
import com.yhl.scp.dfp.transport.vo.TransportRoutingVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>TransportRoutingServiceImpl</code>
 * <p>
 * 运输路径应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 22:19:46
 */
@Slf4j
@Service
public class TransportRoutingServiceImpl extends AbstractService implements TransportRoutingService {

    @Resource
    private TransportRoutingDao transportRoutingDao;

    @Resource
    private TransportRoutingDomainService transportRoutingDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    TransportRoutingDetailService transportDetailService;

    @Resource
    TransportRoutingInterfaceLogService transportRoutingInterfaceLogService;


    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(TransportRoutingDTO transportRoutingDTO) {
        // 0.数据转换
        TransportRoutingDO transportRoutingDO = TransportRoutingConvertor.INSTANCE.dto2Do(transportRoutingDTO);
        TransportRoutingPO transportRoutingPO = TransportRoutingConvertor.INSTANCE.dto2Po(transportRoutingDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        transportRoutingDomainService.validation(transportRoutingDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(transportRoutingPO);
        transportRoutingDao.insert(transportRoutingPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(TransportRoutingDTO transportRoutingDTO) {
        // 0.数据转换
        TransportRoutingDO transportRoutingDO = TransportRoutingConvertor.INSTANCE.dto2Do(transportRoutingDTO);
        TransportRoutingPO transportRoutingPO = TransportRoutingConvertor.INSTANCE.dto2Po(transportRoutingDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        transportRoutingDomainService.validation(transportRoutingDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(transportRoutingPO);
        transportRoutingDao.update(transportRoutingPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<TransportRoutingDTO> list) {
        List<TransportRoutingPO> newList = TransportRoutingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        transportRoutingDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<TransportRoutingDTO> list) {
        List<TransportRoutingPO> newList = TransportRoutingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        transportRoutingDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return transportRoutingDao.deleteBatch(idList);
        }
        return transportRoutingDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public TransportRoutingVO selectByPrimaryKey(String id) {
        TransportRoutingPO po = transportRoutingDao.selectByPrimaryKey(id);
        return TransportRoutingConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "TRANSPORT_ROUTING")
    public List<TransportRoutingVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "TRANSPORT_ROUTING")
    public List<TransportRoutingVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<TransportRoutingVO> dataList = transportRoutingDao.selectByCondition(sortParam, queryCriteriaParam);
        TransportRoutingServiceImpl target = springBeanUtils.getBean(TransportRoutingServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<TransportRoutingVO> selectByParams(Map<String, Object> params) {
        List<TransportRoutingPO> list = transportRoutingDao.selectByParams(params);
        return TransportRoutingConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<TransportRoutingVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS) {
        if (CollectionUtils.isEmpty(removeVersionDTOS)) {
            return 0;
        }
        return transportRoutingDao.deleteBatchVersion(removeVersionDTOS);
    }

    @Override
    public List<LabelValue<String>> listRoutingCode(String oemCode) {
        List<TransportRoutingVO> transportRoutingVOS = transportRoutingDao.selectOemTransportRouting(oemCode);
        List<LabelValue<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(transportRoutingVOS)) {
            transportRoutingVOS.stream()
                    .filter(x -> StringUtils.isNotBlank(x.getRoutingCode()))
                    .forEach(x -> {
                        LabelValue<String> labelValue = new LabelValue<>();
                        labelValue.setValue(x.getId());
                        labelValue.setLabel(x.getRoutingName());
                        result.add(labelValue);
                    });
        }
        return result;
    }

    @Override
    public BaseResponse<Void> syncTransportRoutings() {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("reqCode", "FY_PKN_CTN_TYPE_FOR_BPIM");
        params.put("scenario", SystemHolder.getScenario());
        newDcpFeign.callExternalApi(SystemHolder.getTenantCode(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.TRANSPORT_ROUTING.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.TRANSPORT_ROUTING.getCode();
    }

    @Override
    public List<TransportRoutingVO> invocation(List<TransportRoutingVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<TransportRoutingVO> getOemTransportRouting() {
        return transportRoutingDao.selectOemTransportRouting(null);
    }

    @Override
    public BaseResponse<Void> updateTransportRouting(List<MesTransportRouting> mesTransportRoutingsList) {
        if (CollectionUtils.isEmpty(mesTransportRoutingsList)) {
            return BaseResponse.success();
        }
        try {
            List<TransportRoutingVO> transportRoutingVOS = selectAll();
            List<TransportRoutingDetailVO> transportRoutingDetailVOS = transportDetailService.selectAll();
            List<TransportRoutingInterfaceLogVO> transportRoutingInterfaceLogVOS = insertTransportRoutingInterfaceLog(mesTransportRoutingsList);
            insertTransportRouting(transportRoutingInterfaceLogVOS, transportRoutingVOS, transportRoutingDetailVOS);

        } catch (Exception ex) {
            return BaseResponse.error("同步失败" + ex.getMessage());
        }
        return BaseResponse.success();
    }

    private List<TransportRoutingInterfaceLogVO> insertTransportRoutingInterfaceLog(List<MesTransportRouting> o) {
        List<TransportRoutingInterfaceLogVO> transportRoutingInterfaceLogVOS = transportRoutingInterfaceLogService.selectAll();
        Map<String, TransportRoutingInterfaceLogVO> oldTransportRoutingInterfaceLogMap =
                transportRoutingInterfaceLogVOS.stream().collect(Collectors.toMap(TransportRoutingInterfaceLogVO::getRoutingCode,
                        Function.identity(), (v1, v2) -> v1));
        List<TransportRoutingInterfaceLogDTO> insertTransportRoutingInterfaceLogDTOs = Lists.newArrayList();
        List<TransportRoutingInterfaceLogDTO> updateTransportRoutingInterfaceLogDTOs = Lists.newArrayList();
        for (MesTransportRouting mesTransportRouting : o) {
            TransportRoutingInterfaceLogDTO transportRoutingInterfaceLogDTO = new TransportRoutingInterfaceLogDTO();
            String transportRouteCode = mesTransportRouting.getTransportRouteCode();
            String enabled = "Y".equals(mesTransportRouting.getEnableFlag()) ? YesOrNoEnum.YES.getCode() :
                    YesOrNoEnum.NO.getCode();
            if (oldTransportRoutingInterfaceLogMap.containsKey(transportRouteCode)) {
                // 更新
                TransportRoutingInterfaceLogVO transportRoutingInterfaceLog = oldTransportRoutingInterfaceLogMap.get(transportRouteCode);
                BeanUtils.copyProperties(transportRoutingInterfaceLog, transportRoutingInterfaceLogDTO);
                addTransportRoutingInterfaceLog(mesTransportRouting, transportRoutingInterfaceLogDTO, transportRouteCode, enabled);
                updateTransportRoutingInterfaceLogDTOs.add(transportRoutingInterfaceLogDTO);
            } else {
                // 新增
                transportRoutingInterfaceLogDTO.setId(UUIDUtil.getUUID());
                addTransportRoutingInterfaceLog(mesTransportRouting, transportRoutingInterfaceLogDTO, transportRouteCode, enabled);
                insertTransportRoutingInterfaceLogDTOs.add(transportRoutingInterfaceLogDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(insertTransportRoutingInterfaceLogDTOs)) {
            transportRoutingInterfaceLogService.doCreateBatch(insertTransportRoutingInterfaceLogDTOs);
        }
        if (CollectionUtils.isNotEmpty(updateTransportRoutingInterfaceLogDTOs)) {
            transportRoutingInterfaceLogService.doUpdateBatch(updateTransportRoutingInterfaceLogDTOs);
        }
        return transportRoutingInterfaceLogService.selectAll();
    }

    private void addTransportRoutingInterfaceLog(MesTransportRouting mesTransportRouting, TransportRoutingInterfaceLogDTO transportRoutingInterfaceLogDTO, String transportRouteCode, String enabled) {
        transportRoutingInterfaceLogDTO.setRoutingCode(transportRouteCode);
        transportRoutingInterfaceLogDTO.setRoutingName(mesTransportRouting.getTransportRouteName());
        transportRoutingInterfaceLogDTO.setTransportType(mesTransportRouting.getTransportPlanType());
        transportRoutingInterfaceLogDTO.setBusinessUnit(mesTransportRouting.getUnitCode());
        transportRoutingInterfaceLogDTO.setCustomerCode(mesTransportRouting.getCustomerCode());
        transportRoutingInterfaceLogDTO.setCustomerName(mesTransportRouting.getCustomerName());
        transportRoutingInterfaceLogDTO.setCustomerLocation(mesTransportRouting.getCustomerSiteName());
        transportRoutingInterfaceLogDTO.setTransportCountry(mesTransportRouting.getCountryName());
        transportRoutingInterfaceLogDTO.setTransportCity(mesTransportRouting.getCityName());
        transportRoutingInterfaceLogDTO.setTransportMode(mesTransportRouting.getShippingType());
        transportRoutingInterfaceLogDTO.setDeliverAddress(mesTransportRouting.getShipmentPlace());
        transportRoutingInterfaceLogDTO.setReceiverAddress(mesTransportRouting.getObjectivePlace());
        transportRoutingInterfaceLogDTO.setTotalTransportDistance(mesTransportRouting.getHaulDistanceKm());
        transportRoutingInterfaceLogDTO.setTotalTransportTime(mesTransportRouting.getTransportationTime());
        transportRoutingInterfaceLogDTO.setRemark(mesTransportRouting.getRemark());
        transportRoutingInterfaceLogDTO.setSegment(mesTransportRouting.getSegment());
        transportRoutingInterfaceLogDTO.setKid(mesTransportRouting.getKid());
        transportRoutingInterfaceLogDTO.setLastUpdatedBy(mesTransportRouting.getLastUpdatedBy());
        transportRoutingInterfaceLogDTO.setShipmentPort(mesTransportRouting.getShipmentPort());
        transportRoutingInterfaceLogDTO.setObjectivePort(mesTransportRouting.getObjectivePort());
        transportRoutingInterfaceLogDTO.setEnabled(enabled);
        transportRoutingInterfaceLogDTO.setLastUpdateTime(mesTransportRouting.getLastUpdateDate());
    }

    private void insertTransportRouting(List<TransportRoutingInterfaceLogVO> transportRoutingInterfaceLogVOS,
                                        List<TransportRoutingVO> transportRoutingVOS, List<TransportRoutingDetailVO> transportRoutingDetailVOS) {

        // 使用流操作过滤出匹配的项
        List<TransportRoutingInterfaceLogVO> filteredO = transportRoutingInterfaceLogVOS.stream()
                .filter(item -> item.getRoutingCode() != null && transportRoutingDetailVOS.stream()
                        .anyMatch(detail -> detail.getRoutingDetailCode() != null && detail.getRoutingDetailCode().equals(item.getRoutingCode())))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filteredO)) {
            //         // 从原始列表中移除匹配的项
            transportRoutingInterfaceLogVOS.removeAll(filteredO);
            updateAndReconstruct(filteredO, transportRoutingDetailVOS, transportRoutingVOS);
        }
        List<TransportRoutingDTO> transportRoutingDTOS = new ArrayList<>();
        List<TransportRoutingDetailDTO> transportRoutingDetailDTOS = new ArrayList<>();
        generateTransportRouting(transportRoutingDTOS, transportRoutingDetailDTOS, transportRoutingInterfaceLogVOS);

        if (CollectionUtils.isNotEmpty(transportRoutingDTOS)) {
            doCreateBatch(transportRoutingDTOS);
        }

        if (CollectionUtils.isNotEmpty(transportRoutingDetailDTOS)) {
            transportDetailService.doCreateBatch(transportRoutingDetailDTOS);
        }

    }


    private void generateTransportRouting(List<TransportRoutingDTO> transportRoutingDTOS, List<TransportRoutingDetailDTO> transportRoutingDetailDTOS, List<TransportRoutingInterfaceLogVO> tempSegments) {

        List<TransportRoutingInterfaceLogVO> domesticTradeSegments = tempSegments.stream()
                .filter(segment -> !"外贸".equals(segment.getTransportType()))
                .collect(Collectors.toList());

        // 首先过滤出所有“外贸”类型的运输段
        List<TransportRoutingInterfaceLogVO> foreignTradeSegments = tempSegments.stream()
                .filter(segment -> "外贸".equals(segment.getTransportType()))
                .collect(Collectors.toList());

        // 创建一个列表来存储处理过的段
        List<TransportRoutingInterfaceLogVO> processedSegments = new ArrayList<>();

        currentMiddleRearTransportRoutingDetails(transportRoutingDTOS, transportRoutingDetailDTOS,
                foreignTradeSegments, processedSegments);

        middleRearTransportRoutingDetails(transportRoutingDTOS, transportRoutingDetailDTOS, foreignTradeSegments, processedSegments);

        // 处理剩余的未处理的运输段
        for (TransportRoutingInterfaceLogVO segment : foreignTradeSegments) {
            if (!processedSegments.contains(segment)) {
                addTransportRoting(transportRoutingDTOS, transportRoutingDetailDTOS, segment);
            }
        }
        for (TransportRoutingInterfaceLogVO segment : domesticTradeSegments) {
            addTransportRoting(transportRoutingDTOS, transportRoutingDetailDTOS, segment);
        }


    }

    private void middleRearTransportRoutingDetails(List<TransportRoutingDTO> transportRoutingDTOS, List<TransportRoutingDetailDTO> transportRoutingDetailDTOS, List<TransportRoutingInterfaceLogVO> foreignTradeSegments, List<TransportRoutingInterfaceLogVO> processedSegments) {
        // 处理前段和后段的组合
        for (TransportRoutingInterfaceLogVO current : foreignTradeSegments) {
            if (!current.getSegment().equals("前段")) {
                continue; // 跳过已经处理过的段
            }

            for (TransportRoutingInterfaceLogVO rear : foreignTradeSegments) {
                if ( !rear.getSegment().equals("后段")) {
                    continue;
                }

                if (current.getSegment().equals("前段") && rear.getSegment().equals("后段") && current.getReceiverAddress().equals(rear.getDeliverAddress())) {
                    TransportRoutingDTO transportRoutingDTO = createTransportRoutingDTO(current, rear);
                    transportRoutingDTOS.add(transportRoutingDTO);

                    addTransportRoutingDetail(transportRoutingDetailDTOS, current, transportRoutingDTO.getId());
                    addTransportRoutingDetail(transportRoutingDetailDTOS, rear, transportRoutingDTO.getId());

                    // 标记这些段为已处理
                    processedSegments.add(current);
                    processedSegments.add(rear);
                }
            }
        }
    }

    private void currentMiddleRearTransportRoutingDetails(List<TransportRoutingDTO> transportRoutingDTOS,
                                                          List<TransportRoutingDetailDTO> transportRoutingDetailDTOS, List<TransportRoutingInterfaceLogVO> foreignTradeSegments, List<TransportRoutingInterfaceLogVO> processedSegments) {
        // 处理前段、中段和后段的组合
        for (TransportRoutingInterfaceLogVO current : foreignTradeSegments) {
            if (!current.getSegment().equals("前段")) {
                continue; // 跳过已经处理过的段
            }
            for (TransportRoutingInterfaceLogVO middle : foreignTradeSegments) {
                if ( !middle.getSegment().equals("中段")) {
                    continue;
                }

                for (TransportRoutingInterfaceLogVO rear : foreignTradeSegments) {
                    if ( !rear.getSegment().equals("后段")) {
                        continue;
                    }

                    if (current.getSegment().equals("前段") && middle.getSegment().equals("中段") && rear.getSegment().equals(
                            "后段") && current.getReceiverAddress().equals(middle.getDeliverAddress()) && middle.getReceiverAddress().equals(rear.getDeliverAddress())) {
                        TransportRoutingDTO transportRoutingDTO = createTransportRoutingDTO(current, middle, rear);
                        transportRoutingDTOS.add(transportRoutingDTO);

                        addTransportRoutingDetail(transportRoutingDetailDTOS, current, transportRoutingDTO.getId());
                        addTransportRoutingDetail(transportRoutingDetailDTOS, middle, transportRoutingDTO.getId());
                        addTransportRoutingDetail(transportRoutingDetailDTOS, rear, transportRoutingDTO.getId());

                        // 标记这些段为已处理
                        processedSegments.add(current);
                        processedSegments.add(middle);
                        processedSegments.add(rear);
                    }
                }
            }
        }
    }

    // 辅助方法：创建包含前段、中段和后段的 TransportRoutingDTO
    private TransportRoutingDTO createTransportRoutingDTO(TransportRoutingInterfaceLogVO current, TransportRoutingInterfaceLogVO next1, TransportRoutingInterfaceLogVO next2) {
        TransportRoutingDTO transportRoutingDTO = new TransportRoutingDTO();
        transportRoutingDTO.setId(UUIDUtil.getUUID());
        transportRoutingDTO.setRoutingCode(current.getRoutingCode() + "/" + next1.getRoutingCode() + "/" + next2.getRoutingCode());
        transportRoutingDTO.setRoutingName(current.getRoutingName() + "/" + next2.getRoutingName());
        transportRoutingDTO.setTotalTransportDistance(current.getTotalTransportDistance() + next1.getTotalTransportDistance() + next2.getTotalTransportDistance());
        transportRoutingDTO.setTotalTransportTime(current.getTotalTransportTime() + next1.getTotalTransportTime() + next2.getTotalTransportTime());
        transportRoutingDTO.setTransportType(current.getTransportType());
        transportRoutingDTO.setBusinessUnit(current.getBusinessUnit());
        transportRoutingDTO.setCustomerCode(next2.getCustomerCode());
        transportRoutingDTO.setCustomerName(next2.getCustomerName());
        transportRoutingDTO.setCustomerLocation(next2.getCustomerLocation());
        transportRoutingDTO.setTransportCountry(next2.getTransportCountry());
        transportRoutingDTO.setTransportCity(next2.getTransportCity());
        transportRoutingDTO.setTransportMode(next2.getTransportMode());
        transportRoutingDTO.setDeliverAddress(current.getDeliverAddress());
        transportRoutingDTO.setReceiverAddress(next2.getReceiverAddress());
        transportRoutingDTO.setRemark(current.getRemark());
        transportRoutingDTO.setEnabled(
                YesOrNoEnum.NO.getCode().equals(current.getEnabled()) ||
                        YesOrNoEnum.NO.getCode().equals(next1.getEnabled()) ||
                        YesOrNoEnum.NO.getCode().equals(next2.getEnabled()) ?
                        YesOrNoEnum.NO.getCode() :
                        YesOrNoEnum.YES.getCode()
        );
        transportRoutingDTO.setLastUpdateTime(next2.getLastUpdateTime());
        return transportRoutingDTO;
    }


    // 辅助方法：创建包含前段和后段的 TransportRoutingDTO
    private TransportRoutingDTO createTransportRoutingDTO(TransportRoutingInterfaceLogVO current, TransportRoutingInterfaceLogVO next2) {
        TransportRoutingDTO transportRoutingDTO = new TransportRoutingDTO();
        transportRoutingDTO.setId(UUIDUtil.getUUID());
        transportRoutingDTO.setRoutingCode(current.getRoutingCode() + "/" + next2.getRoutingCode());
        transportRoutingDTO.setRoutingName(current.getRoutingName() + "/" + next2.getRoutingName());
        transportRoutingDTO.setTotalTransportDistance(current.getTotalTransportDistance() + next2.getTotalTransportDistance());
        transportRoutingDTO.setTotalTransportTime(current.getTotalTransportTime() + next2.getTotalTransportTime());
        transportRoutingDTO.setTransportType(current.getTransportType());
        transportRoutingDTO.setBusinessUnit(current.getBusinessUnit());
        transportRoutingDTO.setCustomerCode(next2.getCustomerCode());
        transportRoutingDTO.setCustomerName(next2.getCustomerName());
        transportRoutingDTO.setCustomerLocation(next2.getCustomerLocation());
        transportRoutingDTO.setTransportCountry(next2.getTransportCountry());
        transportRoutingDTO.setTransportCity(next2.getTransportCity());
        transportRoutingDTO.setTransportMode(next2.getTransportMode());
        transportRoutingDTO.setDeliverAddress(current.getDeliverAddress());
        transportRoutingDTO.setReceiverAddress(next2.getReceiverAddress());
        transportRoutingDTO.setRemark(current.getRemark());
        transportRoutingDTO.setEnabled(
                YesOrNoEnum.NO.getCode().equals(current.getEnabled()) ||
                        YesOrNoEnum.NO.getCode().equals(next2.getEnabled()) ?
                        YesOrNoEnum.NO.getCode() :
                        YesOrNoEnum.YES.getCode()
        );
        transportRoutingDTO.setLastUpdateTime(next2.getLastUpdateTime());
        return transportRoutingDTO;
    }

    private TransportRoutingDTO updateTransportRoutingDTO(TransportRoutingDetailVO current, TransportRoutingDetailVO next1,
                                                          TransportRoutingDetailVO next2) {
        TransportRoutingDTO transportRoutingDTO = new TransportRoutingDTO();
        transportRoutingDTO.setRoutingCode(current.getRoutingDetailCode() + "/" + next1.getRoutingDetailCode() + "/" + next2.getRoutingDetailCode());
        transportRoutingDTO.setRoutingName(current.getRoutingDetailName() + "/" + next2.getRoutingDetailName());
        transportRoutingDTO.setTotalTransportDistance(current.getTransportDistance() + next1.getTransportDistance() + next2.getTransportDistance());
        transportRoutingDTO.setTotalTransportTime(current.getTransportTime() + next1.getTransportTime() + next2.getTransportTime());
        transportRoutingDTO.setTransportType(current.getTransportType());
        transportRoutingDTO.setBusinessUnit(current.getBusinessUnit());
        transportRoutingDTO.setCustomerCode(next2.getCustomerCode());
        transportRoutingDTO.setCustomerName(next2.getCustomerName());
        transportRoutingDTO.setCustomerLocation(next2.getCustomerLocation());
        transportRoutingDTO.setTransportCountry(next2.getTransportCountry());
        transportRoutingDTO.setTransportCity(next2.getTransportCity());
        transportRoutingDTO.setTransportMode(next2.getTransportMode());
        transportRoutingDTO.setDeliverAddress(current.getDeliverAddress());
        transportRoutingDTO.setReceiverAddress(next2.getReceiverAddress());
        transportRoutingDTO.setRemark(current.getRemark());
        transportRoutingDTO.setEnabled(
                YesOrNoEnum.NO.getCode().equals(current.getEnabled()) ||
                        YesOrNoEnum.NO.getCode().equals(next1.getEnabled()) ||
                        YesOrNoEnum.NO.getCode().equals(next2.getEnabled()) ?
                        YesOrNoEnum.NO.getCode() :
                        YesOrNoEnum.YES.getCode()
        );
        transportRoutingDTO.setLastUpdateTime(next2.getLastUpdateTime());
        return transportRoutingDTO;
    }

    private TransportRoutingDTO updateTransportRoutingDTO(TransportRoutingDetailVO current) {
        TransportRoutingDTO transportRoutingDTO = new TransportRoutingDTO();
        transportRoutingDTO.setId(UUIDUtil.getUUID());
        transportRoutingDTO.setRoutingCode(current.getRoutingDetailCode());
        transportRoutingDTO.setRoutingName(current.getRoutingDetailName());
        transportRoutingDTO.setTotalTransportDistance(current.getTransportDistance());
        transportRoutingDTO.setTotalTransportTime(current.getTransportTime());
        transportRoutingDTO.setTransportType(current.getTransportType());
        transportRoutingDTO.setBusinessUnit(current.getBusinessUnit());
        transportRoutingDTO.setCustomerCode(current.getCustomerCode());
        transportRoutingDTO.setCustomerName(current.getCustomerName());
        transportRoutingDTO.setCustomerLocation(current.getCustomerLocation());
        transportRoutingDTO.setTransportCountry(current.getTransportCountry());
        transportRoutingDTO.setTransportCity(current.getTransportCity());
        transportRoutingDTO.setTransportMode(current.getTransportMode());
        transportRoutingDTO.setDeliverAddress(current.getDeliverAddress());
        transportRoutingDTO.setReceiverAddress(current.getReceiverAddress());
        transportRoutingDTO.setRemark(current.getRemark());
        transportRoutingDTO.setEnabled(
                YesOrNoEnum.NO.getCode().equals(current.getEnabled()) ?
                        YesOrNoEnum.NO.getCode() :
                        YesOrNoEnum.YES.getCode()
        );
        transportRoutingDTO.setLastUpdateTime(current.getLastUpdateTime());
        return transportRoutingDTO;
    }

    private TransportRoutingDTO updateTransportRoutingDTO(TransportRoutingDetailVO current, TransportRoutingDetailVO next2) {
        TransportRoutingDTO transportRoutingDTO = new TransportRoutingDTO();
        transportRoutingDTO.setId(UUIDUtil.getUUID());
        transportRoutingDTO.setRoutingCode(current.getRoutingDetailCode() + "/" + next2.getRoutingDetailCode());
        transportRoutingDTO.setRoutingName(current.getRoutingDetailName() + "/" + next2.getRoutingDetailName());
        transportRoutingDTO.setTotalTransportDistance(current.getTransportDistance() + next2.getTransportDistance());
        transportRoutingDTO.setTotalTransportTime(current.getTransportTime() + next2.getTransportTime());
        transportRoutingDTO.setTransportType(current.getTransportType());
        transportRoutingDTO.setBusinessUnit(current.getBusinessUnit());
        transportRoutingDTO.setCustomerCode(next2.getCustomerCode());
        transportRoutingDTO.setCustomerName(next2.getCustomerName());
        transportRoutingDTO.setCustomerLocation(next2.getCustomerLocation());
        transportRoutingDTO.setTransportCountry(next2.getTransportCountry());
        transportRoutingDTO.setTransportCity(next2.getTransportCity());
        transportRoutingDTO.setTransportMode(next2.getTransportMode());
        transportRoutingDTO.setDeliverAddress(current.getDeliverAddress());
        transportRoutingDTO.setReceiverAddress(next2.getReceiverAddress());
        transportRoutingDTO.setRemark(current.getRemark());
        transportRoutingDTO.setEnabled(
                YesOrNoEnum.NO.getCode().equals(current.getEnabled()) ||
                        YesOrNoEnum.NO.getCode().equals(next2.getEnabled()) ?
                        YesOrNoEnum.NO.getCode() :
                        YesOrNoEnum.YES.getCode()
        );
        transportRoutingDTO.setLastUpdateTime(next2.getLastUpdateTime());
        return transportRoutingDTO;
    }

    private void addTransportRoting(List<TransportRoutingDTO> transportRoutingDTOS, List<TransportRoutingDetailDTO> transportRoutingDetailDTOS, TransportRoutingInterfaceLogVO current) {
        TransportRoutingDTO transportRoutingDTO = new TransportRoutingDTO();
        transportRoutingDTO.setId(UUIDUtil.getUUID());
        transportRoutingDTO.setRoutingCode(current.getRoutingCode());
        transportRoutingDTO.setRoutingName(current.getRoutingName());
        transportRoutingDTO.setTotalTransportDistance(current.getTotalTransportDistance());
        transportRoutingDTO.setTotalTransportTime(current.getTotalTransportTime());
        transportRoutingDTO.setTransportType(current.getTransportType());
        transportRoutingDTO.setBusinessUnit(current.getBusinessUnit());
        transportRoutingDTO.setCustomerCode(current.getCustomerCode());
        transportRoutingDTO.setCustomerName(current.getCustomerName());
        transportRoutingDTO.setCustomerLocation(current.getCustomerLocation());
        transportRoutingDTO.setTransportCountry(current.getTransportCountry());
        transportRoutingDTO.setTransportCity(current.getTransportCity());
        transportRoutingDTO.setTransportMode(current.getTransportMode());
        transportRoutingDTO.setDeliverAddress(current.getDeliverAddress());
        transportRoutingDTO.setReceiverAddress(current.getReceiverAddress());
        transportRoutingDTO.setRemark(current.getRemark());
        transportRoutingDTO.setEnabled(current.getEnabled());
        transportRoutingDTO.setLastUpdateTime(current.getLastUpdateTime());
        transportRoutingDTOS.add(transportRoutingDTO);
        addTransportRoutingDetail(transportRoutingDetailDTOS, current, transportRoutingDTO.getId());
    }

    private void addTransportRoutingDetail(List<TransportRoutingDetailDTO> transportRoutingDetailDTOS,
                                           TransportRoutingInterfaceLogVO current, String transportRoutingId) {
        TransportRoutingDetailDTO transportRoutingDetailDTO = new TransportRoutingDetailDTO();
        transportRoutingDetailDTO.setId(UUIDUtil.getUUID());
        transportRoutingDetailDTO.setRoutingId(transportRoutingId);
        transportRoutingDetailDTO.setRoutingDetailCode(current.getRoutingCode());
        transportRoutingDetailDTO.setRoutingDetailName(current.getRoutingName());
        transportRoutingDetailDTO.setTransportType(current.getTransportType());
        transportRoutingDetailDTO.setTransportSegment(current.getSegment());
        transportRoutingDetailDTO.setBusinessUnit(current.getBusinessUnit());
        transportRoutingDetailDTO.setCustomerName(current.getCustomerName());
        transportRoutingDetailDTO.setCustomerLocation(current.getCustomerLocation());
        transportRoutingDetailDTO.setCustomerCode(current.getCustomerCode());
        transportRoutingDetailDTO.setLastUpdateTime(current.getLastUpdateTime());
        transportRoutingDetailDTO.setTransportCountry(current.getTransportCountry());
        transportRoutingDetailDTO.setTransportCity(current.getTransportCity());
        transportRoutingDetailDTO.setTransportMode(current.getTransportMode());
        transportRoutingDetailDTO.setTransportDistance(current.getTotalTransportDistance());
        transportRoutingDetailDTO.setTransportTime(current.getTotalTransportTime());
        transportRoutingDetailDTO.setDeliverAddress(current.getDeliverAddress());
        transportRoutingDetailDTO.setReceiverAddress(current.getReceiverAddress());
        transportRoutingDetailDTO.setRemark(current.getRemark());
        transportRoutingDetailDTO.setKid(current.getKid());
        transportRoutingDetailDTO.setEnabled(current.getEnabled());
        transportRoutingDetailDTOS.add(transportRoutingDetailDTO);
    }

    public void updateAndReconstruct(List<TransportRoutingInterfaceLogVO> filteredO,
                                     List<TransportRoutingDetailVO> transportRoutingDetailVOS, List<TransportRoutingVO> transportRoutingVOS) {
        // Step 1: 更新 transportRoutingDetailVOS 中的值
        List<TransportRoutingDetailDTO> transportRoutingDetailDTOS = new ArrayList<>();
        List<TransportRoutingDTO> transportRoutingDTOS = new ArrayList<>();

        transportRoutingDetailDTOSList(filteredO, transportRoutingDetailVOS, transportRoutingDetailDTOS);
        if (CollectionUtils.isNotEmpty(transportRoutingDetailDTOS)) {
            transportDetailService.doUpdateBatch(transportRoutingDetailDTOS);
        }
        // 创建一个映射，用于快速查找 filteredO 中的项
        Set<String> filteredIds = transportRoutingDetailDTOS.stream()
                .map(TransportRoutingDetailDTO::getRoutingId)
                .collect(Collectors.toSet());
        // Step 2: 找到 transportRoutingDetailVOS 和 transportRoutingVOS 中具有相同 id 的项
        Map<String, List<TransportRoutingDetailVO>> detailMap = transportRoutingDetailVOS.stream()
                .collect(Collectors.groupingBy(TransportRoutingDetailVO::getRoutingId));

        transportRoutingDTOSList(transportRoutingVOS, transportRoutingDTOS, filteredIds, detailMap);
        if (CollectionUtils.isNotEmpty(transportRoutingDTOS)) {
            doUpdateBatch(transportRoutingDTOS);
        }
    }

    private void transportRoutingDTOSList(List<TransportRoutingVO> transportRoutingVOS, List<TransportRoutingDTO> transportRoutingDTOS, Set<String> filteredIds, Map<String, List<TransportRoutingDetailVO>> detailMap) {
        // Step 3: 根据 id 更新 transportRoutingVOS 中的值
        for (TransportRoutingVO routing : transportRoutingVOS) {
            String id = routing.getId();
            List<TransportRoutingDetailVO> details = detailMap.get(id);
            if (filteredIds.contains(id) && details != null && !details.isEmpty()) {
                TransportRoutingDetailVO current = null;
                TransportRoutingDetailVO transportRoutingDetailVO = null;
                TransportRoutingDetailVO middle = null;
                TransportRoutingDetailVO rear = null;
                for (TransportRoutingDetailVO detail : details) {
                    transportRoutingDetailVO = detail;
                    if (detail.getTransportSegment() != null) {
                        switch (detail.getTransportSegment()) {
                            case "前段":
                                current = detail;
                                break;
                            case "中段":
                                middle = detail;
                                break;
                            case "后段":
                                rear = detail;
                                break;
                            default:
                                break;
                        }
                    }

                }
                TransportRoutingDTO transportRoutingDTO = new TransportRoutingDTO();
                if (current != null && rear != null && middle != null) {
                    transportRoutingDTO = updateTransportRoutingDTO(current, middle, rear);
                } else if (current != null && rear != null && middle == null) {
                    transportRoutingDTO = updateTransportRoutingDTO(current, rear);
                } else {
                    transportRoutingDTO = updateTransportRoutingDTO(transportRoutingDetailVO);
                }
                transportRoutingDTO.setId(id);
                transportRoutingDTOS.add(transportRoutingDTO);

            }
        }
    }

    private void transportRoutingDetailDTOSList(List<TransportRoutingInterfaceLogVO> filteredO, List<TransportRoutingDetailVO> transportRoutingDetailVOS, List<TransportRoutingDetailDTO> transportRoutingDetailDTOS) {
        for (TransportRoutingInterfaceLogVO mesRouting : filteredO) {
            String routingDetailCode = mesRouting.getRoutingCode();

            for (int i = 0; i < transportRoutingDetailVOS.size(); i++) {
                TransportRoutingDetailVO detail = transportRoutingDetailVOS.get(i);
                if (detail.getRoutingDetailCode().equals(routingDetailCode)) {
                    TransportRoutingDetailDTO transportRoutingDetailDTO = new TransportRoutingDetailDTO();
                    transportRoutingDetailDTO.setId(detail.getId());
                    transportRoutingDetailDTO.setRoutingId(detail.getRoutingId());
                    transportRoutingDetailDTO.setRoutingDetailCode(mesRouting.getRoutingCode());
                    transportRoutingDetailDTO.setRoutingDetailName(mesRouting.getRoutingName());
                    transportRoutingDetailDTO.setTransportType(mesRouting.getTransportType());
                    transportRoutingDetailDTO.setTransportSegment(mesRouting.getSegment());
                    transportRoutingDetailDTO.setBusinessUnit(mesRouting.getBusinessUnit());
                    transportRoutingDetailDTO.setCustomerName(mesRouting.getCustomerName());
                    transportRoutingDetailDTO.setCustomerLocation(mesRouting.getCustomerLocation());
                     transportRoutingDetailDTO.setCustomerCode(mesRouting.getCustomerCode());
                    transportRoutingDetailDTO.setTransportCountry(mesRouting.getTransportCountry());
                    transportRoutingDetailDTO.setLastUpdateTime(mesRouting.getLastUpdateTime());
                    transportRoutingDetailDTO.setTransportCity(mesRouting.getTransportCity());
                    transportRoutingDetailDTO.setTransportMode(mesRouting.getTransportMode());
                    transportRoutingDetailDTO.setTransportDistance(mesRouting.getTotalTransportDistance());
                    transportRoutingDetailDTO.setTransportTime(mesRouting.getTotalTransportTime());
                    transportRoutingDetailDTO.setDeliverAddress(mesRouting.getDeliverAddress());
                    transportRoutingDetailDTO.setReceiverAddress(mesRouting.getReceiverAddress());
                    transportRoutingDetailDTO.setKid(mesRouting.getKid());
                    transportRoutingDetailDTO.setRemark(mesRouting.getRemark());
                    transportRoutingDetailDTO.setEnabled(mesRouting.getEnabled());

                    detail.setRoutingDetailCode(mesRouting.getRoutingCode());
                    detail.setRoutingDetailName(mesRouting.getRoutingName());
                    detail.setTransportType(mesRouting.getTransportType());
                    detail.setTransportSegment(mesRouting.getSegment());
                    detail.setBusinessUnit(mesRouting.getBusinessUnit());
                    detail.setCustomerName(mesRouting.getCustomerName());
                    detail.setCustomerLocation(mesRouting.getCustomerLocation());
                    detail.setLastUpdateTime(mesRouting.getLastUpdateTime());
                    detail.setCustomerCode(mesRouting.getCustomerCode());
                    detail.setTransportCountry(mesRouting.getTransportCountry());
                    detail.setTransportCity(mesRouting.getTransportCity());
                    detail.setTransportMode(mesRouting.getTransportMode());
                    detail.setTransportDistance(mesRouting.getTotalTransportDistance());
                    detail.setTransportTime(mesRouting.getTotalTransportTime());
                    detail.setDeliverAddress(mesRouting.getDeliverAddress());
                    detail.setReceiverAddress(mesRouting.getReceiverAddress());
                    detail.setRemark(mesRouting.getRemark());
                    detail.setEnabled(mesRouting.getEnabled());
                    transportRoutingDetailVOS.set(i, detail);
                    transportRoutingDetailDTOS.add(transportRoutingDetailDTO);
                }
            }
        }
    }

    @Override
    public List<TransportRoutingVO> selectVOByParams(Map<String, Object> params) {
        return transportRoutingDao.selectVOByParams(params);
    }

	@Override
	public List<LabelValue<String>> allRoutingCodeDropDown() {
		List<TransportRoutingPO> transportRoutingList = transportRoutingDao.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode()));
		if (CollectionUtils.isNotEmpty(transportRoutingList)) {
            	List<LabelValue<String>> list = transportRoutingList.stream()
            			.map(x -> new LabelValue<>(x.getRoutingName(), x.getRoutingCode()))
            			.collect(Collectors.toList());
            	return removeDuplicates(list);
    	}
        return Collections.emptyList();
	}
	
	private List<LabelValue<String>> removeDuplicates(List<LabelValue<String>> list) {
        return list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                lv -> Arrays.asList(lv.getLabel(), lv.getValue()), // 使用 label 和 value 作为键
                                lv -> lv, // 值为 LabelValue 对象
                                (existing, replacement) -> existing // 如果有重复，保留第一个出现的
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }
}
