package com.yhl.scp.dfp.delivery.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanCalcReportDTO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanCalcReportService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanCalcReportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>DeliveryPlanCalcReportController</code>
 * <p>
 * 发货计划计算数据汇总报表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:00:52
 */
@Slf4j
@Api(tags = "发货计划计算数据汇总报表控制器")
@RestController
@RequestMapping("deliveryPlanCalcReport")
public class DeliveryPlanCalcReportController extends BaseController {

    @Resource
    private DeliveryPlanCalcReportService deliveryPlanCalcReportService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<DeliveryPlanCalcReportVO>> page() {
        List<DeliveryPlanCalcReportVO> deliveryPlanCalcReportList = deliveryPlanCalcReportService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<DeliveryPlanCalcReportVO> pageInfo = new PageInfo<>(deliveryPlanCalcReportList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody DeliveryPlanCalcReportDTO deliveryPlanCalcReportDTO) {
        return deliveryPlanCalcReportService.doCreate(deliveryPlanCalcReportDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody DeliveryPlanCalcReportDTO deliveryPlanCalcReportDTO) {
        return deliveryPlanCalcReportService.doUpdate(deliveryPlanCalcReportDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        deliveryPlanCalcReportService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<DeliveryPlanCalcReportVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryPlanCalcReportService.selectByPrimaryKey(id));
    }

}
