<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.stock.infrastructure.dao.RealtimeInventoryBatchDetailDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.stock.infrastructure.po.RealtimeInventoryBatchDetailPO">
        <!--@Table fdp_realtime_inventory_batch_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="current_quantity" jdbcType="VARCHAR" property="currentQuantity"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="freight_space" jdbcType="VARCHAR" property="freightSpace"/>
        <result column="freight_space_description" jdbcType="VARCHAR" property="freightSpaceDescription"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="original_org_id" jdbcType="VARCHAR" property="originalOrgId"/>
        <result column="original_product_code" jdbcType="VARCHAR" property="originalProductCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="subinventory" jdbcType="VARCHAR" property="subinventory"/>
        <result column="subinventory_description" jdbcType="VARCHAR" property="subinventoryDescription"/>
        <result column="sync_user_id" jdbcType="VARCHAR" property="syncUserId"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.stock.vo.RealtimeInventoryBatchDetailVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,create_time,creator,current_quantity,enabled,freight_space,freight_space_description,modifier,modify_time,operation_code,original_org_id,original_product_code,product_code,remark,stock_point_code,subinventory,subinventory_description,sync_user_id,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.currentQuantity != null and params.currentQuantity != ''">
                and current_quantity = #{params.currentQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.freightSpace != null and params.freightSpace != ''">
                and freight_space = #{params.freightSpace,jdbcType=VARCHAR}
            </if>
            <if test="params.freightSpaceDescription != null and params.freightSpaceDescription != ''">
                and freight_space_description = #{params.freightSpaceDescription,jdbcType=VARCHAR}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.operationCode != null and params.operationCode != ''">
                and operation_code = #{params.operationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.originalOrgId != null and params.originalOrgId != ''">
                and original_org_id = #{params.originalOrgId,jdbcType=VARCHAR}
            </if>
            <if test="params.originalProductCode != null and params.originalProductCode != ''">
                and original_product_code = #{params.originalProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.subinventory != null and params.subinventory != ''">
                and subinventory = #{params.subinventory,jdbcType=VARCHAR}
            </if>
            <if test="params.subinventoryDescription != null and params.subinventoryDescription != ''">
                and subinventory_description = #{params.subinventoryDescription,jdbcType=VARCHAR}
            </if>
            <if test="params.syncUserId != null and params.syncUserId != ''">
                and sync_user_id = #{params.syncUserId,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_realtime_inventory_batch_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_realtime_inventory_batch_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from fdp_realtime_inventory_batch_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_realtime_inventory_batch_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.RealtimeInventoryBatchDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_realtime_inventory_batch_detail(
        id,
        create_time,
        creator,
        current_quantity,
        enabled,
        freight_space,
        freight_space_description,
        modifier,
        modify_time,
        operation_code,
        original_org_id,
        original_product_code,
        product_code,
        remark,
        stock_point_code,
        subinventory,
        subinventory_description,
        sync_user_id,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=VARCHAR},
        #{currentQuantity,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{freightSpace,jdbcType=VARCHAR},
        #{freightSpaceDescription,jdbcType=VARCHAR},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{operationCode,jdbcType=VARCHAR},
        #{originalOrgId,jdbcType=VARCHAR},
        #{originalProductCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{subinventory,jdbcType=VARCHAR},
        #{subinventoryDescription,jdbcType=VARCHAR},
        #{syncUserId,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.dfp.stock.infrastructure.po.RealtimeInventoryBatchDetailPO">
        insert into fdp_realtime_inventory_batch_detail(
        id,
        create_time,
        creator,
        current_quantity,
        enabled,
        freight_space,
        freight_space_description,
        modifier,
        modify_time,
        operation_code,
        original_org_id,
        original_product_code,
        product_code,
        remark,
        stock_point_code,
        subinventory,
        subinventory_description,
        sync_user_id,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=VARCHAR},
        #{currentQuantity,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{freightSpace,jdbcType=VARCHAR},
        #{freightSpaceDescription,jdbcType=VARCHAR},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{operationCode,jdbcType=VARCHAR},
        #{originalOrgId,jdbcType=VARCHAR},
        #{originalProductCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{subinventory,jdbcType=VARCHAR},
        #{subinventoryDescription,jdbcType=VARCHAR},
        #{syncUserId,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_realtime_inventory_batch_detail(
        id,
        create_time,
        creator,
        current_quantity,
        enabled,
        freight_space,
        freight_space_description,
        modifier,
        modify_time,
        operation_code,
        original_org_id,
        original_product_code,
        product_code,
        remark,
        stock_point_code,
        subinventory,
        subinventory_description,
        sync_user_id,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.currentQuantity,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.freightSpace,jdbcType=VARCHAR},
            #{entity.freightSpaceDescription,jdbcType=VARCHAR},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.operationCode,jdbcType=VARCHAR},
            #{entity.originalOrgId,jdbcType=VARCHAR},
            #{entity.originalProductCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.subinventory,jdbcType=VARCHAR},
            #{entity.subinventoryDescription,jdbcType=VARCHAR},
            #{entity.syncUserId,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_realtime_inventory_batch_detail(
        id,
        create_time,
        creator,
        current_quantity,
        enabled,
        freight_space,
        freight_space_description,
        modifier,
        modify_time,
        operation_code,
        original_org_id,
        original_product_code,
        product_code,
        remark,
        stock_point_code,
        subinventory,
        subinventory_description,
        sync_user_id,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.currentQuantity,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.freightSpace,jdbcType=VARCHAR},
            #{entity.freightSpaceDescription,jdbcType=VARCHAR},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.operationCode,jdbcType=VARCHAR},
            #{entity.originalOrgId,jdbcType=VARCHAR},
            #{entity.originalProductCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.subinventory,jdbcType=VARCHAR},
            #{entity.subinventoryDescription,jdbcType=VARCHAR},
            #{entity.syncUserId,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.RealtimeInventoryBatchDetailPO">
        update fdp_realtime_inventory_batch_detail set
        current_quantity = #{currentQuantity,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        freight_space = #{freightSpace,jdbcType=VARCHAR},
        freight_space_description = #{freightSpaceDescription,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        operation_code = #{operationCode,jdbcType=VARCHAR},
        original_org_id = #{originalOrgId,jdbcType=VARCHAR},
        original_product_code = #{originalProductCode,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
        subinventory = #{subinventory,jdbcType=VARCHAR},
        subinventory_description = #{subinventoryDescription,jdbcType=VARCHAR},
        sync_user_id = #{syncUserId,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.RealtimeInventoryBatchDetailPO">
        update fdp_realtime_inventory_batch_detail
        <set>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.currentQuantity != null and item.currentQuantity != ''">
                current_quantity = #{item.currentQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpace != null and item.freightSpace != ''">
                freight_space = #{item.freightSpace,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpaceDescription != null and item.freightSpaceDescription != ''">
                freight_space_description = #{item.freightSpaceDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.originalOrgId != null and item.originalOrgId != ''">
                original_org_id = #{item.originalOrgId,jdbcType=VARCHAR},
            </if>
            <if test="item.originalProductCode != null and item.originalProductCode != ''">
                original_product_code = #{item.originalProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.subinventory != null and item.subinventory != ''">
                subinventory = #{item.subinventory,jdbcType=VARCHAR},
            </if>
            <if test="item.subinventoryDescription != null and item.subinventoryDescription != ''">
                subinventory_description = #{item.subinventoryDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.syncUserId != null and item.syncUserId != ''">
                sync_user_id = #{item.syncUserId,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_realtime_inventory_batch_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="current_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currentQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="freight_space = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.freightSpace,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="freight_space_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.freightSpaceDescription,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="operation_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="original_org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originalOrgId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="original_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originalProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="subinventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.subinventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="subinventory_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.subinventoryDescription,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sync_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.syncUserId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_realtime_inventory_batch_detail
            <set>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.currentQuantity != null and item.currentQuantity != ''">
                    current_quantity = #{item.currentQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.freightSpace != null and item.freightSpace != ''">
                    freight_space = #{item.freightSpace,jdbcType=VARCHAR},
                </if>
                <if test="item.freightSpaceDescription != null and item.freightSpaceDescription != ''">
                    freight_space_description = #{item.freightSpaceDescription,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.operationCode != null and item.operationCode != ''">
                    operation_code = #{item.operationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.originalOrgId != null and item.originalOrgId != ''">
                    original_org_id = #{item.originalOrgId,jdbcType=VARCHAR},
                </if>
                <if test="item.originalProductCode != null and item.originalProductCode != ''">
                    original_product_code = #{item.originalProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.subinventory != null and item.subinventory != ''">
                    subinventory = #{item.subinventory,jdbcType=VARCHAR},
                </if>
                <if test="item.subinventoryDescription != null and item.subinventoryDescription != ''">
                    subinventory_description = #{item.subinventoryDescription,jdbcType=VARCHAR},
                </if>
                <if test="item.syncUserId != null and item.syncUserId != ''">
                    sync_user_id = #{item.syncUserId,jdbcType=VARCHAR},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from fdp_realtime_inventory_batch_detail where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_realtime_inventory_batch_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <!-- 根据组织ID列表删除数据 -->
    <delete id="doDeleteAllByOrgIds" parameterType="java.util.List">
        delete from fdp_realtime_inventory_batch_detail 
        where original_org_id in
        <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
            #{orgId,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <!-- 根据用户ID删除数据 -->
    <delete id="doDeleteAllByUserId" parameterType="java.lang.String">
        delete from fdp_realtime_inventory_batch_detail
        where sync_user_id =#{userId,jdbcType=VARCHAR}
    </delete>
</mapper>
