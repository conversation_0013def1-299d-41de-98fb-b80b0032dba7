package com.yhl.scp.dfp.transport.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.transport.dto.TransportRoutingDTO;
import com.yhl.scp.dfp.transport.service.TransportRoutingInterfaceLogService;
import com.yhl.scp.dfp.transport.service.TransportRoutingService;
import com.yhl.scp.dfp.transport.vo.TransportRoutingVO;
import com.yhl.scp.ips.common.SystemHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>TransportRoutingController</code>
 * <p>
 * 运输路径控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 22:19:46
 */
@Slf4j
@Api(tags = "运输路径控制器")
@RestController
@RequestMapping("transportRouting")
public class TransportRoutingController extends BaseController {

    @Resource
    private TransportRoutingService transportRoutingService;

    @Resource
    private TransportRoutingInterfaceLogService transportRoutingInterfaceLogService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<TransportRoutingVO>> page() {
        List<TransportRoutingVO> transportRoutingList = transportRoutingService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<TransportRoutingVO> pageInfo = new PageInfo<>(transportRoutingList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody TransportRoutingDTO transportRoutingDTO) {
        return transportRoutingService.doCreate(transportRoutingDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody TransportRoutingDTO transportRoutingDTO) {
        return transportRoutingService.doUpdate(transportRoutingDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<RemoveVersionDTO> removeVersionDTOS) {
        transportRoutingService.deleteBatchVersion(removeVersionDTOS);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<TransportRoutingVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, transportRoutingService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "路径编码下拉")
    @GetMapping(value = "routingCode/dropDown")
    public BaseResponse<List<LabelValue<String>>> routingCodeDropDown(@RequestParam(name = "oemCode",required = false) String oemCode) {
        List<LabelValue<String>> result = transportRoutingService.listRoutingCode(oemCode);
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "运输路径同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncTransportRoutings() {
        return transportRoutingInterfaceLogService.syncTransportRoutings(SystemHolder.getTenantCode(),SystemHolder.getScenario());
    }
    
    @ApiOperation(value = "路径编码下拉（所有）")
    @GetMapping(value = "allRoutingCode/dropDown")
    public BaseResponse<List<LabelValue<String>>> allRoutingCodeDropDown() {
        List<LabelValue<String>> result = transportRoutingService.allRoutingCodeDropDown();
        return BaseResponse.success(result);
    }
}
