package com.yhl.scp.dfp.delivery.domain.service;

import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanCalcReportDO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanCalcReportDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>DeliveryPlanCalcReportDomainService</code>
 * <p>
 * 发货计划计算数据汇总报表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:00:52
 */
@Service
public class DeliveryPlanCalcReportDomainService {

    @Resource
    private DeliveryPlanCalcReportDao deliveryPlanCalcReportDao;

    /**
     * 数据校验
     *
     * @param deliveryPlanCalcReportDO 领域对象
     */
    public void validation(DeliveryPlanCalcReportDO deliveryPlanCalcReportDO) {
        checkNotNull(deliveryPlanCalcReportDO);
        checkUniqueCode(deliveryPlanCalcReportDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param deliveryPlanCalcReportDO 领域对象
     */
    private void checkNotNull(DeliveryPlanCalcReportDO deliveryPlanCalcReportDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param deliveryPlanCalcReportDO 领域对象
     */
    private void checkUniqueCode(DeliveryPlanCalcReportDO deliveryPlanCalcReportDO) {
        // TODO
    }

}
