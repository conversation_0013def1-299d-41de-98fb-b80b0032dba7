package com.yhl.scp.dfp.stock.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.stock.infrastructure.po.RealtimeInventoryBatchDetailPO;
import com.yhl.scp.dfp.stock.vo.RealtimeInventoryBatchDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>RealtimeInventoryBatchDetailDao</code>
 * <p>
 * 实时库存批次明细DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:52:25
 */
public interface RealtimeInventoryBatchDetailDao extends BaseDao<RealtimeInventoryBatchDetailPO, RealtimeInventoryBatchDetailVO> {

    /**
     * 根据组织ID列表删除数据
     *
     * @param orgIds 组织ID列表
     * @return 删除的记录数
     */
    int doDeleteAllByOrgIds(@Param("orgIds") List<String> orgIds);
    int doDeleteAllByUserId(@Param("userId") String userId);

}
