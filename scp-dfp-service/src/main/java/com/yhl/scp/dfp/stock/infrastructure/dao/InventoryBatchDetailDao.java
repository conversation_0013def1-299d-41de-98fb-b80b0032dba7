package com.yhl.scp.dfp.stock.infrastructure.dao;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.stock.infrastructure.po.InventoryBatchDetailPO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>InventoryBatchDetailDao</code>
 * <p>
 * 库存批次明细DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 09:53:39
 */
public interface InventoryBatchDetailDao extends BaseDao<InventoryBatchDetailPO, InventoryBatchDetailVO> {

    List<InventoryBatchDetailVO> selectByProductCodes(@Param("productCodes") List<String> productCodes,
                                                      @Param("stockPointType") String stockPointType);

	List<InventoryBatchDetailVO> selectByParamMap(@Param("params") Map<String, Object> params);

    int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS);

    int deleteAll(@Param("sourceType") String sourceType);
    int deleteAllByOrgId(@Param("sourceType") String sourceType,@Param("originalOrgId") String originalOrgId);
    int doDeleteAllByOrgIds(@Param("sourceType") String sourceType,@Param("orgIds") List<String> orgIds);

    List<InventoryBatchDetailVO> selectRealTimeInventory(@Param("productCodes") List<String> productCodes);

	List<InventoryBatchDetailVO> selectCollectGroupByLoadingPosit();

	List<InventoryBatchDetailVO> selectCollectGroupByNoLoadingPosit();

    List<InventoryBatchDetailVO> selectByVOParams(@Param("params") Map<String, Object> params);

    List<InventoryBatchDetailVO> selectAllGlassInventoryBatch();

    List<InventoryBatchDetailVO> selectByVOParams02(@Param("params") Map<String, Object> params);

    List<InventoryBatchDetailVO> selectByConditionOurFactory(@Param("sortParam")String sortParam, @Param("queryCriteriaParam")String queryCriteriaParam);

	List<InventoryBatchDetailVO> selectStatisticsInventory(@Param("productCodes")List<String> productCodes, 
			@Param("stockPointCodes")List<String> stockPointCodes);

    List<InventoryBatchDetailVO> selectVOColumnByParams(@Param("params") Map<String, Object> params);

}
