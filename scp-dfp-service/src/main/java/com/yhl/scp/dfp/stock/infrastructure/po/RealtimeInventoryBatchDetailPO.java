package com.yhl.scp.dfp.stock.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>RealtimeInventoryBatchDetailPO</code>
 * <p>
 * 实时库存批次明细PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:52:25
 */
public class RealtimeInventoryBatchDetailPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 259707298367904073L;

    /**
     * 现有量
     */
    private String currentQuantity;
    /**
     * 货位
     */
    private String freightSpace;
    /**
     * 货位描述
     */
    private String freightSpaceDescription;
    /**
     * 工序
     */
    private String operationCode;
    /**
     * 原始报文组织ID
     */
    private String originalOrgId;
    /**
     * 原始物料编码
     */
    private String originalProductCode;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 子库存
     */
    private String subinventory;
    /**
     * 子库存描述
     */
    private String subinventoryDescription;
    /**
     * 同步人
     */
    private String syncUserId;
    /**
     * 版本号
     */
    private Integer versionValue;

    public String getCurrentQuantity() {
        return currentQuantity;
    }

    public void setCurrentQuantity(String currentQuantity) {
        this.currentQuantity = currentQuantity;
    }

    public String getFreightSpace() {
        return freightSpace;
    }

    public void setFreightSpace(String freightSpace) {
        this.freightSpace = freightSpace;
    }

    public String getFreightSpaceDescription() {
        return freightSpaceDescription;
    }

    public void setFreightSpaceDescription(String freightSpaceDescription) {
        this.freightSpaceDescription = freightSpaceDescription;
    }

    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public String getOriginalOrgId() {
        return originalOrgId;
    }

    public void setOriginalOrgId(String originalOrgId) {
        this.originalOrgId = originalOrgId;
    }

    public String getOriginalProductCode() {
        return originalProductCode;
    }

    public void setOriginalProductCode(String originalProductCode) {
        this.originalProductCode = originalProductCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getStockPointCode() {
        return stockPointCode;
    }

    public void setStockPointCode(String stockPointCode) {
        this.stockPointCode = stockPointCode;
    }

    public String getSubinventory() {
        return subinventory;
    }

    public void setSubinventory(String subinventory) {
        this.subinventory = subinventory;
    }

    public String getSubinventoryDescription() {
        return subinventoryDescription;
    }

    public void setSubinventoryDescription(String subinventoryDescription) {
        this.subinventoryDescription = subinventoryDescription;
    }

    public String getSyncUserId() {
        return syncUserId;
    }

    public void setSyncUserId(String syncUserId) {
        this.syncUserId = syncUserId;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
