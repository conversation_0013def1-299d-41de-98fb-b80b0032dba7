package com.yhl.scp.dfp.stock.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.delivery.vo.RealTimeInventoryVO;
import com.yhl.scp.dfp.stock.dto.InventoryDataDTO;
import com.yhl.scp.dfp.stock.infrastructure.po.InventoryRealTimeDataPO;
import com.yhl.scp.dfp.stock.vo.InventoryRealTimeDataVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>InventoryRealTimeDataDao</code>
 * <p>
 * 库存实时数据DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-29 11:40:18
 */
public interface InventoryRealTimeDataDao extends BaseDao<InventoryRealTimeDataPO, InventoryRealTimeDataVO> {

    /**
     * 根据版本批量删除
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

    /**
     * 根据产品编码批量查询半品库存与成品库存
     * @param productCodes
     * @return
     */
    List<InventoryDataDTO> selectInventoryByProductCodes(@Param("productCodes") List<String> productCodes);

    List<InventoryRealTimeDataVO> selectRealTimeInventory();

    List<RealTimeInventoryVO> selectInventoryByParams(@Param("params") Map<String, Object> params);

    List<InventoryRealTimeDataVO> dataConsistentCheck();
}
