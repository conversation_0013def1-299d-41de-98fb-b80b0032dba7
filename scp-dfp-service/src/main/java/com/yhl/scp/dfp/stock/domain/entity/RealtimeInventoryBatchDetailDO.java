package com.yhl.scp.dfp.stock.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>RealtimeInventoryBatchDetailDO</code>
 * <p>
 * 实时库存批次明细DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:52:25
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RealtimeInventoryBatchDetailDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 552308794124457495L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 现有量
     */
    private String currentQuantity;
    /**
     * 货位
     */
    private String freightSpace;
    /**
     * 货位描述
     */
    private String freightSpaceDescription;
    /**
     * 工序
     */
    private String operationCode;
    /**
     * 原始报文组织ID
     */
    private String originalOrgId;
    /**
     * 原始物料编码
     */
    private String originalProductCode;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 子库存
     */
    private String subinventory;
    /**
     * 子库存描述
     */
    private String subinventoryDescription;
    /**
     * 同步人
     */
    private String syncUserId;
    /**
     * 版本号
     */
    private Integer versionValue;

}
