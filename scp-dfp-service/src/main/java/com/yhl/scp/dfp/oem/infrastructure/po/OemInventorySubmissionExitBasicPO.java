package com.yhl.scp.dfp.oem.infrastructure.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.ddd.BasePO;

/**
 * <code>OemInventorySubmissionExitBasicPO</code>
 * <p>
 * 主机厂库存提报基础（出口）PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 21:32:28
 */
public class OemInventorySubmissionExitBasicPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -15080142417009812L;

    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 主机厂名称
     */
    private String oemName;
    /**
     * 中转库编码
     */
    private String stockPointCode;
    /**
     * 中转库名称
     */
    private String stockPointName;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 本厂名称
     */
    private String productName;
    /**
     * 中转库库存数量
     */
    private BigDecimal stockInventoryQuantity;
    /**
     * 主机厂库存数量
     */
    private BigDecimal oemInventoryQuantity;
    /**
     * 日期
     */
    private Date submissionDate;
    
    /**
     * 发货清单号(国内)
     */
    private String ShippingListNumber;
    
    /**
     * 柜号(海外)
     */
    private String containerNumber;
    
    /**
     * 零件号
     */
    private String partNumber;
    /**
     * 零件名称
     */
    private String partName;
    /**
     * 中转库待接收量
     */
    private BigDecimal transitWaitQuantity;
    /**
     * 中转库入库数量
     */
    private BigDecimal transitEnterQuantity;
    /**
     * 中转库发货数量
     */
    private BigDecimal transitDeliverQuantity;
    /**
     * 退货
     */
    private String returnGoods;

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getOemName() {
        return oemName;
    }

    public void setOemName(String oemName) {
        this.oemName = oemName;
    }

    public String getStockPointCode() {
        return stockPointCode;
    }

    public void setStockPointCode(String stockPointCode) {
        this.stockPointCode = stockPointCode;
    }

    public String getStockPointName() {
        return stockPointName;
    }

    public void setStockPointName(String stockPointName) {
        this.stockPointName = stockPointName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getStockInventoryQuantity() {
        return stockInventoryQuantity;
    }

    public void setStockInventoryQuantity(BigDecimal stockInventoryQuantity) {
        this.stockInventoryQuantity = stockInventoryQuantity;
    }

    public BigDecimal getOemInventoryQuantity() {
        return oemInventoryQuantity;
    }

    public void setOemInventoryQuantity(BigDecimal oemInventoryQuantity) {
        this.oemInventoryQuantity = oemInventoryQuantity;
    }

    public Date getSubmissionDate() {
        return submissionDate;
    }

    public void setSubmissionDate(Date submissionDate) {
        this.submissionDate = submissionDate;
    }

    public String getPartNumber() {
        return partNumber;
    }

    public void setPartNumber(String partNumber) {
        this.partNumber = partNumber;
    }

    public String getPartName() {
        return partName;
    }

    public void setPartName(String partName) {
        this.partName = partName;
    }

    public BigDecimal getTransitWaitQuantity() {
        return transitWaitQuantity;
    }

    public void setTransitWaitQuantity(BigDecimal transitWaitQuantity) {
        this.transitWaitQuantity = transitWaitQuantity;
    }

    public BigDecimal getTransitEnterQuantity() {
        return transitEnterQuantity;
    }

    public void setTransitEnterQuantity(BigDecimal transitEnterQuantity) {
        this.transitEnterQuantity = transitEnterQuantity;
    }

    public BigDecimal getTransitDeliverQuantity() {
        return transitDeliverQuantity;
    }

    public void setTransitDeliverQuantity(BigDecimal transitDeliverQuantity) {
        this.transitDeliverQuantity = transitDeliverQuantity;
    }

    public String getReturnGoods() {
        return returnGoods;
    }

    public void setReturnGoods(String returnGoods) {
        this.returnGoods = returnGoods;
    }

	public String getShippingListNumber() {
		return ShippingListNumber;
	}

	public void setShippingListNumber(String shippingListNumber) {
		ShippingListNumber = shippingListNumber;
	}

	public String getContainerNumber() {
		return containerNumber;
	}

	public void setContainerNumber(String containerNumber) {
		this.containerNumber = containerNumber;
	}
    
}
