package com.yhl.scp.dfp.stock.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesRealTimeInventory;
import com.yhl.scp.dfp.stock.convertor.RealtimeInventoryBatchDetailConvertor;
import com.yhl.scp.dfp.stock.domain.entity.RealtimeInventoryBatchDetailDO;
import com.yhl.scp.dfp.stock.domain.service.RealtimeInventoryBatchDetailDomainService;
import com.yhl.scp.dfp.stock.dto.RealtimeInventoryBatchDetailDTO;
import com.yhl.scp.dfp.stock.infrastructure.dao.RealtimeInventoryBatchDetailDao;
import com.yhl.scp.dfp.stock.infrastructure.po.RealtimeInventoryBatchDetailPO;
import com.yhl.scp.dfp.stock.service.RealtimeInventoryBatchDetailService;
import com.yhl.scp.dfp.stock.vo.RealtimeInventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>RealtimeInventoryBatchDetailServiceImpl</code>
 * <p>
 * 实时库存批次明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:52:25
 */
@Slf4j
@Service
public class RealtimeInventoryBatchDetailServiceImpl extends AbstractService implements RealtimeInventoryBatchDetailService {

    @Resource
    private RealtimeInventoryBatchDetailDao realtimeInventoryBatchDetailDao;

    @Resource
    private RealtimeInventoryBatchDetailDomainService realtimeInventoryBatchDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewMdsFeign mdsFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(RealtimeInventoryBatchDetailDTO realtimeInventoryBatchDetailDTO) {
        // 0.数据转换
        RealtimeInventoryBatchDetailDO realtimeInventoryBatchDetailDO = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Do(realtimeInventoryBatchDetailDTO);
        RealtimeInventoryBatchDetailPO realtimeInventoryBatchDetailPO = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Po(realtimeInventoryBatchDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        realtimeInventoryBatchDetailDomainService.validation(realtimeInventoryBatchDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(realtimeInventoryBatchDetailPO);
        realtimeInventoryBatchDetailDao.insert(realtimeInventoryBatchDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(RealtimeInventoryBatchDetailDTO realtimeInventoryBatchDetailDTO) {
        // 0.数据转换
        RealtimeInventoryBatchDetailDO realtimeInventoryBatchDetailDO = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Do(realtimeInventoryBatchDetailDTO);
        RealtimeInventoryBatchDetailPO realtimeInventoryBatchDetailPO = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Po(realtimeInventoryBatchDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        realtimeInventoryBatchDetailDomainService.validation(realtimeInventoryBatchDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(realtimeInventoryBatchDetailPO);
        realtimeInventoryBatchDetailDao.update(realtimeInventoryBatchDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<RealtimeInventoryBatchDetailDTO> list) {
        List<RealtimeInventoryBatchDetailPO> newList = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        realtimeInventoryBatchDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<RealtimeInventoryBatchDetailDTO> list) {
        List<RealtimeInventoryBatchDetailPO> newList = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        realtimeInventoryBatchDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return realtimeInventoryBatchDetailDao.deleteBatch(idList);
        }
        return realtimeInventoryBatchDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public RealtimeInventoryBatchDetailVO selectByPrimaryKey(String id) {
        RealtimeInventoryBatchDetailPO po = realtimeInventoryBatchDetailDao.selectByPrimaryKey(id);
        return RealtimeInventoryBatchDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "REALTIME_INVENTORY_BATCH_DETAIL")
    public List<RealtimeInventoryBatchDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "REALTIME_INVENTORY_BATCH_DETAIL")
    public List<RealtimeInventoryBatchDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<RealtimeInventoryBatchDetailVO> dataList = realtimeInventoryBatchDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        RealtimeInventoryBatchDetailServiceImpl target = SpringBeanUtils.getBean(RealtimeInventoryBatchDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<RealtimeInventoryBatchDetailVO> selectByParams(Map<String, Object> params) {
        List<RealtimeInventoryBatchDetailPO> list = realtimeInventoryBatchDetailDao.selectByParams(params);
        return RealtimeInventoryBatchDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<RealtimeInventoryBatchDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    /**
     * 根据组织ID列表删除数据
     *
     * @param orgIds 组织ID列表
     * @return 删除的记录数
     */
    public int doDeleteAllByOrgIds(List<String> orgIds) {
        return realtimeInventoryBatchDetailDao.doDeleteAllByOrgIds(orgIds);
    }
    /**
     * 根据用户ID列表删除数据
     *
     * @param userId 用户ID
     * @return 删除的记录数
     */
    public int doDeleteAllByUserId(String userId) {
        return realtimeInventoryBatchDetailDao.doDeleteAllByUserId(userId);
    }
    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<RealtimeInventoryBatchDetailVO> invocation(List<RealtimeInventoryBatchDetailVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<Void> sync(String scenario, List<MesRealTimeInventory> mesRealTimeInventories, List<String> orgIds,String syncUser) {
        log.info("开始处理MES实时库存数据");
        if (CollectionUtils.isEmpty(mesRealTimeInventories)) {
            return BaseResponse.success();
        }
        List<RealtimeInventoryBatchDetailDTO> batchList = new java.util.ArrayList<>();

        for (MesRealTimeInventory detail : mesRealTimeInventories) {
            String itemCode = detail.getItemCode();
            String plantId = detail.getPlantId();

            RealtimeInventoryBatchDetailDTO dto = new RealtimeInventoryBatchDetailDTO();
            dto.setOriginalProductCode(itemCode);
            dto.setOriginalOrgId(plantId);
            dto.setStockPointCode(detail.getPlantCode());
            dto.setCurrentQuantity(String.valueOf(detail.getLoctOnhand()));
            dto.setFreightSpace(detail.getLocatorCode());
            dto.setFreightSpaceDescription(detail.getColsIn());
            dto.setSubinventory(detail.getWarehouseCode());
            dto.setSubinventoryDescription(detail.getWhDesc());
            dto.setEnabled(YesOrNoEnum.YES.getCode());
            dto.setOperationCode(detail.getSplitedSequence());
            dto.setSyncUserId(SystemHolder.getUserId());
            batchList.add(dto);
        }

        // 批量保存数据
        if (CollectionUtils.isNotEmpty(batchList)) {
            this.doDeleteAllByUserId(syncUser);
            log.info("删除用户ID为{}的实时库存批次明细数据", syncUser);

            int optimalBatchSize = Math.min(Math.max(batchList.size() / 10, 1000), 3000);
            List<List<RealtimeInventoryBatchDetailDTO>> partitions =
                    com.google.common.collect.Lists.partition(batchList, optimalBatchSize);

            log.info("开始批量插入数据，总数量：{}，批次大小：{}，批次数量：{}", 
                batchList.size(), optimalBatchSize, partitions.size());

            for (int i = 0; i < partitions.size(); i++) {
                List<RealtimeInventoryBatchDetailDTO> part = partitions.get(i);
                long batchStartTime = System.currentTimeMillis();
                this.doCreateBatch(part);
                long batchEndTime = System.currentTimeMillis();

                log.info("批次{}插入完成，数量：{}，耗时：{}ms", 
                    i + 1, part.size(), batchEndTime - batchStartTime);
            }
        }

        log.info("同步实时库存批次明细数据完成，库存点:{}，新增:{}", orgIds, batchList.size());
        return BaseResponse.success("同步成功");
    }

}
