package com.yhl.scp.dfp.stock.convertor;

import com.yhl.scp.dfp.stock.domain.entity.RealtimeInventoryBatchDetailDO;
import com.yhl.scp.dfp.stock.dto.RealtimeInventoryBatchDetailDTO;
import com.yhl.scp.dfp.stock.infrastructure.po.RealtimeInventoryBatchDetailPO;
import com.yhl.scp.dfp.stock.vo.RealtimeInventoryBatchDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>RealtimeInventoryBatchDetailConvertor</code>
 * <p>
 * 实时库存批次明细转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:52:25
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RealtimeInventoryBatchDetailConvertor {

    RealtimeInventoryBatchDetailConvertor INSTANCE = Mappers.getMapper(RealtimeInventoryBatchDetailConvertor.class);

    RealtimeInventoryBatchDetailDO dto2Do(RealtimeInventoryBatchDetailDTO obj);

    RealtimeInventoryBatchDetailDTO do2Dto(RealtimeInventoryBatchDetailDO obj);

    List<RealtimeInventoryBatchDetailDO> dto2Dos(List<RealtimeInventoryBatchDetailDTO> list);

    List<RealtimeInventoryBatchDetailDTO> do2Dtos(List<RealtimeInventoryBatchDetailDO> list);

    RealtimeInventoryBatchDetailVO do2Vo(RealtimeInventoryBatchDetailDO obj);

    RealtimeInventoryBatchDetailVO po2Vo(RealtimeInventoryBatchDetailPO obj);

    List<RealtimeInventoryBatchDetailVO> po2Vos(List<RealtimeInventoryBatchDetailPO> list);

    RealtimeInventoryBatchDetailPO do2Po(RealtimeInventoryBatchDetailDO obj);

    RealtimeInventoryBatchDetailDO po2Do(RealtimeInventoryBatchDetailPO obj);

    RealtimeInventoryBatchDetailPO dto2Po(RealtimeInventoryBatchDetailDTO obj);

    List<RealtimeInventoryBatchDetailPO> dto2Pos(List<RealtimeInventoryBatchDetailDTO> obj);

}
