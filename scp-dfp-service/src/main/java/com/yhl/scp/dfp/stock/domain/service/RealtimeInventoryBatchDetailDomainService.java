package com.yhl.scp.dfp.stock.domain.service;

import com.yhl.scp.dfp.stock.domain.entity.RealtimeInventoryBatchDetailDO;
import com.yhl.scp.dfp.stock.infrastructure.dao.RealtimeInventoryBatchDetailDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>RealtimeInventoryBatchDetailDomainService</code>
 * <p>
 * 实时库存批次明细领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:52:25
 */
@Service
public class RealtimeInventoryBatchDetailDomainService {

    @Resource
    private RealtimeInventoryBatchDetailDao realtimeInventoryBatchDetailDao;

    /**
     * 数据校验
     *
     * @param realtimeInventoryBatchDetailDO 领域对象
     */
    public void validation(RealtimeInventoryBatchDetailDO realtimeInventoryBatchDetailDO) {
        checkNotNull(realtimeInventoryBatchDetailDO);
        checkUniqueCode(realtimeInventoryBatchDetailDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param realtimeInventoryBatchDetailDO 领域对象
     */
    private void checkNotNull(RealtimeInventoryBatchDetailDO realtimeInventoryBatchDetailDO) {
    }

    /**
     * 唯一性校验
     *
     * @param realtimeInventoryBatchDetailDO 领域对象
     */
    private void checkUniqueCode(RealtimeInventoryBatchDetailDO realtimeInventoryBatchDetailDO) {
    }

}
