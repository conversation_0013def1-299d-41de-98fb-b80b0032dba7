package com.yhl.scp.mrp.material.plan.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialPlanTransferVO</code>
 * <p>
 * 调拨计划VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:13:56
 */
@ApiModel(value = "调拨计划VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPlanTransferVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -29696440422823889L;

    /**
     * 调拨计划号
     */
    @ApiModelProperty(value = "调拨计划号")
    @FieldInterpretation(value = "调拨计划号")
    private String planTransferNo;
    /**
     * 物品编码
     */
    @ApiModelProperty(value = "物品编码")
    @FieldInterpretation(value = "物品编码")
    private String productCode;

    /**
     * 需求物料
     */
    @ApiModelProperty(value = "需求物料")
    @FieldInterpretation(value = "需求物料")
    private String demandProductCode;
    /**
     * 运输路径id
     */
    @ApiModelProperty(value = "运输路径id")
    @FieldInterpretation(value = "运输路径id")
    private String transferRoutingId;
    /**
     * 柜号
     */
    @ApiModelProperty(value = "柜号")
    @FieldInterpretation(value = "柜号")
    private String cabinetNo;

    /**
     * 库存类型
     */
    @ApiModelProperty(value = "库存类型")
    @FieldInterpretation(value = "库存类型")
    private String inventoryType;

    /**
     * 调拨状态
     */
    @ApiModelProperty(value = "调拨状态")
    @FieldInterpretation(value = "调拨状态")
    private String transferStatus;
    /**
     * 历史调拨数量
     */
    @ApiModelProperty(value = "历史调拨数量")
    @FieldInterpretation(value = "历史调拨数量")
    private BigDecimal historyQuantity;
    /**
     * 调整数量
     */
    @ApiModelProperty(value = "调整数量")
    @FieldInterpretation(value = "调整数量")
    private BigDecimal adjustQuantity;
    /**
     * 调拨数量
     */
    @ApiModelProperty(value = "调拨数量")
    @FieldInterpretation(value = "调拨数量")
    private BigDecimal transferQuantity;
    /**
     * 已调拨数量
     */
    @ApiModelProperty(value = "已调拨数量")
    @FieldInterpretation(value = "已调拨数量")
    private BigDecimal transferredQuantity;
    /**
     * 调拨出发日期
     */
    @ApiModelProperty(value = "调拨出发日期")
    @FieldInterpretation(value = "调拨出发日期")
    private Date transferDateDepart;
    /**
     * 调拨到达日期
     */
    @ApiModelProperty(value = "调拨到达日期")
    @FieldInterpretation(value = "调拨到达日期")
    private Date transferDateArrive;
    /**
     * 是否入库
     */
    @ApiModelProperty(value = "是否入库")
    @FieldInterpretation(value = "是否入库")
    private String storageFlag;
    /**
     * 物品id
     */
    @ApiModelProperty(value = "物品id")
    @FieldInterpretation(value = "物品id")
    private String productId;
    /**
     * 物品名称
     */
    @ApiModelProperty(value = "物品名称")
    @FieldInterpretation(value = "物品名称")
    private String productName;
    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    @FieldInterpretation(value = "长")
    private BigDecimal productLength;
    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    @FieldInterpretation(value = "宽")
    private BigDecimal productWidth;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    @FieldInterpretation(value = "厚度")
    private BigDecimal productThickness;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @FieldInterpretation(value = "颜色")
    private String productColor;
    /**
     * 运输路径编码
     */
    @ApiModelProperty(value = "运输路径编码")
    @FieldInterpretation(value = "运输路径编码")
    private String routingCode;
    /**
     * 来源库存点编码
     */
    @ApiModelProperty(value = "来源库存点编码")
    @FieldInterpretation(value = "来源库存点编码")
    private String stockPointCodeFrom;
    /**
     * 来源库存点名称
     */
    @ApiModelProperty(value = "来源库存点名称")
    @FieldInterpretation(value = "来源库存点名称")
    private String stockPointNameFrom;
    /**
     * 来源库存点类型
     */
    @ApiModelProperty(value = "来源库存点类型")
    @FieldInterpretation(value = "来源库存点类型")
    private String stockPointTypeFrom;
    /**
     * 目标库存点编码
     */
    @ApiModelProperty(value = "目标库存点编码")
    @FieldInterpretation(value = "目标库存点编码")
    private String stockPointCodeTo;
    /**
     * 目标库存点名称
     */
    @ApiModelProperty(value = "目标库存点名称")
    @FieldInterpretation(value = "目标库存点名称")
    private String stockPointNameTo;
    /**
     * 目标库存点类型
     */
    @ApiModelProperty(value = "目标库存点类型")
    @FieldInterpretation(value = "目标库存点类型")
    private String stockPointTypeTo;

    /**
     * 库存点
     */
    @ApiModelProperty(value = "库存点")
    @FieldInterpretation(value = "库存点")
    private String stockPointCode;

    /**
     * 物品规格
     */
    @ApiModelProperty(value = "物品规格")
    @FieldInterpretation(value = "物品规格")
    private String productSpec;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    @FieldInterpretation(value = "等级")
    private String level;
    /**
     * 片/箱
     */
    @ApiModelProperty(value = "片/箱")
    @FieldInterpretation(value = "片/箱")
    private BigDecimal perBox;
    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    @FieldInterpretation(value = "箱数")
    private BigDecimal box;

    /**
     * 实发片数
     */
    @ApiModelProperty(value = "实发片数")
    @FieldInterpretation(value = "实发片数")
    private BigDecimal actualSentQuantity;
    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    @FieldInterpretation(value = "面积")
    private BigDecimal area;
    /**
     * 吨数
     */
    @ApiModelProperty(value = "吨数")
    @FieldInterpretation(value = "吨数")
    private BigDecimal weight;
    /**
     * 包装方式
     */
    @ApiModelProperty(value = "包装方式")
    @FieldInterpretation(value = "包装方式")
    private String packageType;
    /**
     * 实际到港时间
     */
    @ApiModelProperty(value = "实际到港时间")
    @FieldInterpretation(value = "实际到港时间")
    private Date actualArrivalTime;
    /**
     * 港口
     */
    @ApiModelProperty(value = "港口")
    @FieldInterpretation(value = "港口")
    private String portName;
    /**
     * 承运商
     */
    @ApiModelProperty(value = "承运商")
    @FieldInterpretation(value = "承运商")
    private String carrier;
    /**
     * 是否发货
     */
    @ApiModelProperty(value = "是否发货")
    @FieldInterpretation(value = "是否发货")
    private String delivered;
    /**
     * 送柜时间
     */
    @ApiModelProperty(value = "送柜时间")
    @FieldInterpretation(value = "送柜时间")
    private Date containerDeliveryTime;
    /**
     * PO
     */
    @ApiModelProperty(value = "PO")
    @FieldInterpretation(value = "PO")
    private String purchaseOrderCode;
    /**
     * PO行
     */
    @ApiModelProperty(value = "PO行")
    @FieldInterpretation(value = "PO行")
    private String purchaseOrderLineCode;
    /**
     * 分类说明
     */
    @ApiModelProperty(value = "分类说明")
    @FieldInterpretation(value = "分类说明")
    private String classifyDesc;
    /**
     * 柜号
     */
    @ApiModelProperty(value = "柜号")
    @FieldInterpretation(value = "柜号")
    private String containerNumber;
    /**
     * 超期时间
     */
    @ApiModelProperty(value = "超期时间")
    @FieldInterpretation(value = "超期时间")
    private Date overdueTime;

    /**
     * 是否超期
     */
    @ApiModelProperty(value = "是否超期")
    @FieldInterpretation(value = "是否超期")
    private String whetherOverdue;

    @Override
    public void clean() {

    }

    public String getGroupKey() {
        return this.getProductCode() + "&&" + this.getStockPointCodeFrom();
    }

}
