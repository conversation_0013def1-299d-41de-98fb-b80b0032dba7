package com.yhl.scp.mrp.material.purchase.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPrQuery;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseRequestDTO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseRequestVO;

import java.util.List;

/**
 * <code>MaterialPurchaseRequestService</code>
 * <p>
 * ERP材料采购申请单应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-02 10:19:16
 */
public interface MaterialPurchaseRequestService extends BaseService<MaterialPurchaseRequestDTO, MaterialPurchaseRequestVO> {

    void doUpdateBatchSelective(List<MaterialPurchaseRequestDTO> list);

    /**
     * 查询所有
     *
     * @return list {@link MaterialPurchaseRequestVO}
     */
    List<MaterialPurchaseRequestVO> selectAll();

    /**
     * 材料采购需求，材料到货跟踪关联采购订单定时job
     * @param moveMinute 
     */
	void doDisposePurchaseRequestJob(Integer moveMinute);

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> handleMaterialPurchaseRequest(List<ErpPrQuery> erpPrQuery);

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> syncMaterialPurchaseRequest(String tenantId,String scenario);

    void doPrCancelJob(Integer moveMinute);

}
