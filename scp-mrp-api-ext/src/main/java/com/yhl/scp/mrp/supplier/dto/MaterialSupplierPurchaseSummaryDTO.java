package com.yhl.scp.mrp.supplier.dto;

import com.yhl.scp.mds.extension.supplier.dto.SupplierPurchaseRatioDTO;
import com.yhl.scp.mds.extension.supplier.dto.SupplierRatioDTO;
import com.yhl.scp.mrp.inventory.dto.SafetyInventoryDTO;
import lombok.Data;

/**
 * @ClassName MaterialSupplierPurchaseSummaryDTO
 * @Description TODO
 * @Date 2025-08-21 11:01:19
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
@Data
public class MaterialSupplierPurchaseSummaryDTO {

    /**
     *  材料与供应商对象
     */
    private MaterialSupplierPurchaseDTO materialSupplierPurchaseDTO;

    /**
     * 供应商采购比例对象
     */
    private SupplierPurchaseRatioDTO supplierPurchaseRatioDTO;

    /**
     * 材料安全库存对象
     */
    private SafetyInventoryDTO safetyInventoryDTO;
}
