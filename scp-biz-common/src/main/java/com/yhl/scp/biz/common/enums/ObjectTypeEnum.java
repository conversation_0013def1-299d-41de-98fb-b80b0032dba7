package com.yhl.scp.biz.common.enums;

import com.alibaba.excel.util.StringUtils;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.enums.CommonEnum2;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <code>ObjectTypeEnum</code>
 * <p>
 * ObjectTypeEnum
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-10 09:47:17
 */
public enum ObjectTypeEnum implements CommonEnum2 {
    /********************* IPS *********************/
    /**
     * 数据权限配置
     */
    DATA_PERMISSION_CONFIG("auth_rbac_data_permission_config", "数据权限配置", "com.yhl.scp.ips.rbac.vo" +
            ".DataPermissionConfigVO"),
    /**
     * 数据权限配置明细
     */
    DATA_PERMISSION_DETAIL("auth_rbac_data_permission_detail", "数据权限配置明细", "com.yhl.scp.ips.rbac.vo" +
            ".DataPermissionDetailVO"),
    /**
     * 数据权限配置关联对象
     */
    DATA_PERMISSION_RELATION("auth_rbac_data_permission_relation", "数据权限配置关联对象", "com.yhl.scp.ips.rbac.vo" +
            ".DataPermissionRelationVO"),
    EXT_API_CONFIG("ext_api_config","外部api接口配置","com.yhl.scp.ips.api.vo.ExtApiConfigVO"),
    EXT_API_LOG("ext_api_log","接口日志表","com.yhl.scp.ips.api.vo.ExtApiLogVO"),
    EXT_API_SYNC_CTRL("ext_api_sync_ctrl","外部api同步控制表","com.yhl.scp.ips.api.vo.ExtApiSyncCtrlVO"),
    EXT_LLM_CONFIG("ext_llm_config", "大模型配置表", "com.yhl.scp.ips.api.vo.LlmConfigVO"),
    /**
     *用户
     */
    USER("auth_rbac_user", "用户下拉权限", "com.yhl.scp.ips.rbac.vo.UserVO"),

    MESSAGE("sys_message", "消息", "com.yhl.scp.ips.system.message.vo.MessageVO"),
    MESSAGE_TEMPLATE("sys_message_template", "消息模板", "com.yhl.scp.ips.system.message.vo.MessageTemplateVO"),
    EVENT_DEFINITION("sys_event_definition", "事件定义", "com.yhl.scp.ips.system.event.vo.EventDefinitionVO"),
    EVENT_CONFIG("sys_event_config", "事件配置", "com.yhl.scp.ips.system.event.vo.EventConfigVO"),
    EVENT_DEPENDENCY_CONFIG("sys_event_dependency_config", "事件依赖配置", "com.yhl.scp.system.event.vo.EventDependencyConfigVO"),
    EVENT_UPGRADE_CONFIG("sys_event_upgrade_config", "事件升级配置", "com.yhl.scp.system.event.vo.EventUpgradeConfigVO"),
    EVENT_LOG("sys_event_log", "事件日志", "com.yhl.scp.system.event.vo.EventLogVO"),
    /********************* MDS *********************/
    PRODUCT_BOX_RELATION("v_mds_product_box_relation", "产品与成品箱关系", "com.yhl.scp.mds.productBox.vo" +
            ".ProductBoxRelationVO"),
    PRODUCTION_ORGANIZE("mds_production_organize", "生产组织", "com.yhl.scp.mds.production.vo.ProductionOrganizeVO"),
    STOCK_POINT("v_mds_stock_point", "库存点", "com.yhl.scp.mds.stock.vo.NewStockPointVO"),
    ERP_BOM("erp_bom", "erp同步bom主数据", "com.yhl.scp.mds.routing.vo.ErpBomVO"),
    PRODUCT_STOCK_POINT_BASE("mds_product_stock_point_base", "产品工艺基础数据", "com.yhl.scp.mds.productroutestepbase.vo" +
            ".MdsProductStockPointBaseVO"),
    OVER_DEADLINE_DAYS("mds_over_deadline_days", "超期界定天数", "com.yhl.scp.mds.overdeadlineday.vo.MdsOverDeadlineDaysVO"),
    BOX_INFO("v_mds_box_info", "箱体信息", "com.yhl.scp.mds.box.vo.BoxInfoVO"),
    PRODUCT_STOCK_POINT("v_mds_product_stock_point", "物品", "com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO"),
    PRODUCT_SUBSTITUTION_RELATIONSHIP("mds_product_substitution_relationship", "物料替换关系", "com.yhl.scp.mds" +
            ".substitution.vo.ProductSubstitutionRelationshipVO"),


    /********************* DFP *********************/
    SALE_ORGANIZE("v_mds_sale_organize", "销售组织", "com.yhl.scp.dfp.sale.vo.SaleOrganizeVO"),
    ANNUAL_FORECAST_SUBMISSION("v_fdp_annual_forecast_submission", "年度预测提报", "com.yhl.scp.dfp.annual.vo" +
            ".AnnualForecastSubmissionVO"),
    CLEAN_ALGORITHM_DATA("v_fdp_clean_algorithm_data", "预测算法数据", "com.yhl.scp.dfp.clean.vo.CleanAlgorithmDataVO"),
    CLEAN_DEMAND_DATA("v_fdp_clean_demand_data", "日需求数据", "com.yhl.scp.dfp.clean.vo.CleanDemandDataVO"),
    CLEAN_DEMAND_DATA_DETAIL("v_fdp_clean_demand_data_detail", "日需求数据明细", "com.yhl.scp.dfp.clean.vo" +
            ".CleanDemandDataDetailVO"),
    CLEAN_FORECAST_DATA("v_fdp_clean_forecast_data", "滚动预测数据", "com.yhl.scp.dfp.clean.vo.CleanForecastDataVO"),
    CLEAN_FORECAST_DATA_DETAIL("fdp_clean_forecast_data_detail", "滚动预测数据明细", "com.yhl.scp.dfp.clean.vo" +
            ".CleanForecastDataDetailVO"),
    CONSISTENCE_DEMAND_FORECAST_DATA("fdp_consistence_demand_forecast_data", "一致性业务预测数据", "com.yhl.scp.dfp" +
            ".consistence.vo.ConsistenceDemandForecastDataVO"),
    CONSISTENCE_DEMAND_FORECAST_DATA_DETAIL("v_fdp_consistence_demand_forecast_data_detail", "一致性业务预测数据明细", "com.yhl" +
            ".scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO"),
    CONSISTENCE_DEMAND_FORECAST_VERSION("fdp_consistence_demand_forecast_version", "一致性业务预测版本", "com.yhl.scp.dfp" +
            ".consistence.vo.ConsistenceDemandForecastVersionVO"),
    DELIVERY_PLAN_LOCK_CONFIG("fdp_delivery_plan_lock_config", "发货计划锁定配置", "com.yhl.scp.dfp.delivery.vo" +
            ".DeliveryPlanLockConfigVO"),
    DELIVERY_PLAN_REPLENISH_CONFIG("fdp_delivery_plan_replenish_config", "发货计划补货策略", "com.yhl.scp.dfp.delivery.vo" +
            ".DeliveryPlanReplenishConfigVO"),
    DELIVERY_PLAN("v_fdp_delivery_plan", "发货计划表", "com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO"),
    DELIVERY_PLAN_DETAIL("v_fdp_delivery_plan_detail", "发货计划明细表", "com.yhl.scp.dfp.delivery.vo.DeliveryPlanDetailVO"),
    DELIVERY_PLAN_VERSION("v_fdp_delivery_plan_version", "发货计划版本", "com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO"),
    DEMAND_FORECAST_VERSION("v_fdp_demand_forecast_version", "业务预测版本", "com.yhl.scp.dfp.demand.vo" +
            ".DemandForecastVersionVO"),
    DEMAND_VERSION("v_fdp_demand_version", "需求版本", "com.yhl.scp.dfp.demand.vo.DemandVersionVO"),
    GLOBAL_CAR_SALE("fdp_global_car_sale", "全球汽车销量", "com.yhl.scp.dfp.global.vo.GlobalCarSaleVO"),
    GLOBAL_CAR_SALE_DETAIL("fdp_global_car_sale_detail", "全球汽车销量详情", "com.yhl.scp.dfp.global.vo.GlobalCarSaleDetailVO"),
    CAR_PRICE_LIST("fdp_car_price_list", "汽车价格表", "com.yhl.scp.dfp.car.vo.CarPriceListVO"),
    CAR_SALE_LIST("fdp_car_sale_list", "汽车销量表", "com.yhl.scp.dfp.car.vo.CarSaleListVO"),
    LOADING_DEMAND_SUBMISSION("v_fdp_loading_demand_submission", "装车需求提报", "com.yhl.scp.dfp.loading.vo" +
            ".LoadingDemandSubmissionVO"),
    LOADING_DEMAND_SUBMISSION_DETAIL("fdp_loading_demand_submission_detail", "装车需求提报详情", "com.yhl.scp.dfp.loading.vo" +
            ".LoadingDemandSubmissionDetailVO"),
    LOADING_DEMAND_SUBMISSION_EXIT("fdp_loading_demand_submission_exit","装车需求提报(出口)" ,"com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionExitVO" ),

    MARKET_INFORMATION("v_fdp_market_information", "市场信息", "com.yhl.scp.dfp.market.vo.MarketInformationVO"),
    POLICY_INFORMATION("v_fdp_policy_information", "政策信息", "com.yhl.scp.dfp.policy.vo.PolicyInformationVO"),
    MARKET_SHARE("v_fdp_market_share", "市场占有率", "com.yhl.scp.dfp.market.vo.MarketShareVO"),
    MARKET_SHARE_DETAIL("fdp_market_share_detail", "市场占有率详情", "com.yhl.scp.dfp.market.vo.MarketShareDetailVO"),
    PART_RISK_LEVEL("v_fdp_part_risk_level", "零件风险等级", "com.yhl.scp.dfp.material.vo.PartRiskLevelVO"),
    PART_RISK_LEVEL_DETAIL("v_fdp_part_risk_level_detail", "零件风险等级详情", "com.yhl.scp.dfp.material.vo" +
            ".PartRiskLevelDetailVO"),
    NEW_PRODUCT_TRIAL_SUBMISSION("v_fdp_new_product_trial_submission", "新品试制提报", "com.yhl.scp.dfp.newProduct.vo" +
            ".NewProductTrialSubmissionVO"),
    NEW_PRODUCT_TRIAL_SUBMISSION_DETAIL("v_fdp_new_product_trial_submission_detail", "新品试制提报详情", "com.yhl.scp.dfp" +
            ".newProduct.vo.NewProductTrialSubmissionDetailVO"),
    NEW_PROJECT_SUBMISSION("v_fdp_new_project_submission", "新项目提报", "com.yhl.scp.dfp.projectForecast.vo" +
            ".NewProjectSubmissionVO"),
    NEW_PROJECT_SUBMISSION_DETAIL("v_fdp_new_project_submission_detail", "新项目提报详情", "com.yhl.scp.dfp.projectForecast" +
            ".vo.NewProjectSubmissionDetailVO"),
    OEM_RISK_LEVEL("v_fdp_oem_risk_level", "主机厂风险等级", "com.yhl.scp.dfp.oem.vo.OemRiskLevelVO"),
    ORIGIN_DEMAND_VERSION("v_fdp_origin_demand_version", "原始需求版本", "com.yhl.scp.dfp.origin.vo.OriginDemandVersionVO"),
    PART_RELATION_MAP("fdp_part_relation_map", "零件映射关系", "com.yhl.scp.dfp.part.vo.PartRelationMapVO"),
    PASSENGER_CAR_SALE("v_fdp_passenger_car_sale", "乘用车市场信息", "com.yhl.scp.dfp.passenger.vo.PassengerCarSaleVO"),
    QUEUE_PLAN("fdp_queue_plan", "排车计划", "com.yhl.scp.dfp.queue.vo.QueuePlanVO"),
    QUEUE_PLAN_BATCH("fdp_queue_plan_batch", "排车计划批次", "com.yhl.scp.dfp.queue.vo.QueuePlanBatchVO"),
    RISK_LEVEL_RULE("fdp_risk_level_rule", "风险等级规则", "com.yhl.scp.dfp.risk.vo.RiskLevelRuleVO"),
    RISK_LEVEL_RULE_DETAIL("fdp_risk_level_rule_detail", "风险等级规则详情", "com.yhl.scp.dfp.risk.vo.RiskLevelRuleDetailVO"),
    SAFETY_STOCK_LEVEL("fdp_safety_stock_level", "安全库存水位", "com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO"),
    VEHICLE_CONFIGURATION("fdp_vehicle_configuration", "车型配置信息", "com.yhl.scp.dfp.vehicle.vo.VehicleConfigurationVO"),
    VEHICLE_CONFIGURATION_DETAIL("fdp_vehicle_configuration_detail", "车型配置信息详情", "com.yhl.scp.dfp.vehicle.vo" +
            ".VehicleConfigurationDetailVO"),
    OEM("v_mds_oem", "主机厂档案", "com.yhl.scp.dfp.oem.vo.OemVO"),
    OEM_PRODUCT_LINE("v_mds_oem_product_line", "主机厂产线资源", "com.yhl.scp.dfp.oem.vo.OemProductLineVO"),
    OEM_PRODUCT_LINE_MAP("v_mds_oem_product_line_map", "产线映射关系", "com.yhl.scp.dfp.oem.vo.OemProductLineMapVO"),
    OEM_STOCK_POINT_MAP("v_mds_oem_stock_point_map", "主机厂库存点关联关系", "com.yhl.scp.dfp.oem.vo.OemStockPointMapVO"),
    OEM_VEHICLE_MODEL("v_mds_oem_vehicle_model", "主机厂车型信息", "com.yhl.scp.dfp.oem.vo.OemVehicleModelVO"),
    OEM_VEHICLE_MODEL_MAP("v_mds_oem_vehicle_model_map", "主机厂车型映射关系", "com.yhl.scp.dfp.oem.vo.OemVehicleModelMapVO"),
    SUPPLIER_SUBMISSION("v_fdp_supplier_submission", "车型全供应商提报", "com.yhl.scp.dfp.supplier.vo.SupplierSubmissionVO"),
    SUPPLIER_SUBMISSION_DETAIL("fdp_supplier_submission_detail", "车型全供应商提报明细", "com.yhl.scp.dfp.supplier.vo" +
            ".SupplierSubmissionDetailVO"),
    INVENTORY_REAL_TIME_DATA("v_fdp_inventory_real_time_data", "库存实时数据", "com.yhl.scp.dfp.stock.vo" +
            ".InventoryRealTimeDataVO"),
    CURRENT_BATCH_QUANTITY("fdp_current_batch_quantity", "批次现有量数据", "com.yhl.scp.dfp.stock.vo.InventoryRealTimeDataVO"),
    ORIGINAL_FILM_CURRENT_BATCH_QUANTITY("fdp_original_film_current_batch_quantity", "原片库存批次明细数据", "com.yhl.scp.dfp" +
            ".stock.vo.OriginalFilmCurrentBatchQuantityVO"),
    ORIGINAL_FILM_IN_TRANSIT("fdp_original_film_in_transit", "原片在途数据", "com.yhl.scp.dfp.stock.vo" +
            ".OriginalFilmInTransitVO"),
    EXTERNAL_BUSINESS_DEMAND_SUBMIT("fdp_external_business_demand_submit", "外事业部需求提报", "com.yhl.scp.dfp" +
            ".externalBusiness.vo.ExternalBusinessDemandSubmitVO"),
    EXTERNAL_BUSINESS_DEMAND_SUBMIT_DETAIL("fdp_external_business_demand_submit_detail", "外事业部需求提报明细", "com.yhl.scp" +
            ".dfp.externalBusiness.vo.ExternalBusinessDemandSubmitDetailVO"),
    INVENTORY_SHIFT("v_fdp_inventory_shift", "库存推移表", "com.yhl.scp.dfp.stock.vo.InventoryShiftVO"),
    DEMAND_FORECAST_ESTABLISHMENT("fdp_demand_forecast_establishment", "业务预测编制", "com.yhl.scp.dfp.demand.vo" +
            ".DemandForecastEstablishmentVO"),
    TRANSPORT_RESOURCE("v_fdp_transport_resource", "运输资源", "com.yhl.scp.dfp.transport.vo.TransportResourceVO"),
    TRANSPORT_ROUTING("fdp_transport_routing", "运输路径", "com.yhl.scp.dfp.transport.vo.TransportRoutingVO"),
    TRANSPORT_ROUTING_DETAIL("fdp_transport_routing_detail", "运输线路", "com.yhl.scp.dfp.transport.vo" +
            ".TransportRoutingDetailVO"),
    TRANSPORT_ROUTING_RESOURCE("v_fdp_transport_routing_resource", "运输路径资源关系", "com.yhl.scp.dfp.transport.vo" +
            ".TransportRoutingResourceVO"),
    VEHICLE_MARKET_INVENTORY("fdp_vehicle_model_market_inventory", "车型市场保有量", "com.yhl.scp.dfp.vehicle.vo" +
            ".VehicleMarketInventoryVO"),
    INVENTORY_BATCH_DETAIL("fdp_inventory_batch_detail", "库存批次明细", "com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO"),
    HISTORY_INVENTORY_BATCH_DETAIL("fdp_history_inventory_batch_detail", "历史库存批次明细", "com.yhl.scp.dfp.inventory.vo.HistoryInventoryBatchDetailVO"),
    DELIVERY_DOCKING_ORDER_DETAIL("v_fdp_delivery_docking_order_detail", "发货对接单详情", "com.yhl.scp.dfp" +
            ".deliverydockingorder.vo.DeliveryDockingOrderDetailVO"),
    DELIVERY_DOCKING_ORDER("v_fdp_delivery_docking_order", "发货对接单", "com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderVO"),
    OEM_INVENTORY_SUBMISSION("v_fdp_oem_inventory_submission", "主机厂库存提报", "com.yhl.scp.dfp.oem.vo" +
            ".OemInventorySubmissionVO"),
    WAREHOUSE_RELEASE_RECORD_LOG("dfp_warehouse_release_record_log", "DFP库存发布记录日志", "com.yhl.scp.dfp.warehouse.vo" +
            ".WarehouseReleaseRecordLogVO"),
    INDUSTRY_INFO("fdp_industry_info", "行业资讯", "com.yhl.scp.dfp.industry.vo.IndustryInfoVO"),
    WAREHOUSE_RELEASE_RECORD("v_fdp_warehouse_release_record", "库存发货记录", "com.yhl.scp.dfp.warehouse.vo" +
            ".WarehouseReleaseRecordVO"),
    OEM_CALENDAR_RULE("v_mds_cal_calendar_rule", "日历规则", "com.yhl.scp.dfp.calendar.vo.CalendarRuleVO"),
    OEM_CAL_SHIFT("mds_cal_shift", "主机厂班次", "com.yhl.scp.dfp.calendar.vo.ShiftVO"),
    OEM_RESOURCE_CALENDAR("v_mds_cal_resource_calendar", "主机厂装车日历", "com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO"),
    PROJECT_FORECAST_PRESENTATION("v_fdp_project_forecast_presentation", "项目预测提报", "com.yhl.scp.dfp.projectForecast" +
            ".vo.ProjectForecastPresentationVO"),
    PROJECT_FORECAST_PRESENTATION_DETAIL("fdp_project_forecast_presentation_detail", "项目预测提报详情", "com.yhl.scp.dfp" +
            ".projectForecast.vo.ProjectForecastPresentationDetailVO"),
    PROJECT_FORECAST_VERSION("v_fdp_project_forecast_version", "项目预测版本", "com.yhl.scp.dfp.demand.vo" +
            ".ProjectForecastVersionVO"),
    OEM_INVENTORY("v_fdp_oem_inventory", "主机厂库存", "com.yhl.scp.dfp.oem.vo.OemInventoryVO"),
    CLEAN_ALGORITHM_ANALYSIS("dfp_clean_algorithm_analysis", "算法结果-预测因子分析", "com.yhl.scp.dfp.clean.vo" +
            ".CleanAlgorithmAnalysisVO"),
    FDP_ORIGIN_DEMAND_INTERFACE_LOG("fdp_origin_demand_interface_log", "EDI系统需求数据接口中间表", "com.yhl.scp.dfp" +
            ".originDemand.vo.FdpOriginDemandInterfaceLogVO"),
    FDP_ORIGIN_DEMAND_FORECAST_INTERFACE_LOG("fdp_origin_demand_forecast_interface_log", "EDI系统预测数据中间表", "com.yhl.scp" +
            ".dfp.originDemand.vo.FdpOriginDemandForecastInterfaceLogVO"),
    DFP_DEMAND_FORECAST_ATTACHMENTS("dfp_demand_forecast_attachments","源文件管理","com.yhl.scp.dfp.loading.vo.DemandForecastAttachmentsVO"),
    /********************* MPS *********************/
    RESOURCE_GROUP("v_mds_resource_group", "资源组", "com.yhl.scp.mps.resource.vo.ResourceGroupVO"),
    RESOURCE("v_mds_resource", "资源", "com.yhl.scp.mps.resource.vo.ResourceVO"),
    MASTER_PLAN_DETAIL("mps_master_plan_detail", "主生产计划明细", "com.yhl.scp.mps.plan.vo.MasterPlanDetailVO"),
    CALENDAR_RULE("v_mds_cal_calendar_rule", "日历规则", "com.yhl.scp.mps.calendar.vo.CalendarRuleVO"),
    SHIFT("mds_cal_shift", "班次", "com.yhl.scp.mps.calendar.vo.ShiftVO"),
    PRODUCT_CANDIDATE_RESOURCE_TIME("v_mds_product_candidate_resource_time", "产品资源生产关系时段优先级", "com.yhl.scp.mps" +
            ".product.vo.ProductCandidateResourceTimeVO"),
    MOLD_CHANGE_TIME("mps_mold_change_time", "换模换型时间", "com.yhl.scp.mps.model.vo.MoldChangeTimeVO"),
    PRODUCTION_LOT("mps_production_lot", "生产经济批量", "com.yhl.scp.mps.productionLot.vo.ProductionLotVO"),
    PRODUCTION_LEAD_TIME("mps_production_lead_time", "生产提前期", "com.yhl.scp.mps.productionLeadTime.vo" +
            ".ProductionLeadTimeVO"),
    PARAM_CAPACITY_BALANCE("mps_param_capacity_balance", "产能平衡规则", "com.yhl.scp.mps.capacityBalance.vo" +
            ".ParamCapacityBalanceVO"),
    CAPACITY_BALANCE_VERSION("mps_capacity_balance_version", "产能平衡版本", "com.yhl.scp.mps.capacityBalance.vo" +
            ".CapacityBalanceVersionVO"),
    COATING_MAINTENANCE_AMOUNT("mps_coating_maintenance_amount", "镀膜保养量", "com.yhl.scp.mps.coating.vo" +
            ".CoatingMaintenanceAmountVO"),
    PRODUCT_CANDIDATE_RESOURCE("v_mps_product_candidate_resource", "产品资源生产关系", "com.yhl.scp.mps.product.vo" +
            ".ProductCandidateResourceVO"),
    CAPACITY_LOAD("mps_capacity_load", "产能负荷", "com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO"),
    CAPACITY_SUPPLY_RELATIONSHIP("v_mps_capacity_supply_relationship", "产能供应关系", "com.yhl.scp.mps.capacityBalance.vo" +
            ".CapacitySupplyRelationshipVO"),
    EQUIPMENT_PRODUCTION_RELATIONS("mps_equipment_production_relations", "产品设备生产关系", "com.yhl.scp.mps.capacityBalance" +
            ".vo.EquipmentProductionRelationsVO"),
    RESOURCE_OEE("v_mps_resource_oee", "工序设备生产效率", "com.yhl.scp.mps.resource.vo.ResourceOeeVO"),
    PRODUCT_ADVANCE_BATCH_RULE("mps_product_advance_batch_rule", "提前生产批次规则", "com.yhl.scp.mps.product.vo" +
            ".ProductAdvanceBatchRuleVO"),
    OUTSOURCE_TRANSFER_SUMMARY("v_mps_outsource_transfer_summary", "委外转产需求汇总", "com.yhl.scp.mps.demand.vo" +
            ".OutsourceTransferSummaryVO"),
    DELIVERY_CHANGE_RECORD("v_mps_delivery_change_record", "发货变更记录", "com.yhl.scp.mps.plan.vo.DeliveryChangeRecordVO"),
    MASTER_PLAN("v_mps_master_plan", "主生产计划", "com.yhl.scp.mps.plan.vo.MasterPlanVO"),
    SUB_CARGO_LOCATION_INFORMATION("mps_sub_cargo_location_information", "子库存货位信息", "com.yhl.scp.mps" +
            ".subCargoLocation.vo.SubCargoLocationVO"),
    HIGH_VALUE_MATERIALS("mps_high_value_materials", "高价值物料", "com.yhl.scp.mps.highValueMaterials.vo" +
            ".MpsHighValueMaterialsVO"),
    PRODUCTION_CAPACITY("mps_production_capacity", "工序后库容量", "com.yhl.scp.mps.productionCapacity.vo" +
            ".ProductionCapacityVO"),
    SPECIAL_OPERATION_PRODUCTION_LIMIT("mps_special_operation_production_limit", "特殊工艺产能约束", "com.yhl.scp.mps" +
            ".productionLimit.vo.ProductionLimitVO"),
    MASTER_PLAN_RELATION("mps_master_plan_relation", "计划单关联", "com.yhl.scp.mps.plan.vo.MasterPlanRelationVO"),
    MASTER_PLAN_DELIVERY_RELATION("mps_master_plan_delivery_relation", "货计划与主生产计划关系", "com.yhl.scp.mps.plan.vo" +
            ".MasterPlanDeliveryRelationVO"),
    REPORTING_FEEDBACK("mps_pro_reporting_feedback", "生产报工反馈", "com.yhl.scp.mps.reportingFeedback.vo" +
            ".MpsProReportingFeedbackVO"),
    ABNORMAL_FEEDBACK("v_mps_pro_abnormal_feedback", "生产异常反馈", "com.yhl.scp.mps.proabnormalfeedback.vo" +
            ".ProAbnormalFeedbackVO"),
    COATING_CHANGE_TIME("mps_coating_change_time", "镀膜切换时间", "com.yhl.scp.mps.coating.vo.MpsCoatingChangeTimeVO"),
    OUTSOURCE_TRANSFER_MATERIAL_DETAIL("mps_outsource_transfer_material_detail", "委外转产材料需求明细", "com.yhl.scp.mps" +
            ".demandMaterial.vo.MpsOutsourceTransferMaterialDetailVO"),
    PRODUCT_DEMAND_MONITOR("mps_pro_product_demand_monitor", "生产齐套生产需求", "com.yhl.scp.mps.productDemandMonitor.vo" +
            ".MpsProProductDemandMonitorVO"),
    // ????
    MATERIAL_SUPPLY_MONITOR("mps_pro_material_supply_monitor", "生产齐套物料供应", "com.yhl.scp.mps.productDemandMonitor.vo" +
            ".ProMaterialSupplyMonitorVO"),
    SPECIAL_TECHNOLOGY_EQUIPMENT_EFFICIENCY("mps_special_technology_equipment_efficiency", "特殊工艺设备效率", "com.yhl.scp" +
            ".mps.equipmentEfficiency.vo.EquipmentEfficiencyVO"),
    OUTSOURCE_TRANSFER_SUPPLY("v_mps_outsource_transfer_supply", "委外转产汇总供料时间", "com.yhl.scp.mps.demand.vo" +
            ".OutsourceTransferSupplyVO"),
    OUTSOURCE_TRANSFER_DEMAND_DETAIL("v_mps_outsource_transfer_demand_detail", "委外转产材料需求明细", "com.yhl.scp.mps.demand" +
            ".vo.OutsourceTransferDemandDetailVO"),
    MASTER_PLAN_ISSUED_DATA("sds_master_plan_issued_data", "主计划发布数据", "com.yhl.scp.mps.plan.vo.MasterPlanIssuedDataVO"),
    MASTER_PLAN_VERSION("sds_master_plan_version", "主计划发布版本表", "com.yhl.scp.mps.plan.vo.MasterPlanVersionVO"),
    ALGORITHM_CONSTRAINT_RULE("mps_algorithm_constraint_rule","算法约束规则","com.yhl.scp.mps.rule.vo.AlgorithmConstraintRuleVO"),

    /********************* MRP *********************/
    MATERIAL_INVENTORY_OUR_FACTORY_DETAIL("v_sds_material_inventory_our_factory_detail", "原片本厂库存批次明细", "com.yhl.scp" +
            ".mrp.inventory.vo.InventoryOurFactoryDetailVO"),
    MATERIAL_INVENTORY_QUAY_DETAIL("v_sds_material_inventory_quay_detail", "原片码头库存批次明细", "com.yhl.scp.mrp.inventory" +
            ".vo.InventoryQuayDetailVO"),
    MATERIAL_INVENTORY_FLOAT_GLASS_DETAIL("v_sds_material_inventory_float_glass_detail", "原片浮法库存批次明细", "com.yhl.scp" +
            ".mrp.inventory.vo.InventoryFloatGlassDetailVO"),
    MATERIAL_INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL("v_sds_material_inventory_float_glass_shipped_detail",
            "原片浮法已发运库存批次明细", "com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO"),
    FULFILLMENT_MANUAL("sds_peg_fulfillment_manual", "手动分配关系", "com.yhl.scp.mrp.inventory.vo.FulfillmentManualVO"),
    MATERIAL_INVENTORY_ALTERNATIVE_RELATIONSHIP("v_mrp_material_inventory_alternative_relationship", "原片库存替代关系", "com" +
            ".yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO"),
    SAFETY_INVENTORY("v_mrp_safety_inventory", "安全库存", "com.yhl.scp.mrp.inventory.vo.SafetyInventoryVO"),
    MATERIAL_SUPPLIER_PURCHASE("mrp_material_supplier_purchase", "材料采购", "com.yhl.scp.mrp.supplier.vo" +
            ".MaterialSupplierPurchaseVO"),
    RESOURCE_PERMISSION("resource_permission", "资源权限", "com.yhl.scp.mps.extension.plan.vo.OperationTaskExtVO"),
    OEM_PERMISSION("oem_permission", "主机厂权限", "com.yhl.scp.dfp.demand.vo.DemandForecastOemVO"),
    // ????
    MATERIAL_ALTERNATIVE("mrp_material_alternative", "材料替代关系", "com.yhl.scp.mrp.alternative.vo" +
            ".MrpMaterialAlternativeVO"),
    PARAM_SETTING("mrp_par_param_setting", "参数设置", "com.yhl.scp.mrp.paramSetting.vo.MrpParParamSettingVO"),
    // ????
    RISK_LEVEL_RULE_PARAMS("risk_level_rule_params", "材料风险等级参数设置", "com.yhl.scp.mrp.risk.vo.RiskLevelRuleParamsVO"),
    MATERIAL_ENQUIRY_TRACK("mrp_material_enquiry_track", "材料到货跟踪", "com.yhl.scp.mrp.enquiry.vo.MaterialEnquiryTrackVO"),
    ORIGINAL_FILM_DEMAND_CONSULT_DETAIL("v_mrp_original_film_demand_consult_detail", "原片需求征询明细", "com.yhl.scp.mrp" +
            ".originalFilm.vo.OriginalFilmDemandConsultDetailVO"),
    ORIGINAL_FILM_DEMAND_CONSULT_SUMMARY("mrp_original_film_demand_consult_summary", "原片需求征询汇总", "com.yhl.scp.mrp" +
            ".originalFilm.vo.OriginalFilmDemandConsultSummaryVO"),
    DELIVERY_PLAN_RECORD("fdp_delivery_plan_record", "发货计划下发记录", "com.yhl.scp.dfp.delivery.vo.DeliveryPlanRecordVO"),
    MATERIAL_ARRIVAL_TRACKING("v_mrp_material_arrival_tracking", "材料到货跟踪", "com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO"),
    MATERIAL_PLAN_NEED("v_mrp_material_plan_need", "材料要货计划", "com.yhl.scp.mrp.extension.material.vo.MaterialPlanNeedVO"),
    MATERIAL_PURCHASE_REQUIREMENT_DETAIL("v_mrp_material_purchase_requirement_detail", "材料采购需求明细", "com.yhl.scp.mrp.extension.material.vo.MaterialPurchaseRequirementDetailVO"),
    NO_GLASS_INVENTORY_SHIFT_DATA("v_mrp_no_glass_inventory_shift_data","非原片库存推移" ,"com.yhl.scp.mrp.material.vo.NoGlassInventoryShiftDataVO"),
    WORK_ORDER_DELETION("sds_ord_work_order_deletion", "已删除制造订单", "com.yhl.scp.mps.order.vo.WorkOrderDeletionVO"),
    SDS_ORD_WORK_ORDER_SUPPLEMENTARY_PUBLISH_LOG("sds_ord_work_order_supplementary_publish_log", "临时计划单", "com.yhl" +
            ".scp.mps.operationPublished.vo.SdsOrdWorkOrderSupplementaryPublishLogVO"),
    ;

    ObjectTypeEnum(String code, String desc, String mappingValue) {
        this.code = code;
        this.desc = desc;
        this.mappingValue = mappingValue;
    }

    /**
     * 表名 / 视图名
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 映射类
     */
    private String mappingValue;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getMappingValue() {
        return mappingValue;
    }

    void setMappingValue(String mappingValue) {
        this.mappingValue = mappingValue;
    }


    /**
     * 根据模块获取对应数据下拉
     *
     * @param moduleCode 模块代码
     * @return java.util.List<com.yhl.platform.common.LabelValue<java.lang.String>>
     */
    public static List<LabelValue<String>> getDropdownByModuleCode(String moduleCode) {
        if (StringUtils.isEmpty(moduleCode)) {
            return new ArrayList<>();
        }
        return Arrays.stream(ObjectTypeEnum.values()).sequential()
                .filter(e -> e.getMappingValue().contains("." + moduleCode.toLowerCase() + "."))
                .map(e -> new LabelValue<>(e.getDesc(), e.getCode())).collect(Collectors.toList());
    }

}
