package com.yhl.scp.ips.system.event.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>EventDependencyConfigPO</code>
 * <p>
 * 事件依赖配置表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
public class EventDependencyConfigPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 628011931752952642L;

    /**
     * 事件定义ID
     */
    private String eventId;
    /**
     * 前置事件定义ID
     */
    private String preEventId;
    /**
     * 事件偏移日
     */
    private Integer dayOffset;
    /**
     * Java代理
     */
    private String javaDelegator;
    /**
     * 是否必须
     */
    private String requisiteFlag;
    /**
     * 版本值
     */
    private Integer versionValue;

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getPreEventId() {
        return preEventId;
    }

    public void setPreEventId(String preEventId) {
        this.preEventId = preEventId;
    }

    public Integer getDayOffset() {
        return dayOffset;
    }

    public void setDayOffset(Integer dayOffset) {
        this.dayOffset = dayOffset;
    }

    public String getJavaDelegator() {
        return javaDelegator;
    }

    public void setJavaDelegator(String javaDelegator) {
        this.javaDelegator = javaDelegator;
    }

    public String getRequisiteFlag() {
        return requisiteFlag;
    }

    public void setRequisiteFlag(String requisiteFlag) {
        this.requisiteFlag = requisiteFlag;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
