package com.yhl.scp.ips.system.event.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.system.event.dto.EventConfigDTO;
import com.yhl.scp.ips.system.event.service.EventConfigService;
import com.yhl.scp.ips.system.event.vo.EventConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>EventConfigController</code>
 * <p>
 * 事件配置表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:09
 */
@Slf4j
@Api(tags = "事件配置表控制器")
@RestController
@RequestMapping("eventConfig")
public class EventConfigController extends BaseController {

    @Resource
    private EventConfigService eventConfigService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<EventConfigVO>> page() {
        List<EventConfigVO> eventConfigList = eventConfigService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<EventConfigVO> pageInfo = new PageInfo<>(eventConfigList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody EventConfigDTO eventConfigDTO) {
        return eventConfigService.doCreate(eventConfigDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody EventConfigDTO eventConfigDTO) {
        return eventConfigService.doUpdate(eventConfigDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        eventConfigService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<EventConfigVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, eventConfigService.selectByPrimaryKey(id));
    }

}
