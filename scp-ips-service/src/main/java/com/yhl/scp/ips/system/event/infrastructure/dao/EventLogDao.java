package com.yhl.scp.ips.system.event.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.system.event.infrastructure.po.EventLogPO;
import com.yhl.scp.ips.system.event.vo.EventLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>EventLogDao</code>
 * <p>
 * 事件日志表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
public interface EventLogDao extends BaseDao<EventLogPO, EventLogVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link EventLogVO}
     */
    List<EventLogVO> selectVOByParams(@Param("params") Map<String, Object> params);

}
