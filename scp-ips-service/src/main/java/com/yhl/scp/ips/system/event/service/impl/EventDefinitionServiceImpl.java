package com.yhl.scp.ips.system.event.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.ips.system.event.convertor.EventDefinitionConvertor;
import com.yhl.scp.ips.system.event.domain.entity.EventDefinitionDO;
import com.yhl.scp.ips.system.event.domain.service.EventDefinitionDomainService;
import com.yhl.scp.ips.system.event.dto.EventDefinitionDTO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventDefinitionDao;
import com.yhl.scp.ips.system.event.infrastructure.po.EventDefinitionPO;
import com.yhl.scp.ips.system.event.service.EventDefinitionService;
import com.yhl.scp.ips.system.event.vo.EventDefinitionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>EventDefinitionServiceImpl</code>
 * <p>
 * 事件定义表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
@Slf4j
@Service
public class EventDefinitionServiceImpl extends AbstractService implements EventDefinitionService {

    @Resource
    private EventDefinitionDao eventDefinitionDao;

    @Resource
    private EventDefinitionDomainService eventDefinitionDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(EventDefinitionDTO eventDefinitionDTO) {
        // 0.数据转换
        EventDefinitionDO eventDefinitionDO = EventDefinitionConvertor.INSTANCE.dto2Do(eventDefinitionDTO);
        EventDefinitionPO eventDefinitionPO = EventDefinitionConvertor.INSTANCE.dto2Po(eventDefinitionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        eventDefinitionDomainService.validation(eventDefinitionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(eventDefinitionPO);
        eventDefinitionDao.insertWithPrimaryKey(eventDefinitionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(EventDefinitionDTO eventDefinitionDTO) {
        // 0.数据转换
        EventDefinitionDO eventDefinitionDO = EventDefinitionConvertor.INSTANCE.dto2Do(eventDefinitionDTO);
        EventDefinitionPO eventDefinitionPO = EventDefinitionConvertor.INSTANCE.dto2Po(eventDefinitionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        eventDefinitionDomainService.validation(eventDefinitionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(eventDefinitionPO);
        eventDefinitionDao.update(eventDefinitionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<EventDefinitionDTO> list) {
        List<EventDefinitionPO> newList = EventDefinitionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        eventDefinitionDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<EventDefinitionDTO> list) {
        List<EventDefinitionPO> newList = EventDefinitionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        eventDefinitionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return eventDefinitionDao.deleteBatch(idList);
        }
        return eventDefinitionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public EventDefinitionVO selectByPrimaryKey(String id) {
        EventDefinitionPO po = eventDefinitionDao.selectByPrimaryKey(id);
        return EventDefinitionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sys_event_definition")
    public List<EventDefinitionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sys_event_definition")
    public List<EventDefinitionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<EventDefinitionVO> dataList = eventDefinitionDao.selectByCondition(sortParam, queryCriteriaParam);
        EventDefinitionServiceImpl target = springBeanUtils.getBean(EventDefinitionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<EventDefinitionVO> selectByParams(Map<String, Object> params) {
        List<EventDefinitionPO> list = eventDefinitionDao.selectByParams(params);
        return EventDefinitionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<EventDefinitionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.EVENT_DEFINITION.getCode();
    }

    @Override
    public List<EventDefinitionVO> invocation(List<EventDefinitionVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
