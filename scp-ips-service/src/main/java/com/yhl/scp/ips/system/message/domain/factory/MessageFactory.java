package com.yhl.scp.ips.system.message.domain.factory;

import com.yhl.scp.ips.system.message.domain.entity.MessageDO;
import com.yhl.scp.ips.system.message.dto.MessageDTO;
import com.yhl.scp.ips.system.message.infrastructure.dao.MessageDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>MessageFactory</code>
 * <p>
 * 消息表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:03
 */
@Component
public class MessageFactory {

    @Resource
    private MessageDao messageDao;

    MessageDO create(MessageDTO dto) {
        // TODO
        return null;
    }

}
