package com.yhl.scp.ips.system.event.convertor;

import com.yhl.scp.ips.system.event.domain.entity.EventDefinitionDO;
import com.yhl.scp.ips.system.event.dto.EventDefinitionDTO;
import com.yhl.scp.ips.system.event.infrastructure.po.EventDefinitionPO;
import com.yhl.scp.ips.system.event.vo.EventDefinitionVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>EventDefinitionConvertor</code>
 * <p>
 * 事件定义表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EventDefinitionConvertor {

    EventDefinitionConvertor INSTANCE = Mappers.getMapper(EventDefinitionConvertor.class);

    EventDefinitionDO dto2Do(EventDefinitionDTO obj);

    List<EventDefinitionDO> dto2Dos(List<EventDefinitionDTO> list);

    EventDefinitionDTO do2Dto(EventDefinitionDO obj);

    List<EventDefinitionDTO> do2Dtos(List<EventDefinitionDO> list);

    EventDefinitionDTO vo2Dto(EventDefinitionVO obj);

    List<EventDefinitionDTO> vo2Dtos(List<EventDefinitionVO> list);

    EventDefinitionVO po2Vo(EventDefinitionPO obj);

    List<EventDefinitionVO> po2Vos(List<EventDefinitionPO> list);

    EventDefinitionPO dto2Po(EventDefinitionDTO obj);

    List<EventDefinitionPO> dto2Pos(List<EventDefinitionDTO> obj);

    EventDefinitionVO do2Vo(EventDefinitionDO obj);

    EventDefinitionPO do2Po(EventDefinitionDO obj);

    EventDefinitionDO po2Do(EventDefinitionPO obj);

}
