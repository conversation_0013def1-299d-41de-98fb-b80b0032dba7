<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.system.event.infrastructure.dao.EventDefinitionDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.system.event.infrastructure.po.EventDefinitionPO">
        <!--@Table sys_event_definition-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="module_code" jdbcType="VARCHAR" property="moduleCode"/>
        <result column="event_code" jdbcType="VARCHAR" property="eventCode"/>
        <result column="event_name" jdbcType="VARCHAR" property="eventName"/>
        <result column="event_type" jdbcType="VARCHAR" property="eventType"/>
        <result column="event_frequency" jdbcType="VARCHAR" property="eventFrequency"/>
        <result column="virtual_flag" jdbcType="VARCHAR" property="virtualFlag"/>
        <result column="trigger_methods" jdbcType="VARCHAR" property="triggerMethods"/>
        <result column="message_type" jdbcType="VARCHAR" property="messageType"/>
        <result column="receiver_type" jdbcType="VARCHAR" property="receiverType"/>
        <result column="receivers" jdbcType="VARCHAR" property="receivers"/>
        <result column="cc_receivers" jdbcType="VARCHAR" property="ccReceivers"/>
        <result column="java_delegator" jdbcType="VARCHAR" property="javaDelegator"/>
        <result column="message_level" jdbcType="VARCHAR" property="messageLevel"/>
        <result column="upgrade_level" jdbcType="INTEGER" property="upgradeLevel"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.system.event.vo.EventDefinitionVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
id,tenant_id,module_code,event_code,event_name,event_type,event_frequency,virtual_flag,trigger_methods,message_type,receiver_type,receivers,cc_receivers,java_delegator,message_level,upgrade_level,remark,version_value,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.tenantId != null and params.tenantId != ''">
                and tenant_id = #{params.tenantId,jdbcType=VARCHAR}
            </if>
            <if test="params.moduleCode != null and params.moduleCode != ''">
                and module_code = #{params.moduleCode,jdbcType=VARCHAR}
            </if>
            <if test="params.eventCode != null and params.eventCode != ''">
                and event_code = #{params.eventCode,jdbcType=VARCHAR}
            </if>
            <if test="params.eventName != null and params.eventName != ''">
                and event_name = #{params.eventName,jdbcType=VARCHAR}
            </if>
            <if test="params.eventType != null and params.eventType != ''">
                and event_type = #{params.eventType,jdbcType=VARCHAR}
            </if>
            <if test="params.eventFrequency != null and params.eventFrequency != ''">
                and event_frequency = #{params.eventFrequency,jdbcType=VARCHAR}
            </if>
            <if test="params.virtualFlag != null and params.virtualFlag != ''">
                and virtual_flag = #{params.virtualFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.triggerMethods != null and params.triggerMethods != ''">
                and trigger_methods = #{params.triggerMethods,jdbcType=VARCHAR}
            </if>
            <if test="params.messageType != null and params.messageType != ''">
                and message_type = #{params.messageType,jdbcType=VARCHAR}
            </if>
            <if test="params.receiverType != null and params.receiverType != ''">
                and receiver_type = #{params.receiverType,jdbcType=VARCHAR}
            </if>
            <if test="params.receivers != null and params.receivers != ''">
                and receivers = #{params.receivers,jdbcType=VARCHAR}
            </if>
            <if test="params.ccReceivers != null and params.ccReceivers != ''">
                and cc_receivers = #{params.ccReceivers,jdbcType=VARCHAR}
            </if>
            <if test="params.javaDelegator != null and params.javaDelegator != ''">
                and java_delegator = #{params.javaDelegator,jdbcType=VARCHAR}
            </if>
            <if test="params.messageLevel != null and params.messageLevel != ''">
                and message_level = #{params.messageLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.upgradeLevel != null">
                and upgrade_level = #{params.upgradeLevel,jdbcType=INTEGER}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_definition
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_definition
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_event_definition
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_definition
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_event_definition
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventDefinitionPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sys_event_definition(
        id,
        tenant_id,
        module_code,
        event_code,
        event_name,
        event_type,
        event_frequency,
        virtual_flag,
        trigger_methods,
        message_type,
        receiver_type,
        receivers,
        cc_receivers,
        java_delegator,
        message_level,
        upgrade_level,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{tenantId,jdbcType=VARCHAR},
        #{moduleCode,jdbcType=VARCHAR},
        #{eventCode,jdbcType=VARCHAR},
        #{eventName,jdbcType=VARCHAR},
        #{eventType,jdbcType=VARCHAR},
        #{eventFrequency,jdbcType=VARCHAR},
        #{virtualFlag,jdbcType=VARCHAR},
        #{triggerMethods,jdbcType=VARCHAR},
        #{messageType,jdbcType=VARCHAR},
        #{receiverType,jdbcType=VARCHAR},
        #{receivers,jdbcType=VARCHAR},
        #{ccReceivers,jdbcType=VARCHAR},
        #{javaDelegator,jdbcType=VARCHAR},
        #{messageLevel,jdbcType=VARCHAR},
        #{upgradeLevel,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventDefinitionPO">
        insert into sys_event_definition(
        id,
        tenant_id,
        module_code,
        event_code,
        event_name,
        event_type,
        event_frequency,
        virtual_flag,
        trigger_methods,
        message_type,
        receiver_type,
        receivers,
        cc_receivers,
        java_delegator,
        message_level,
        upgrade_level,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{tenantId,jdbcType=VARCHAR},
        #{moduleCode,jdbcType=VARCHAR},
        #{eventCode,jdbcType=VARCHAR},
        #{eventName,jdbcType=VARCHAR},
        #{eventType,jdbcType=VARCHAR},
        #{eventFrequency,jdbcType=VARCHAR},
        #{virtualFlag,jdbcType=VARCHAR},
        #{triggerMethods,jdbcType=VARCHAR},
        #{messageType,jdbcType=VARCHAR},
        #{receiverType,jdbcType=VARCHAR},
        #{receivers,jdbcType=VARCHAR},
        #{ccReceivers,jdbcType=VARCHAR},
        #{javaDelegator,jdbcType=VARCHAR},
        #{messageLevel,jdbcType=VARCHAR},
        #{upgradeLevel,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_event_definition(
        id,
        tenant_id,
        module_code,
        event_code,
        event_name,
        event_type,
        event_frequency,
        virtual_flag,
        trigger_methods,
        message_type,
        receiver_type,
        receivers,
        cc_receivers,
        java_delegator,
        message_level,
        upgrade_level,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.tenantId,jdbcType=VARCHAR},
        #{entity.moduleCode,jdbcType=VARCHAR},
        #{entity.eventCode,jdbcType=VARCHAR},
        #{entity.eventName,jdbcType=VARCHAR},
        #{entity.eventType,jdbcType=VARCHAR},
        #{entity.eventFrequency,jdbcType=VARCHAR},
        #{entity.virtualFlag,jdbcType=VARCHAR},
        #{entity.triggerMethods,jdbcType=VARCHAR},
        #{entity.messageType,jdbcType=VARCHAR},
        #{entity.receiverType,jdbcType=VARCHAR},
        #{entity.receivers,jdbcType=VARCHAR},
        #{entity.ccReceivers,jdbcType=VARCHAR},
        #{entity.javaDelegator,jdbcType=VARCHAR},
        #{entity.messageLevel,jdbcType=VARCHAR},
        #{entity.upgradeLevel,jdbcType=INTEGER},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sys_event_definition(
        id,
        tenant_id,
        module_code,
        event_code,
        event_name,
        event_type,
        event_frequency,
        virtual_flag,
        trigger_methods,
        message_type,
        receiver_type,
        receivers,
        cc_receivers,
        java_delegator,
        message_level,
        upgrade_level,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.tenantId,jdbcType=VARCHAR},
        #{entity.moduleCode,jdbcType=VARCHAR},
        #{entity.eventCode,jdbcType=VARCHAR},
        #{entity.eventName,jdbcType=VARCHAR},
        #{entity.eventType,jdbcType=VARCHAR},
        #{entity.eventFrequency,jdbcType=VARCHAR},
        #{entity.virtualFlag,jdbcType=VARCHAR},
        #{entity.triggerMethods,jdbcType=VARCHAR},
        #{entity.messageType,jdbcType=VARCHAR},
        #{entity.receiverType,jdbcType=VARCHAR},
        #{entity.receivers,jdbcType=VARCHAR},
        #{entity.ccReceivers,jdbcType=VARCHAR},
        #{entity.javaDelegator,jdbcType=VARCHAR},
        #{entity.messageLevel,jdbcType=VARCHAR},
        #{entity.upgradeLevel,jdbcType=INTEGER},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventDefinitionPO">
        update sys_event_definition set
        tenant_id = #{tenantId,jdbcType=VARCHAR},
        module_code = #{moduleCode,jdbcType=VARCHAR},
        event_code = #{eventCode,jdbcType=VARCHAR},
        event_name = #{eventName,jdbcType=VARCHAR},
        event_type = #{eventType,jdbcType=VARCHAR},
        event_frequency = #{eventFrequency,jdbcType=VARCHAR},
        virtual_flag = #{virtualFlag,jdbcType=VARCHAR},
        trigger_methods = #{triggerMethods,jdbcType=VARCHAR},
        message_type = #{messageType,jdbcType=VARCHAR},
        receiver_type = #{receiverType,jdbcType=VARCHAR},
        receivers = #{receivers,jdbcType=VARCHAR},
        cc_receivers = #{ccReceivers,jdbcType=VARCHAR},
        java_delegator = #{javaDelegator,jdbcType=VARCHAR},
        message_level = #{messageLevel,jdbcType=VARCHAR},
        upgrade_level = #{upgradeLevel,jdbcType=INTEGER},
        remark = #{remark,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventDefinitionPO">
        update sys_event_definition
        <set>
            <if test="item.tenantId != null and item.tenantId != ''">
                tenant_id = #{item.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="item.moduleCode != null and item.moduleCode != ''">
                module_code = #{item.moduleCode,jdbcType=VARCHAR},
            </if>
            <if test="item.eventCode != null and item.eventCode != ''">
                event_code = #{item.eventCode,jdbcType=VARCHAR},
            </if>
            <if test="item.eventName != null and item.eventName != ''">
                event_name = #{item.eventName,jdbcType=VARCHAR},
            </if>
            <if test="item.eventType != null and item.eventType != ''">
                event_type = #{item.eventType,jdbcType=VARCHAR},
            </if>
            <if test="item.eventFrequency != null and item.eventFrequency != ''">
                event_frequency = #{item.eventFrequency,jdbcType=VARCHAR},
            </if>
            <if test="item.virtualFlag != null and item.virtualFlag != ''">
                virtual_flag = #{item.virtualFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.triggerMethods != null and item.triggerMethods != ''">
                trigger_methods = #{item.triggerMethods,jdbcType=VARCHAR},
            </if>
            <if test="item.messageType != null and item.messageType != ''">
                message_type = #{item.messageType,jdbcType=VARCHAR},
            </if>
            <if test="item.receiverType != null and item.receiverType != ''">
                receiver_type = #{item.receiverType,jdbcType=VARCHAR},
            </if>
            <if test="item.receivers != null and item.receivers != ''">
                receivers = #{item.receivers,jdbcType=VARCHAR},
            </if>
            <if test="item.ccReceivers != null and item.ccReceivers != ''">
                cc_receivers = #{item.ccReceivers,jdbcType=VARCHAR},
            </if>
            <if test="item.javaDelegator != null and item.javaDelegator != ''">
                java_delegator = #{item.javaDelegator,jdbcType=VARCHAR},
            </if>
            <if test="item.messageLevel != null and item.messageLevel != ''">
                message_level = #{item.messageLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.upgradeLevel != null">
                upgrade_level = #{item.upgradeLevel,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sys_event_definition
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tenant_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.tenantId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="module_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.moduleCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="event_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="event_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="event_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="event_frequency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventFrequency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="virtual_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.virtualFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="trigger_methods = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.triggerMethods,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="receiver_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.receiverType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="receivers = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.receivers,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cc_receivers = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ccReceivers,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="java_delegator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.javaDelegator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="upgrade_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.upgradeLevel,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sys_event_definition 
        <set>
            <if test="item.tenantId != null and item.tenantId != ''">
                tenant_id = #{item.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="item.moduleCode != null and item.moduleCode != ''">
                module_code = #{item.moduleCode,jdbcType=VARCHAR},
            </if>
            <if test="item.eventCode != null and item.eventCode != ''">
                event_code = #{item.eventCode,jdbcType=VARCHAR},
            </if>
            <if test="item.eventName != null and item.eventName != ''">
                event_name = #{item.eventName,jdbcType=VARCHAR},
            </if>
            <if test="item.eventType != null and item.eventType != ''">
                event_type = #{item.eventType,jdbcType=VARCHAR},
            </if>
            <if test="item.eventFrequency != null and item.eventFrequency != ''">
                event_frequency = #{item.eventFrequency,jdbcType=VARCHAR},
            </if>
            <if test="item.virtualFlag != null and item.virtualFlag != ''">
                virtual_flag = #{item.virtualFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.triggerMethods != null and item.triggerMethods != ''">
                trigger_methods = #{item.triggerMethods,jdbcType=VARCHAR},
            </if>
            <if test="item.messageType != null and item.messageType != ''">
                message_type = #{item.messageType,jdbcType=VARCHAR},
            </if>
            <if test="item.receiverType != null and item.receiverType != ''">
                receiver_type = #{item.receiverType,jdbcType=VARCHAR},
            </if>
            <if test="item.receivers != null and item.receivers != ''">
                receivers = #{item.receivers,jdbcType=VARCHAR},
            </if>
            <if test="item.ccReceivers != null and item.ccReceivers != ''">
                cc_receivers = #{item.ccReceivers,jdbcType=VARCHAR},
            </if>
            <if test="item.javaDelegator != null and item.javaDelegator != ''">
                java_delegator = #{item.javaDelegator,jdbcType=VARCHAR},
            </if>
            <if test="item.messageLevel != null and item.messageLevel != ''">
                message_level = #{item.messageLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.upgradeLevel != null">
                upgrade_level = #{item.upgradeLevel,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sys_event_definition where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sys_event_definition where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
