package com.yhl.scp.ips.system.event.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.util.Date;

/**
 * <code>EventConfigDO</code>
 * <p>
 * 事件配置表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 20:02:02
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EventConfigDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -53607498680445736L;

/**
     * 主键ID
     */
    private String id;
/**
     * 事件定义ID
     */
    private String eventId;
/**
     * 消息渠道
     */
    private String messageChannel;
/**
     * 消息模板ID
     */
    private String messageTemplateId;
/**
     * 消息主题
     */
    private String messageSubject;
/**
     * 消息内容
     */
    private String messageContent;
/**
     * 消息链接
     */
    private String messageLink;
/**
     * 是否星标
     */
    private String starFlag;
/**
     * 优先级
     */
    private String priority;
/**
     * 版本值
     */
    private Integer versionValue;

}
