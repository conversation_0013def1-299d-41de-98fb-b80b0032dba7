package com.yhl.scp.ips.system.event.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.util.Date;

/**
 * <code>EventLogDO</code>
 * <p>
 * 事件日志表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-21 15:07:25
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EventLogDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -64850553858571742L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 所属场景
     */
    private String scenario;
    /**
     * 事件ID
     */
    private String eventId;
    /**
     * 事件代码
     */
    private String eventCode;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 事件类型
     */
    private String eventType;
    /**
     * 事件频率
     */
    private String eventFrequency;
    /**
     * 触发方式
     */
    private String triggerMethod;
    /**
     * 运行状态
     */
    private String operationStatus;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 版本值
     */
    private Integer versionValue;

}
