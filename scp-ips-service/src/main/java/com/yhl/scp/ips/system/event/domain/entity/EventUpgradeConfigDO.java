package com.yhl.scp.ips.system.event.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.util.Date;

/**
 * <code>EventUpgradeConfigDO</code>
 * <p>
 * 事件升级配置表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EventUpgradeConfigDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -17966514348857007L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 事件定义ID
     */
    private String eventId;
    /**
     * 当前级别
     */
    private Integer currentLevel;
    /**
     * 当前级别名
     */
    private String currentLevelName;
    /**
     * 目标级别
     */
    private Integer targetLevel;
    /**
     * 目标级别名
     */
    private String targetLevelName;
    /**
     * 超时阈值（分钟）
     */
    private Integer timeoutMinutes;
    /**
     * 重试间隔（分钟）
     */
    private Integer retryIntervalMinutes;
    /**
     * 消息渠道
     */
    private String messageChannel;
    /**
     * 消息主题
     */
    private String messageSubject;
    /**
     * 发送人
     */
    private String sender;
    /**
     * 接收类型
     */
    private String receiverType;
    /**
     * 接收人列表
     */
    private String receivers;
    /**
     * 抄送人列表
     */
    private String ccReceivers;
    /**
     * 消息模板ID
     */
    private String messageTemplateId;
    /**
     * 版本值
     */
    private Integer versionValue;

}
