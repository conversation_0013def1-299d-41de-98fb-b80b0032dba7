<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.system.event.infrastructure.dao.EventConfigDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.system.event.infrastructure.po.EventConfigPO">
        <!--@Table sys_event_config-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="event_id" jdbcType="VARCHAR" property="eventId"/>
        <result column="message_channel" jdbcType="VARCHAR" property="messageChannel"/>
        <result column="message_template_id" jdbcType="VARCHAR" property="messageTemplateId"/>
        <result column="message_subject" jdbcType="VARCHAR" property="messageSubject"/>
        <result column="message_content" jdbcType="VARCHAR" property="messageContent"/>
        <result column="message_link" jdbcType="VARCHAR" property="messageLink"/>
        <result column="star_flag" jdbcType="VARCHAR" property="starFlag"/>
        <result column="priority" jdbcType="VARCHAR" property="priority"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.system.event.vo.EventConfigVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
id,event_id,message_channel,message_template_id,message_subject,message_content,message_link,star_flag,priority,remark,version_value,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.eventId != null and params.eventId != ''">
                and event_id = #{params.eventId,jdbcType=VARCHAR}
            </if>
            <if test="params.eventIds != null and params.eventIds.size() > 0">
                and event_id in
                <foreach collection="params.eventIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.messageChannel != null and params.messageChannel != ''">
                and message_channel = #{params.messageChannel,jdbcType=VARCHAR}
            </if>
            <if test="params.messageTemplateId != null and params.messageTemplateId != ''">
                and message_template_id = #{params.messageTemplateId,jdbcType=VARCHAR}
            </if>
            <if test="params.messageSubject != null and params.messageSubject != ''">
                and message_subject = #{params.messageSubject,jdbcType=VARCHAR}
            </if>
            <if test="params.messageContent != null and params.messageContent != ''">
                and message_content = #{params.messageContent,jdbcType=VARCHAR}
            </if>
            <if test="params.messageLink != null and params.messageLink != ''">
                and message_link = #{params.messageLink,jdbcType=VARCHAR}
            </if>
            <if test="params.starFlag != null and params.starFlag != ''">
                and star_flag = #{params.starFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.priority != null and params.priority != ''">
                and priority = #{params.priority,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_config
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_config
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_event_config
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_config
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_event_config
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventConfigPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sys_event_config(
        id,
        event_id,
        message_channel,
        message_template_id,
        message_subject,
        message_content,
        message_link,
        star_flag,
        priority,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{eventId,jdbcType=VARCHAR},
        #{messageChannel,jdbcType=VARCHAR},
        #{messageTemplateId,jdbcType=VARCHAR},
        #{messageSubject,jdbcType=VARCHAR},
        #{messageContent,jdbcType=VARCHAR},
        #{messageLink,jdbcType=VARCHAR},
        #{starFlag,jdbcType=VARCHAR},
        #{priority,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventConfigPO">
        insert into sys_event_config(
        id,
        event_id,
        message_channel,
        message_template_id,
        message_subject,
        message_content,
        message_link,
        star_flag,
        priority,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{eventId,jdbcType=VARCHAR},
        #{messageChannel,jdbcType=VARCHAR},
        #{messageTemplateId,jdbcType=VARCHAR},
        #{messageSubject,jdbcType=VARCHAR},
        #{messageContent,jdbcType=VARCHAR},
        #{messageLink,jdbcType=VARCHAR},
        #{starFlag,jdbcType=VARCHAR},
        #{priority,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_event_config(
        id,
        event_id,
        message_channel,
        message_template_id,
        message_subject,
        message_content,
        message_link,
        star_flag,
        priority,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.eventId,jdbcType=VARCHAR},
        #{entity.messageChannel,jdbcType=VARCHAR},
        #{entity.messageTemplateId,jdbcType=VARCHAR},
        #{entity.messageSubject,jdbcType=VARCHAR},
        #{entity.messageContent,jdbcType=VARCHAR},
        #{entity.messageLink,jdbcType=VARCHAR},
        #{entity.starFlag,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sys_event_config(
        id,
        event_id,
        message_channel,
        message_template_id,
        message_subject,
        message_content,
        message_link,
        star_flag,
        priority,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.eventId,jdbcType=VARCHAR},
        #{entity.messageChannel,jdbcType=VARCHAR},
        #{entity.messageTemplateId,jdbcType=VARCHAR},
        #{entity.messageSubject,jdbcType=VARCHAR},
        #{entity.messageContent,jdbcType=VARCHAR},
        #{entity.messageLink,jdbcType=VARCHAR},
        #{entity.starFlag,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventConfigPO">
        update sys_event_config set
        event_id = #{eventId,jdbcType=VARCHAR},
        message_channel = #{messageChannel,jdbcType=VARCHAR},
        message_template_id = #{messageTemplateId,jdbcType=VARCHAR},
        message_subject = #{messageSubject,jdbcType=VARCHAR},
        message_content = #{messageContent,jdbcType=VARCHAR},
        message_link = #{messageLink,jdbcType=VARCHAR},
        star_flag = #{starFlag,jdbcType=VARCHAR},
        priority = #{priority,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventConfigPO">
        update sys_event_config
        <set>
            <if test="item.eventId != null and item.eventId != ''">
                event_id = #{item.eventId,jdbcType=VARCHAR},
            </if>
            <if test="item.messageChannel != null and item.messageChannel != ''">
                message_channel = #{item.messageChannel,jdbcType=VARCHAR},
            </if>
            <if test="item.messageTemplateId != null and item.messageTemplateId != ''">
                message_template_id = #{item.messageTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="item.messageSubject != null and item.messageSubject != ''">
                message_subject = #{item.messageSubject,jdbcType=VARCHAR},
            </if>
            <if test="item.messageContent != null and item.messageContent != ''">
                message_content = #{item.messageContent,jdbcType=VARCHAR},
            </if>
            <if test="item.messageLink != null and item.messageLink != ''">
                message_link = #{item.messageLink,jdbcType=VARCHAR},
            </if>
            <if test="item.starFlag != null and item.starFlag != ''">
                star_flag = #{item.starFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null and item.priority != ''">
                priority = #{item.priority,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sys_event_config
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="event_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageChannel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_template_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageTemplateId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_subject = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageSubject,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageContent,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_link = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageLink,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="star_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.starFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.priority,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sys_event_config 
        <set>
            <if test="item.eventId != null and item.eventId != ''">
                event_id = #{item.eventId,jdbcType=VARCHAR},
            </if>
            <if test="item.messageChannel != null and item.messageChannel != ''">
                message_channel = #{item.messageChannel,jdbcType=VARCHAR},
            </if>
            <if test="item.messageTemplateId != null and item.messageTemplateId != ''">
                message_template_id = #{item.messageTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="item.messageSubject != null and item.messageSubject != ''">
                message_subject = #{item.messageSubject,jdbcType=VARCHAR},
            </if>
            <if test="item.messageContent != null and item.messageContent != ''">
                message_content = #{item.messageContent,jdbcType=VARCHAR},
            </if>
            <if test="item.messageLink != null and item.messageLink != ''">
                message_link = #{item.messageLink,jdbcType=VARCHAR},
            </if>
            <if test="item.starFlag != null and item.starFlag != ''">
                star_flag = #{item.starFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null and item.priority != ''">
                priority = #{item.priority,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sys_event_config where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sys_event_config where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
