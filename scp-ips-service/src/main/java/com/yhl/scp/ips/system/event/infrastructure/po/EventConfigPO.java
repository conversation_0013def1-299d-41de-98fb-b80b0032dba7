package com.yhl.scp.ips.system.event.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>EventConfigPO</code>
 * <p>
 * 事件配置表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 20:02:03
 */
public class EventConfigPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -83698492850699388L;

    /**
     * 事件定义ID
     */
    private String eventId;
    /**
     * 消息渠道
     */
    private String messageChannel;
    /**
     * 消息模板ID
     */
    private String messageTemplateId;
    /**
     * 消息主题
     */
    private String messageSubject;
    /**
     * 消息内容
     */
    private String messageContent;
    /**
     * 消息链接
     */
    private String messageLink;
    /**
     * 是否星标
     */
    private String starFlag;
    /**
     * 优先级
     */
    private String priority;
    /**
     * 版本值
     */
    private Integer versionValue;

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getMessageChannel() {
        return messageChannel;
    }

    public void setMessageChannel(String messageChannel) {
        this.messageChannel = messageChannel;
    }

    public String getMessageTemplateId() {
        return messageTemplateId;
    }

    public void setMessageTemplateId(String messageTemplateId) {
        this.messageTemplateId = messageTemplateId;
    }

    public String getMessageSubject() {
        return messageSubject;
    }

    public void setMessageSubject(String messageSubject) {
        this.messageSubject = messageSubject;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getMessageLink() {
        return messageLink;
    }

    public void setMessageLink(String messageLink) {
        this.messageLink = messageLink;
    }

    public String getStarFlag() {
        return starFlag;
    }

    public void setStarFlag(String starFlag) {
        this.starFlag = starFlag;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
