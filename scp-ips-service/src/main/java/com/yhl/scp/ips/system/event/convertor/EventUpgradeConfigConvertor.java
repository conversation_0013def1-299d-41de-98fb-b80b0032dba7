package com.yhl.scp.ips.system.event.convertor;

import com.yhl.scp.ips.system.event.domain.entity.EventUpgradeConfigDO;
import com.yhl.scp.ips.system.event.dto.EventUpgradeConfigDTO;
import com.yhl.scp.ips.system.event.infrastructure.po.EventUpgradeConfigPO;
import com.yhl.scp.ips.system.event.vo.EventUpgradeConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>EventUpgradeConfigConvertor</code>
 * <p>
 * 事件升级配置表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EventUpgradeConfigConvertor {

    EventUpgradeConfigConvertor INSTANCE = Mappers.getMapper(EventUpgradeConfigConvertor.class);

    EventUpgradeConfigDO dto2Do(EventUpgradeConfigDTO obj);

    List<EventUpgradeConfigDO> dto2Dos(List<EventUpgradeConfigDTO> list);

    EventUpgradeConfigDTO do2Dto(EventUpgradeConfigDO obj);

    List<EventUpgradeConfigDTO> do2Dtos(List<EventUpgradeConfigDO> list);

    EventUpgradeConfigDTO vo2Dto(EventUpgradeConfigVO obj);

    List<EventUpgradeConfigDTO> vo2Dtos(List<EventUpgradeConfigVO> list);

    EventUpgradeConfigVO po2Vo(EventUpgradeConfigPO obj);

    List<EventUpgradeConfigVO> po2Vos(List<EventUpgradeConfigPO> list);

    EventUpgradeConfigPO dto2Po(EventUpgradeConfigDTO obj);

    List<EventUpgradeConfigPO> dto2Pos(List<EventUpgradeConfigDTO> obj);

    EventUpgradeConfigVO do2Vo(EventUpgradeConfigDO obj);

    EventUpgradeConfigPO do2Po(EventUpgradeConfigDO obj);

    EventUpgradeConfigDO po2Do(EventUpgradeConfigPO obj);

}
