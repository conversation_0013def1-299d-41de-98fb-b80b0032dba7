package com.yhl.scp.ips.system.event.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.system.event.infrastructure.po.EventConfigPO;
import com.yhl.scp.ips.system.event.vo.EventConfigVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>EventConfigDao</code>
 * <p>
 * 事件配置表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
public interface EventConfigDao extends BaseDao<EventConfigPO, EventConfigVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link EventConfigVO}
     */
    List<EventConfigVO> selectVOByParams(@Param("params") Map<String, Object> params);

}
