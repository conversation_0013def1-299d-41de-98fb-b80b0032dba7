package com.yhl.scp.ips.config;

import com.yhl.scp.biz.common.webSocket.CommonWebSocketNewHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

/**
 * <code>MessageWebSocketHandler</code>
 * <p>
 * MessageWebSocketHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-21 23:28:36
 */
@Component
@Slf4j
public class MessageWebSocketHandler extends CommonWebSocketNewHandler {

    /**
     * 从session中数据范围（实际应用中可能从HTTP头或URL参数获取）
     *
     * @param session 会话
     * @return java.lang.String
     */
    protected String getDataRange(WebSocketSession session) {
        return (String) session.getAttributes().get("userId");
    }

}
