package com.yhl.scp.ips.system.event.domain.factory;

import com.yhl.scp.ips.system.event.domain.entity.EventDependencyConfigDO;
import com.yhl.scp.ips.system.event.dto.EventDependencyConfigDTO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventDependencyConfigDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>EventDependencyConfigFactory</code>
 * <p>
 * 事件依赖配置表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Component
public class EventDependencyConfigFactory {

    @Resource
    private EventDependencyConfigDao eventDependencyConfigDao;

    EventDependencyConfigDO create(EventDependencyConfigDTO dto) {
        // TODO
        return null;
    }

}
