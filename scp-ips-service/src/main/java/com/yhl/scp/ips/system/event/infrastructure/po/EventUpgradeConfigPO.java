package com.yhl.scp.ips.system.event.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>EventUpgradeConfigPO</code>
 * <p>
 * 事件升级配置表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
public class EventUpgradeConfigPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 447742868801137361L;

    /**
     * 事件定义ID
     */
    private String eventId;
    /**
     * 当前级别
     */
    private Integer currentLevel;
    /**
     * 当前级别名
     */
    private String currentLevelName;
    /**
     * 目标级别
     */
    private Integer targetLevel;
    /**
     * 目标级别名
     */
    private String targetLevelName;
    /**
     * 超时阈值（分钟）
     */
    private Integer timeoutMinutes;
    /**
     * 重试间隔（分钟）
     */
    private Integer retryIntervalMinutes;
    /**
     * 消息渠道
     */
    private String messageChannel;
    /**
     * 消息主题
     */
    private String messageSubject;
    /**
     * 发送人
     */
    private String sender;
    /**
     * 接收类型
     */
    private String receiverType;
    /**
     * 接收人列表
     */
    private String receivers;
    /**
     * 抄送人列表
     */
    private String ccReceivers;
    /**
     * 消息模板ID
     */
    private String messageTemplateId;
    /**
     * 版本值
     */
    private Integer versionValue;

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public Integer getCurrentLevel() {
        return currentLevel;
    }

    public void setCurrentLevel(Integer currentLevel) {
        this.currentLevel = currentLevel;
    }

    public String getCurrentLevelName() {
        return currentLevelName;
    }

    public void setCurrentLevelName(String currentLevelName) {
        this.currentLevelName = currentLevelName;
    }

    public Integer getTargetLevel() {
        return targetLevel;
    }

    public void setTargetLevel(Integer targetLevel) {
        this.targetLevel = targetLevel;
    }

    public String getTargetLevelName() {
        return targetLevelName;
    }

    public void setTargetLevelName(String targetLevelName) {
        this.targetLevelName = targetLevelName;
    }

    public Integer getTimeoutMinutes() {
        return timeoutMinutes;
    }

    public void setTimeoutMinutes(Integer timeoutMinutes) {
        this.timeoutMinutes = timeoutMinutes;
    }

    public Integer getRetryIntervalMinutes() {
        return retryIntervalMinutes;
    }

    public void setRetryIntervalMinutes(Integer retryIntervalMinutes) {
        this.retryIntervalMinutes = retryIntervalMinutes;
    }

    public String getMessageChannel() {
        return messageChannel;
    }

    public void setMessageChannel(String messageChannel) {
        this.messageChannel = messageChannel;
    }

    public String getMessageSubject() {
        return messageSubject;
    }

    public void setMessageSubject(String messageSubject) {
        this.messageSubject = messageSubject;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getReceiverType() {
        return receiverType;
    }

    public void setReceiverType(String receiverType) {
        this.receiverType = receiverType;
    }

    public String getReceivers() {
        return receivers;
    }

    public void setReceivers(String receivers) {
        this.receivers = receivers;
    }

    public String getCcReceivers() {
        return ccReceivers;
    }

    public void setCcReceivers(String ccReceivers) {
        this.ccReceivers = ccReceivers;
    }

    public String getMessageTemplateId() {
        return messageTemplateId;
    }

    public void setMessageTemplateId(String messageTemplateId) {
        this.messageTemplateId = messageTemplateId;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
