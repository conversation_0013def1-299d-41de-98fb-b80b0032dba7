package com.yhl.scp.ips.mq.receiver;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yhl.scp.ips.config.MessageWebSocketHandler;
import com.yhl.scp.ips.constant.MqConstants;
import com.yhl.scp.ips.rbac.dao.UserDao;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.system.message.dto.MessageDTO;
import com.yhl.scp.ips.system.message.enums.MessageChannelEnum;
import com.yhl.scp.ips.system.message.enums.ReceiverTypeEnum;
import com.yhl.scp.ips.system.message.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class MqMessageReceiver {

    @Resource
    private MessageService messageService;

    @Resource
    private UserDao userDao;

    @Resource
    private MessageWebSocketHandler messageWebSocketHandler;

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = "${spring.profiles.active}."
                    + MqConstants.COMMON_MESSAGE_QUEUE, ignoreDeclarationExceptions = "true"),
            exchange = @Exchange(value = "${spring.profiles.active}." + MqConstants.BPIM_EVENT_EXCHANGE,
                    ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC),
            key = "${spring.profiles.active}." + MqConstants.COMMON_MESSAGE_KEY))
    public void receiveCommonMassage(String data) {
        try {
            log.info("接收到RabbitMQ数据（common-message）消息,开始处理");
            if (StringUtils.isBlank(data)) {
                log.warn("RabbitMQ数据（common-message）消息为空");
                return;
            }
            List<MessageDTO> messages = JSONObject.parseArray(data, MessageDTO.class);
            log.info("接收到RabbitMQ数据（common-message）消息,总条数:{}", messages.size());
            // 消息落表
            messageService.doCreateBatch(messages);
            // 发送消息
            for (MessageDTO message : messages) {
                String messageChannel = message.getMessageChannel();
                String receiverType = message.getReceiverType();
                String receivers = message.getReceivers();
                List<String> userIds = getReceivers(receiverType, receivers);
                if (MessageChannelEnum.IN_SITE.getCode().equals(messageChannel)) {
                    for (String userId : userIds) {
                        messageWebSocketHandler.sendMessageToUser(userId, message);
                    }
                } else if (MessageChannelEnum.WE_COM.getCode().equals(messageChannel)) {
                    // TODO
                } else if (MessageChannelEnum.E_MAIL.getCode().equals(messageChannel)){
                    // TODO
                } else {
                    log.warn("不支持的消息渠道:{}", messageChannel);
                }
            }
            log.info("处理RabbitMQ数据（common-message）消息结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private List<String> getReceivers(String receiverType, String receivers) {
        if (ReceiverTypeEnum.SINGLE_PERSON.getCode().equals(receiverType)) {
            return Collections.singletonList(receivers);
        } else if (ReceiverTypeEnum.MULTI_PEOPLE.getCode().equals(receiverType)) {
            return Lists.newArrayList(receivers.split(","));
        } else if (ReceiverTypeEnum.ROLE.getCode().equals(receiverType)) {
            String[] roleIds = receivers.split(",");
            return userDao.selectByRoleIds(Lists.newArrayList(roleIds)).stream().map(User::getId).collect(Collectors.toList());
        } else if (ReceiverTypeEnum.DEPARTMENT.getCode().equals(receiverType)) {
            String[] deptIds = receivers.split(",");
            return userDao.selectByDeptIds(Lists.newArrayList(deptIds)).stream().map(User::getId).collect(Collectors.toList());
        } else if (ReceiverTypeEnum.EXPRESSION.getCode().equals(receiverType)) {
            // TODO
        } else {
            log.warn("不支持的消息接收者类型:{}", receiverType);
        }
        return null;
    }

}