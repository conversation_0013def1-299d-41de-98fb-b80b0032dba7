package com.yhl.scp.ips.system.event.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.system.event.infrastructure.po.EventDefinitionPO;
import com.yhl.scp.ips.system.event.vo.EventDefinitionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>EventDefinitionDao</code>
 * <p>
 * 事件定义表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
public interface EventDefinitionDao extends BaseDao<EventDefinitionPO, EventDefinitionVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link EventDefinitionVO}
     */
    List<EventDefinitionVO> selectVOByParams(@Param("params") Map<String, Object> params);

}
