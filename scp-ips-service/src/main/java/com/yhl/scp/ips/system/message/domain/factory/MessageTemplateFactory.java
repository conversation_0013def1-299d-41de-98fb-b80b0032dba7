package com.yhl.scp.ips.system.message.domain.factory;

import com.yhl.scp.ips.system.message.domain.entity.MessageTemplateDO;
import com.yhl.scp.ips.system.message.dto.MessageTemplateDTO;
import com.yhl.scp.ips.system.message.infrastructure.dao.MessageTemplateDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>MessageTemplateFactory</code>
 * <p>
 * 消息模板表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:04
 */
@Component
public class MessageTemplateFactory {

    @Resource
    private MessageTemplateDao messageTemplateDao;

    MessageTemplateDO create(MessageTemplateDTO dto) {
        // TODO
        return null;
    }

}
