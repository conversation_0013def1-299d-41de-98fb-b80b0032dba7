package com.yhl.scp.ips.system.message.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.system.message.dto.MessageDTO;
import com.yhl.scp.ips.system.message.service.MessageService;
import com.yhl.scp.ips.system.message.vo.MessageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MessageController</code>
 * <p>
 * 消息表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:59:58
 */
@Slf4j
@Api(tags = "消息表控制器")
@RestController
@RequestMapping("message")
public class MessageController extends BaseController {

    @Resource
    private MessageService messageService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MessageVO>> page() {
        List<MessageVO> messageList = messageService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MessageVO> pageInfo = new PageInfo<>(messageList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MessageDTO messageDTO) {
        return messageService.doCreate(messageDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MessageDTO messageDTO) {
        return messageService.doUpdate(messageDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        messageService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MessageVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, messageService.selectByPrimaryKey(id));
    }

}
