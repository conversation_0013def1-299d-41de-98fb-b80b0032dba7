package com.yhl.scp.ips.system.event.domain.service;

import com.yhl.scp.ips.system.event.domain.entity.EventUpgradeConfigDO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventUpgradeConfigDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>EventUpgradeConfigDomainService</code>
 * <p>
 * 事件升级配置表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Service
public class EventUpgradeConfigDomainService {

    @Resource
    private EventUpgradeConfigDao eventUpgradeConfigDao;

    /**
     * 数据校验
     *
     * @param eventUpgradeConfigDO 领域对象
     */
    public void validation(EventUpgradeConfigDO eventUpgradeConfigDO) {
        checkNotNull(eventUpgradeConfigDO);
        checkUniqueCode(eventUpgradeConfigDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param eventUpgradeConfigDO 领域对象
     */
    private void checkNotNull(EventUpgradeConfigDO eventUpgradeConfigDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param eventUpgradeConfigDO 领域对象
     */
    private void checkUniqueCode(EventUpgradeConfigDO eventUpgradeConfigDO) {
        // TODO
    }

}
