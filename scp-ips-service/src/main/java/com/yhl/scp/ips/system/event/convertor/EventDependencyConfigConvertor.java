package com.yhl.scp.ips.system.event.convertor;

import com.yhl.scp.ips.system.event.domain.entity.EventDependencyConfigDO;
import com.yhl.scp.ips.system.event.dto.EventDependencyConfigDTO;
import com.yhl.scp.ips.system.event.infrastructure.po.EventDependencyConfigPO;
import com.yhl.scp.ips.system.event.vo.EventDependencyConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>EventDependencyConfigConvertor</code>
 * <p>
 * 事件依赖配置表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EventDependencyConfigConvertor {

    EventDependencyConfigConvertor INSTANCE = Mappers.getMapper(EventDependencyConfigConvertor.class);

    EventDependencyConfigDO dto2Do(EventDependencyConfigDTO obj);

    List<EventDependencyConfigDO> dto2Dos(List<EventDependencyConfigDTO> list);

    EventDependencyConfigDTO do2Dto(EventDependencyConfigDO obj);

    List<EventDependencyConfigDTO> do2Dtos(List<EventDependencyConfigDO> list);

    EventDependencyConfigDTO vo2Dto(EventDependencyConfigVO obj);

    List<EventDependencyConfigDTO> vo2Dtos(List<EventDependencyConfigVO> list);

    EventDependencyConfigVO po2Vo(EventDependencyConfigPO obj);

    List<EventDependencyConfigVO> po2Vos(List<EventDependencyConfigPO> list);

    EventDependencyConfigPO dto2Po(EventDependencyConfigDTO obj);

    List<EventDependencyConfigPO> dto2Pos(List<EventDependencyConfigDTO> obj);

    EventDependencyConfigVO do2Vo(EventDependencyConfigDO obj);

    EventDependencyConfigPO do2Po(EventDependencyConfigDO obj);

    EventDependencyConfigDO po2Do(EventDependencyConfigPO obj);

}
