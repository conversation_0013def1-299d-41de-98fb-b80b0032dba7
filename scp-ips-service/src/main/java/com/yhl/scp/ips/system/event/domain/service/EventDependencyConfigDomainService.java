package com.yhl.scp.ips.system.event.domain.service;

import com.yhl.scp.ips.system.event.domain.entity.EventDependencyConfigDO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventDependencyConfigDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>EventDependencyConfigDomainService</code>
 * <p>
 * 事件依赖配置表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Service
public class EventDependencyConfigDomainService {

    @Resource
    private EventDependencyConfigDao eventDependencyConfigDao;

    /**
     * 数据校验
     *
     * @param eventDependencyConfigDO 领域对象
     */
    public void validation(EventDependencyConfigDO eventDependencyConfigDO) {
        checkNotNull(eventDependencyConfigDO);
        checkUniqueCode(eventDependencyConfigDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param eventDependencyConfigDO 领域对象
     */
    private void checkNotNull(EventDependencyConfigDO eventDependencyConfigDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param eventDependencyConfigDO 领域对象
     */
    private void checkUniqueCode(EventDependencyConfigDO eventDependencyConfigDO) {
        // TODO
    }

}
