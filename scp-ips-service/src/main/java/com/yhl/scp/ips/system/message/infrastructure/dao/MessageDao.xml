<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.system.message.infrastructure.dao.MessageDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.system.message.infrastructure.po.MessagePO">
        <!--@Table sys_message-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="scenario" jdbcType="VARCHAR" property="scenario"/>
        <result column="event_id" jdbcType="VARCHAR" property="eventId"/>
        <result column="todo_id" jdbcType="VARCHAR" property="todoId"/>
        <result column="message_category" jdbcType="VARCHAR" property="messageCategory"/>
        <result column="message_type" jdbcType="VARCHAR" property="messageType"/>
        <result column="message_level" jdbcType="VARCHAR" property="messageLevel"/>
        <result column="message_channel" jdbcType="VARCHAR" property="messageChannel"/>
        <result column="message_subject" jdbcType="VARCHAR" property="messageSubject"/>
        <result column="message_content" jdbcType="VARCHAR" property="messageContent"/>
        <result column="message_link" jdbcType="VARCHAR" property="messageLink"/>
        <result column="sender" jdbcType="VARCHAR" property="sender"/>
        <result column="receiver_type" jdbcType="VARCHAR" property="receiverType"/>
        <result column="receivers" jdbcType="VARCHAR" property="receivers"/>
        <result column="cc_receivers" jdbcType="VARCHAR" property="ccReceivers"/>
        <result column="read_flag" jdbcType="VARCHAR" property="readFlag"/>
        <result column="process_status" jdbcType="VARCHAR" property="processStatus"/>
        <result column="send_time" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.system.message.vo.MessageVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,scenario,event_id,todo_id,message_category,message_type,message_level,message_channel,message_subject,message_content,message_link,sender,receiver_type,receivers,cc_receivers,read_flag,process_status,send_time,remark,version_value,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.scenario != null and params.scenario != ''">
                and scenario = #{params.scenario,jdbcType=VARCHAR}
            </if>
            <if test="params.eventId != null and params.eventId != ''">
                and event_id = #{params.eventId,jdbcType=VARCHAR}
            </if>
            <if test="params.todoId != null and params.todoId != ''">
                and todo_id = #{params.todoId,jdbcType=VARCHAR}
            </if>
            <if test="params.messageCategory != null and params.messageCategory != ''">
                and message_category = #{params.messageCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.messageType != null and params.messageType != ''">
                and message_type = #{params.messageType,jdbcType=VARCHAR}
            </if>
            <if test="params.messageLevel != null and params.messageLevel != ''">
                and message_level = #{params.messageLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.messageChannel != null and params.messageChannel != ''">
                and message_channel = #{params.messageChannel,jdbcType=VARCHAR}
            </if>
            <if test="params.messageSubject != null and params.messageSubject != ''">
                and message_subject = #{params.messageSubject,jdbcType=VARCHAR}
            </if>
            <if test="params.messageContent != null and params.messageContent != ''">
                and message_content = #{params.messageContent,jdbcType=VARCHAR}
            </if>
            <if test="params.messageLink != null and params.messageLink != ''">
                and message_link = #{params.messageLink,jdbcType=VARCHAR}
            </if>
            <if test="params.sender != null and params.sender != ''">
                and sender = #{params.sender,jdbcType=VARCHAR}
            </if>
            <if test="params.receiverType != null and params.receiverType != ''">
                and receiver_type = #{params.receiverType,jdbcType=VARCHAR}
            </if>
            <if test="params.receivers != null and params.receivers != ''">
                and receivers = #{params.receivers,jdbcType=VARCHAR}
            </if>
            <if test="params.ccReceivers != null and params.ccReceivers != ''">
                and cc_receivers = #{params.ccReceivers,jdbcType=VARCHAR}
            </if>
            <if test="params.readFlag != null and params.readFlag != ''">
                and read_flag = #{params.readFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.processStatus != null and params.processStatus != ''">
                and process_status = #{params.processStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.sendTime != null">
                and send_time = #{params.sendTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_message
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_message
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_message
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_message
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_message
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.system.message.infrastructure.po.MessagePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sys_message(
        id,
        scenario,
        event_id,
        todo_id,
        message_category,
        message_type,
        message_level,
        message_channel,
        message_subject,
        message_content,
        message_link,
        sender,
        receiver_type,
        receivers,
        cc_receivers,
        read_flag,
        process_status,
        send_time,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{scenario,jdbcType=VARCHAR},
        #{eventId,jdbcType=VARCHAR},
        #{todoId,jdbcType=VARCHAR},
        #{messageCategory,jdbcType=VARCHAR},
        #{messageType,jdbcType=VARCHAR},
        #{messageLevel,jdbcType=VARCHAR},
        #{messageChannel,jdbcType=VARCHAR},
        #{messageSubject,jdbcType=VARCHAR},
        #{messageContent,jdbcType=VARCHAR},
        #{messageLink,jdbcType=VARCHAR},
        #{sender,jdbcType=VARCHAR},
        #{receiverType,jdbcType=VARCHAR},
        #{receivers,jdbcType=VARCHAR},
        #{ccReceivers,jdbcType=VARCHAR},
        #{readFlag,jdbcType=VARCHAR},
        #{processStatus,jdbcType=VARCHAR},
        #{sendTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.system.message.infrastructure.po.MessagePO">
        insert into sys_message(
        id,
        scenario,
        event_id,
        todo_id,
        message_category,
        message_type,
        message_level,
        message_channel,
        message_subject,
        message_content,
        message_link,
        sender,
        receiver_type,
        receivers,
        cc_receivers,
        read_flag,
        process_status,
        send_time,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{scenario,jdbcType=VARCHAR},
        #{eventId,jdbcType=VARCHAR},
        #{todoId,jdbcType=VARCHAR},
        #{messageCategory,jdbcType=VARCHAR},
        #{messageType,jdbcType=VARCHAR},
        #{messageLevel,jdbcType=VARCHAR},
        #{messageChannel,jdbcType=VARCHAR},
        #{messageSubject,jdbcType=VARCHAR},
        #{messageContent,jdbcType=VARCHAR},
        #{messageLink,jdbcType=VARCHAR},
        #{sender,jdbcType=VARCHAR},
        #{receiverType,jdbcType=VARCHAR},
        #{receivers,jdbcType=VARCHAR},
        #{ccReceivers,jdbcType=VARCHAR},
        #{readFlag,jdbcType=VARCHAR},
        #{processStatus,jdbcType=VARCHAR},
        #{sendTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_message(
        id,
        scenario,
        event_id,
        todo_id,
        message_category,
        message_type,
        message_level,
        message_channel,
        message_subject,
        message_content,
        message_link,
        sender,
        receiver_type,
        receivers,
        cc_receivers,
        read_flag,
        process_status,
        send_time,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.scenario,jdbcType=VARCHAR},
        #{entity.eventId,jdbcType=VARCHAR},
        #{entity.todoId,jdbcType=VARCHAR},
        #{entity.messageCategory,jdbcType=VARCHAR},
        #{entity.messageType,jdbcType=VARCHAR},
        #{entity.messageLevel,jdbcType=VARCHAR},
        #{entity.messageChannel,jdbcType=VARCHAR},
        #{entity.messageSubject,jdbcType=VARCHAR},
        #{entity.messageContent,jdbcType=VARCHAR},
        #{entity.messageLink,jdbcType=VARCHAR},
        #{entity.sender,jdbcType=VARCHAR},
        #{entity.receiverType,jdbcType=VARCHAR},
        #{entity.receivers,jdbcType=VARCHAR},
        #{entity.ccReceivers,jdbcType=VARCHAR},
        #{entity.readFlag,jdbcType=VARCHAR},
        #{entity.processStatus,jdbcType=VARCHAR},
        #{entity.sendTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sys_message(
        id,
        scenario,
        event_id,
        todo_id,
        message_category,
        message_type,
        message_level,
        message_channel,
        message_subject,
        message_content,
        message_link,
        sender,
        receiver_type,
        receivers,
        cc_receivers,
        read_flag,
        process_status,
        send_time,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.scenario,jdbcType=VARCHAR},
        #{entity.eventId,jdbcType=VARCHAR},
        #{entity.todoId,jdbcType=VARCHAR},
        #{entity.messageCategory,jdbcType=VARCHAR},
        #{entity.messageType,jdbcType=VARCHAR},
        #{entity.messageLevel,jdbcType=VARCHAR},
        #{entity.messageChannel,jdbcType=VARCHAR},
        #{entity.messageSubject,jdbcType=VARCHAR},
        #{entity.messageContent,jdbcType=VARCHAR},
        #{entity.messageLink,jdbcType=VARCHAR},
        #{entity.sender,jdbcType=VARCHAR},
        #{entity.receiverType,jdbcType=VARCHAR},
        #{entity.receivers,jdbcType=VARCHAR},
        #{entity.ccReceivers,jdbcType=VARCHAR},
        #{entity.readFlag,jdbcType=VARCHAR},
        #{entity.processStatus,jdbcType=VARCHAR},
        #{entity.sendTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.system.message.infrastructure.po.MessagePO">
        update sys_message set
        scenario = #{scenario,jdbcType=VARCHAR},
        event_id = #{eventId,jdbcType=VARCHAR},
        todo_id = #{todoId,jdbcType=VARCHAR},
        message_category = #{messageCategory,jdbcType=VARCHAR},
        message_type = #{messageType,jdbcType=VARCHAR},
        message_level = #{messageLevel,jdbcType=VARCHAR},
        message_channel = #{messageChannel,jdbcType=VARCHAR},
        message_subject = #{messageSubject,jdbcType=VARCHAR},
        message_content = #{messageContent,jdbcType=VARCHAR},
        message_link = #{messageLink,jdbcType=VARCHAR},
        sender = #{sender,jdbcType=VARCHAR},
        receiver_type = #{receiverType,jdbcType=VARCHAR},
        receivers = #{receivers,jdbcType=VARCHAR},
        cc_receivers = #{ccReceivers,jdbcType=VARCHAR},
        read_flag = #{readFlag,jdbcType=VARCHAR},
        process_status = #{processStatus,jdbcType=VARCHAR},
        send_time = #{sendTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.system.message.infrastructure.po.MessagePO">
        update sys_message
        <set>
            <if test="item.scenario != null and item.scenario != ''">
                scenario = #{item.scenario,jdbcType=VARCHAR},
            </if>
            <if test="item.eventId != null and item.eventId != ''">
                event_id = #{item.eventId,jdbcType=VARCHAR},
            </if>
            <if test="item.todoId != null and item.todoId != ''">
                todo_id = #{item.todoId,jdbcType=VARCHAR},
            </if>
            <if test="item.messageCategory != null and item.messageCategory != ''">
                message_category = #{item.messageCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.messageType != null and item.messageType != ''">
                message_type = #{item.messageType,jdbcType=VARCHAR},
            </if>
            <if test="item.messageLevel != null and item.messageLevel != ''">
                message_level = #{item.messageLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.messageChannel != null and item.messageChannel != ''">
                message_channel = #{item.messageChannel,jdbcType=VARCHAR},
            </if>
            <if test="item.messageSubject != null and item.messageSubject != ''">
                message_subject = #{item.messageSubject,jdbcType=VARCHAR},
            </if>
            <if test="item.messageContent != null and item.messageContent != ''">
                message_content = #{item.messageContent,jdbcType=VARCHAR},
            </if>
            <if test="item.messageLink != null and item.messageLink != ''">
                message_link = #{item.messageLink,jdbcType=VARCHAR},
            </if>
            <if test="item.sender != null and item.sender != ''">
                sender = #{item.sender,jdbcType=VARCHAR},
            </if>
            <if test="item.receiverType != null and item.receiverType != ''">
                receiver_type = #{item.receiverType,jdbcType=VARCHAR},
            </if>
            <if test="item.receivers != null and item.receivers != ''">
                receivers = #{item.receivers,jdbcType=VARCHAR},
            </if>
            <if test="item.ccReceivers != null and item.ccReceivers != ''">
                cc_receivers = #{item.ccReceivers,jdbcType=VARCHAR},
            </if>
            <if test="item.readFlag != null and item.readFlag != ''">
                read_flag = #{item.readFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.processStatus != null and item.processStatus != ''">
                process_status = #{item.processStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.sendTime != null">
                send_time = #{item.sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sys_message
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="scenario = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scenario,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="event_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="todo_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.todoId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageChannel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_subject = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageSubject,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageContent,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_link = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageLink,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sender = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sender,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="receiver_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.receiverType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="receivers = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.receivers,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cc_receivers = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ccReceivers,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="read_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.readFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="process_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.processStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="send_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sendTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sys_message 
        <set>
            <if test="item.scenario != null and item.scenario != ''">
                scenario = #{item.scenario,jdbcType=VARCHAR},
            </if>
            <if test="item.eventId != null and item.eventId != ''">
                event_id = #{item.eventId,jdbcType=VARCHAR},
            </if>
            <if test="item.todoId != null and item.todoId != ''">
                todo_id = #{item.todoId,jdbcType=VARCHAR},
            </if>
            <if test="item.messageCategory != null and item.messageCategory != ''">
                message_category = #{item.messageCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.messageType != null and item.messageType != ''">
                message_type = #{item.messageType,jdbcType=VARCHAR},
            </if>
            <if test="item.messageLevel != null and item.messageLevel != ''">
                message_level = #{item.messageLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.messageChannel != null and item.messageChannel != ''">
                message_channel = #{item.messageChannel,jdbcType=VARCHAR},
            </if>
            <if test="item.messageSubject != null and item.messageSubject != ''">
                message_subject = #{item.messageSubject,jdbcType=VARCHAR},
            </if>
            <if test="item.messageContent != null and item.messageContent != ''">
                message_content = #{item.messageContent,jdbcType=VARCHAR},
            </if>
            <if test="item.messageLink != null and item.messageLink != ''">
                message_link = #{item.messageLink,jdbcType=VARCHAR},
            </if>
            <if test="item.sender != null and item.sender != ''">
                sender = #{item.sender,jdbcType=VARCHAR},
            </if>
            <if test="item.receiverType != null and item.receiverType != ''">
                receiver_type = #{item.receiverType,jdbcType=VARCHAR},
            </if>
            <if test="item.receivers != null and item.receivers != ''">
                receivers = #{item.receivers,jdbcType=VARCHAR},
            </if>
            <if test="item.ccReceivers != null and item.ccReceivers != ''">
                cc_receivers = #{item.ccReceivers,jdbcType=VARCHAR},
            </if>
            <if test="item.readFlag != null and item.readFlag != ''">
                read_flag = #{item.readFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.processStatus != null and item.processStatus != ''">
                process_status = #{item.processStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.sendTime != null">
                send_time = #{item.sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sys_message where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sys_message where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
