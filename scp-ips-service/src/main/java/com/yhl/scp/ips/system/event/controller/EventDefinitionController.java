package com.yhl.scp.ips.system.event.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.system.event.dto.EventDefinitionDTO;
import com.yhl.scp.ips.system.event.service.EventDefinitionService;
import com.yhl.scp.ips.system.event.vo.EventDefinitionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>EventDefinitionController</code>
 * <p>
 * 事件定义表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
@Slf4j
@Api(tags = "事件定义表控制器")
@RestController
@RequestMapping("eventDefinition")
public class EventDefinitionController extends BaseController {

    @Resource
    private EventDefinitionService eventDefinitionService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<EventDefinitionVO>> page() {
        List<EventDefinitionVO> eventDefinitionList = eventDefinitionService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<EventDefinitionVO> pageInfo = new PageInfo<>(eventDefinitionList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody EventDefinitionDTO eventDefinitionDTO) {
        return eventDefinitionService.doCreate(eventDefinitionDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody EventDefinitionDTO eventDefinitionDTO) {
        return eventDefinitionService.doUpdate(eventDefinitionDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        eventDefinitionService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<EventDefinitionVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, eventDefinitionService.selectByPrimaryKey(id));
    }

}
