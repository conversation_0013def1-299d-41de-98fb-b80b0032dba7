package com.yhl.scp.ips.system.event.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>EventLogPO</code>
 * <p>
 * 事件日志表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-21 15:07:25
 */
public class EventLogPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 933745785908222981L;

    /**
     * 所属场景
     */
    private String scenario;
    /**
     * 事件ID
     */
    private String eventId;
    /**
     * 事件代码
     */
    private String eventCode;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 事件类型
     */
    private String eventType;
    /**
     * 事件频率
     */
    private String eventFrequency;
    /**
     * 触发方式
     */
    private String triggerMethod;
    /**
     * 运行状态
     */
    private String operationStatus;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 版本值
     */
    private Integer versionValue;

    public String getScenario() {
        return scenario;
    }

    public void setScenario(String scenario) {
        this.scenario = scenario;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventFrequency() {
        return eventFrequency;
    }

    public void setEventFrequency(String eventFrequency) {
        this.eventFrequency = eventFrequency;
    }

    public String getTriggerMethod() {
        return triggerMethod;
    }

    public void setTriggerMethod(String triggerMethod) {
        this.triggerMethod = triggerMethod;
    }

    public String getOperationStatus() {
        return operationStatus;
    }

    public void setOperationStatus(String operationStatus) {
        this.operationStatus = operationStatus;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
