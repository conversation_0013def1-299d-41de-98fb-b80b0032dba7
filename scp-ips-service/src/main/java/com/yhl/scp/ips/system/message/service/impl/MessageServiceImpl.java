package com.yhl.scp.ips.system.message.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.ips.system.message.convertor.MessageConvertor;
import com.yhl.scp.ips.system.message.domain.entity.MessageDO;
import com.yhl.scp.ips.system.message.domain.service.MessageDomainService;
import com.yhl.scp.ips.system.message.dto.MessageDTO;
import com.yhl.scp.ips.system.message.infrastructure.dao.MessageDao;
import com.yhl.scp.ips.system.message.infrastructure.po.MessagePO;
import com.yhl.scp.ips.system.message.service.MessageService;
import com.yhl.scp.ips.system.message.vo.MessageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MessageServiceImpl</code>
 * <p>
 * 消息表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:00
 */
@Slf4j
@Service
public class MessageServiceImpl extends AbstractService implements MessageService {

    @Resource
    private MessageDao messageDao;

    @Resource
    private MessageDomainService messageDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(MessageDTO messageDTO) {
        // 0.数据转换
        MessageDO messageDO = MessageConvertor.INSTANCE.dto2Do(messageDTO);
        MessagePO messagePO = MessageConvertor.INSTANCE.dto2Po(messageDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        messageDomainService.validation(messageDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(messagePO);
        messageDao.insertWithPrimaryKey(messagePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MessageDTO messageDTO) {
        // 0.数据转换
        MessageDO messageDO = MessageConvertor.INSTANCE.dto2Do(messageDTO);
        MessagePO messagePO = MessageConvertor.INSTANCE.dto2Po(messageDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        messageDomainService.validation(messageDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(messagePO);
        messageDao.update(messagePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MessageDTO> list) {
        List<MessagePO> newList = MessageConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        messageDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MessageDTO> list) {
        List<MessagePO> newList = MessageConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        messageDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return messageDao.deleteBatch(idList);
        }
        return messageDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MessageVO selectByPrimaryKey(String id) {
        MessagePO po = messageDao.selectByPrimaryKey(id);
        return MessageConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sys_message")
    public List<MessageVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sys_message")
    public List<MessageVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MessageVO> dataList = messageDao.selectByCondition(sortParam, queryCriteriaParam);
        MessageServiceImpl target = springBeanUtils.getBean(MessageServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MessageVO> selectByParams(Map<String, Object> params) {
        List<MessagePO> list = messageDao.selectByParams(params);
        return MessageConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MessageVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MESSAGE.getCode();
    }

    @Override
    public List<MessageVO> invocation(List<MessageVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
