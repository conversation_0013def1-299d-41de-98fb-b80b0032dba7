package com.yhl.scp.ips.system.message.convertor;

import com.yhl.scp.ips.system.message.domain.entity.MessageDO;
import com.yhl.scp.ips.system.message.dto.MessageDTO;
import com.yhl.scp.ips.system.message.infrastructure.po.MessagePO;
import com.yhl.scp.ips.system.message.vo.MessageVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MessageConvertor</code>
 * <p>
 * 消息表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:02
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MessageConvertor {

    MessageConvertor INSTANCE = Mappers.getMapper(MessageConvertor.class);

    MessageDO dto2Do(MessageDTO obj);

    List<MessageDO> dto2Dos(List<MessageDTO> list);

    MessageDTO do2Dto(MessageDO obj);

    List<MessageDTO> do2Dtos(List<MessageDO> list);

    MessageDTO vo2Dto(MessageVO obj);

    List<MessageDTO> vo2Dtos(List<MessageVO> list);

    MessageVO po2Vo(MessagePO obj);

    List<MessageVO> po2Vos(List<MessagePO> list);

    MessagePO dto2Po(MessageDTO obj);

    List<MessagePO> dto2Pos(List<MessageDTO> obj);

    MessageVO do2Vo(MessageDO obj);

    MessagePO do2Po(MessageDO obj);

    MessageDO po2Do(MessagePO obj);

}
