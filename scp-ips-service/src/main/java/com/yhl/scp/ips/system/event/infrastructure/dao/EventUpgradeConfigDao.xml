<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.system.event.infrastructure.dao.EventUpgradeConfigDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.system.event.infrastructure.po.EventUpgradeConfigPO">
        <!--@Table sys_event_upgrade_config-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="event_id" jdbcType="VARCHAR" property="eventId"/>
        <result column="current_level" jdbcType="INTEGER" property="currentLevel"/>
        <result column="current_level_name" jdbcType="VARCHAR" property="currentLevelName"/>
        <result column="target_level" jdbcType="INTEGER" property="targetLevel"/>
        <result column="target_level_name" jdbcType="VARCHAR" property="targetLevelName"/>
        <result column="timeout_minutes" jdbcType="INTEGER" property="timeoutMinutes"/>
        <result column="retry_interval_minutes" jdbcType="INTEGER" property="retryIntervalMinutes"/>
        <result column="message_channel" jdbcType="VARCHAR" property="messageChannel"/>
        <result column="message_subject" jdbcType="VARCHAR" property="messageSubject"/>
        <result column="sender" jdbcType="VARCHAR" property="sender"/>
        <result column="receiver_type" jdbcType="VARCHAR" property="receiverType"/>
        <result column="receivers" jdbcType="VARCHAR" property="receivers"/>
        <result column="cc_receivers" jdbcType="VARCHAR" property="ccReceivers"/>
        <result column="message_template_id" jdbcType="VARCHAR" property="messageTemplateId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.system.event.vo.EventUpgradeConfigVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,event_id,current_level,current_level_name,target_level,target_level_name,timeout_minutes,retry_interval_minutes,message_channel,message_subject,sender,receiver_type,receivers,cc_receivers,message_template_id,remark,version_value,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.eventId != null and params.eventId != ''">
                and event_id = #{params.eventId,jdbcType=VARCHAR}
            </if>
            <if test="params.currentLevel != null">
                and current_level = #{params.currentLevel,jdbcType=INTEGER}
            </if>
            <if test="params.currentLevelName != null and params.currentLevelName != ''">
                and current_level_name = #{params.currentLevelName,jdbcType=VARCHAR}
            </if>
            <if test="params.targetLevel != null">
                and target_level = #{params.targetLevel,jdbcType=INTEGER}
            </if>
            <if test="params.targetLevelName != null and params.targetLevelName != ''">
                and target_level_name = #{params.targetLevelName,jdbcType=VARCHAR}
            </if>
            <if test="params.timeoutMinutes != null">
                and timeout_minutes = #{params.timeoutMinutes,jdbcType=INTEGER}
            </if>
            <if test="params.retryIntervalMinutes != null">
                and retry_interval_minutes = #{params.retryIntervalMinutes,jdbcType=INTEGER}
            </if>
            <if test="params.messageChannel != null and params.messageChannel != ''">
                and message_channel = #{params.messageChannel,jdbcType=VARCHAR}
            </if>
            <if test="params.messageSubject != null and params.messageSubject != ''">
                and message_subject = #{params.messageSubject,jdbcType=VARCHAR}
            </if>
            <if test="params.sender != null and params.sender != ''">
                and sender = #{params.sender,jdbcType=VARCHAR}
            </if>
            <if test="params.receiverType != null and params.receiverType != ''">
                and receiver_type = #{params.receiverType,jdbcType=VARCHAR}
            </if>
            <if test="params.receivers != null and params.receivers != ''">
                and receivers = #{params.receivers,jdbcType=VARCHAR}
            </if>
            <if test="params.ccReceivers != null and params.ccReceivers != ''">
                and cc_receivers = #{params.ccReceivers,jdbcType=VARCHAR}
            </if>
            <if test="params.messageTemplateId != null and params.messageTemplateId != ''">
                and message_template_id = #{params.messageTemplateId,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_upgrade_config
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_upgrade_config
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_event_upgrade_config
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_upgrade_config
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_event_upgrade_config
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventUpgradeConfigPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sys_event_upgrade_config(
        id,
        event_id,
        current_level,
        current_level_name,
        target_level,
        target_level_name,
        timeout_minutes,
        retry_interval_minutes,
        message_channel,
        message_subject,
        sender,
        receiver_type,
        receivers,
        cc_receivers,
        message_template_id,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{eventId,jdbcType=VARCHAR},
        #{currentLevel,jdbcType=INTEGER},
        #{currentLevelName,jdbcType=VARCHAR},
        #{targetLevel,jdbcType=INTEGER},
        #{targetLevelName,jdbcType=VARCHAR},
        #{timeoutMinutes,jdbcType=INTEGER},
        #{retryIntervalMinutes,jdbcType=INTEGER},
        #{messageChannel,jdbcType=VARCHAR},
        #{messageSubject,jdbcType=VARCHAR},
        #{sender,jdbcType=VARCHAR},
        #{receiverType,jdbcType=VARCHAR},
        #{receivers,jdbcType=VARCHAR},
        #{ccReceivers,jdbcType=VARCHAR},
        #{messageTemplateId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventUpgradeConfigPO">
        insert into sys_event_upgrade_config(
        id,
        event_id,
        current_level,
        current_level_name,
        target_level,
        target_level_name,
        timeout_minutes,
        retry_interval_minutes,
        message_channel,
        message_subject,
        sender,
        receiver_type,
        receivers,
        cc_receivers,
        message_template_id,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{eventId,jdbcType=VARCHAR},
        #{currentLevel,jdbcType=INTEGER},
        #{currentLevelName,jdbcType=VARCHAR},
        #{targetLevel,jdbcType=INTEGER},
        #{targetLevelName,jdbcType=VARCHAR},
        #{timeoutMinutes,jdbcType=INTEGER},
        #{retryIntervalMinutes,jdbcType=INTEGER},
        #{messageChannel,jdbcType=VARCHAR},
        #{messageSubject,jdbcType=VARCHAR},
        #{sender,jdbcType=VARCHAR},
        #{receiverType,jdbcType=VARCHAR},
        #{receivers,jdbcType=VARCHAR},
        #{ccReceivers,jdbcType=VARCHAR},
        #{messageTemplateId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_event_upgrade_config(
        id,
        event_id,
        current_level,
        current_level_name,
        target_level,
        target_level_name,
        timeout_minutes,
        retry_interval_minutes,
        message_channel,
        message_subject,
        sender,
        receiver_type,
        receivers,
        cc_receivers,
        message_template_id,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.eventId,jdbcType=VARCHAR},
        #{entity.currentLevel,jdbcType=INTEGER},
        #{entity.currentLevelName,jdbcType=VARCHAR},
        #{entity.targetLevel,jdbcType=INTEGER},
        #{entity.targetLevelName,jdbcType=VARCHAR},
        #{entity.timeoutMinutes,jdbcType=INTEGER},
        #{entity.retryIntervalMinutes,jdbcType=INTEGER},
        #{entity.messageChannel,jdbcType=VARCHAR},
        #{entity.messageSubject,jdbcType=VARCHAR},
        #{entity.sender,jdbcType=VARCHAR},
        #{entity.receiverType,jdbcType=VARCHAR},
        #{entity.receivers,jdbcType=VARCHAR},
        #{entity.ccReceivers,jdbcType=VARCHAR},
        #{entity.messageTemplateId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sys_event_upgrade_config(
        id,
        event_id,
        current_level,
        current_level_name,
        target_level,
        target_level_name,
        timeout_minutes,
        retry_interval_minutes,
        message_channel,
        message_subject,
        sender,
        receiver_type,
        receivers,
        cc_receivers,
        message_template_id,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.eventId,jdbcType=VARCHAR},
        #{entity.currentLevel,jdbcType=INTEGER},
        #{entity.currentLevelName,jdbcType=VARCHAR},
        #{entity.targetLevel,jdbcType=INTEGER},
        #{entity.targetLevelName,jdbcType=VARCHAR},
        #{entity.timeoutMinutes,jdbcType=INTEGER},
        #{entity.retryIntervalMinutes,jdbcType=INTEGER},
        #{entity.messageChannel,jdbcType=VARCHAR},
        #{entity.messageSubject,jdbcType=VARCHAR},
        #{entity.sender,jdbcType=VARCHAR},
        #{entity.receiverType,jdbcType=VARCHAR},
        #{entity.receivers,jdbcType=VARCHAR},
        #{entity.ccReceivers,jdbcType=VARCHAR},
        #{entity.messageTemplateId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventUpgradeConfigPO">
        update sys_event_upgrade_config set
        event_id = #{eventId,jdbcType=VARCHAR},
        current_level = #{currentLevel,jdbcType=INTEGER},
        current_level_name = #{currentLevelName,jdbcType=VARCHAR},
        target_level = #{targetLevel,jdbcType=INTEGER},
        target_level_name = #{targetLevelName,jdbcType=VARCHAR},
        timeout_minutes = #{timeoutMinutes,jdbcType=INTEGER},
        retry_interval_minutes = #{retryIntervalMinutes,jdbcType=INTEGER},
        message_channel = #{messageChannel,jdbcType=VARCHAR},
        message_subject = #{messageSubject,jdbcType=VARCHAR},
        sender = #{sender,jdbcType=VARCHAR},
        receiver_type = #{receiverType,jdbcType=VARCHAR},
        receivers = #{receivers,jdbcType=VARCHAR},
        cc_receivers = #{ccReceivers,jdbcType=VARCHAR},
        message_template_id = #{messageTemplateId,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventUpgradeConfigPO">
        update sys_event_upgrade_config
        <set>
            <if test="item.eventId != null and item.eventId != ''">
                event_id = #{item.eventId,jdbcType=VARCHAR},
            </if>
            <if test="item.currentLevel != null">
                current_level = #{item.currentLevel,jdbcType=INTEGER},
            </if>
            <if test="item.currentLevelName != null and item.currentLevelName != ''">
                current_level_name = #{item.currentLevelName,jdbcType=VARCHAR},
            </if>
            <if test="item.targetLevel != null">
                target_level = #{item.targetLevel,jdbcType=INTEGER},
            </if>
            <if test="item.targetLevelName != null and item.targetLevelName != ''">
                target_level_name = #{item.targetLevelName,jdbcType=VARCHAR},
            </if>
            <if test="item.timeoutMinutes != null">
                timeout_minutes = #{item.timeoutMinutes,jdbcType=INTEGER},
            </if>
            <if test="item.retryIntervalMinutes != null">
                retry_interval_minutes = #{item.retryIntervalMinutes,jdbcType=INTEGER},
            </if>
            <if test="item.messageChannel != null and item.messageChannel != ''">
                message_channel = #{item.messageChannel,jdbcType=VARCHAR},
            </if>
            <if test="item.messageSubject != null and item.messageSubject != ''">
                message_subject = #{item.messageSubject,jdbcType=VARCHAR},
            </if>
            <if test="item.sender != null and item.sender != ''">
                sender = #{item.sender,jdbcType=VARCHAR},
            </if>
            <if test="item.receiverType != null and item.receiverType != ''">
                receiver_type = #{item.receiverType,jdbcType=VARCHAR},
            </if>
            <if test="item.receivers != null and item.receivers != ''">
                receivers = #{item.receivers,jdbcType=VARCHAR},
            </if>
            <if test="item.ccReceivers != null and item.ccReceivers != ''">
                cc_receivers = #{item.ccReceivers,jdbcType=VARCHAR},
            </if>
            <if test="item.messageTemplateId != null and item.messageTemplateId != ''">
                message_template_id = #{item.messageTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sys_event_upgrade_config
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="event_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="current_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currentLevel,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="current_level_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currentLevelName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="target_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.targetLevel,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="target_level_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.targetLevelName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="timeout_minutes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.timeoutMinutes,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="retry_interval_minutes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.retryIntervalMinutes,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="message_channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageChannel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_subject = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageSubject,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sender = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sender,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="receiver_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.receiverType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="receivers = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.receivers,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cc_receivers = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ccReceivers,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_template_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageTemplateId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sys_event_upgrade_config 
        <set>
            <if test="item.eventId != null and item.eventId != ''">
                event_id = #{item.eventId,jdbcType=VARCHAR},
            </if>
            <if test="item.currentLevel != null">
                current_level = #{item.currentLevel,jdbcType=INTEGER},
            </if>
            <if test="item.currentLevelName != null and item.currentLevelName != ''">
                current_level_name = #{item.currentLevelName,jdbcType=VARCHAR},
            </if>
            <if test="item.targetLevel != null">
                target_level = #{item.targetLevel,jdbcType=INTEGER},
            </if>
            <if test="item.targetLevelName != null and item.targetLevelName != ''">
                target_level_name = #{item.targetLevelName,jdbcType=VARCHAR},
            </if>
            <if test="item.timeoutMinutes != null">
                timeout_minutes = #{item.timeoutMinutes,jdbcType=INTEGER},
            </if>
            <if test="item.retryIntervalMinutes != null">
                retry_interval_minutes = #{item.retryIntervalMinutes,jdbcType=INTEGER},
            </if>
            <if test="item.messageChannel != null and item.messageChannel != ''">
                message_channel = #{item.messageChannel,jdbcType=VARCHAR},
            </if>
            <if test="item.messageSubject != null and item.messageSubject != ''">
                message_subject = #{item.messageSubject,jdbcType=VARCHAR},
            </if>
            <if test="item.sender != null and item.sender != ''">
                sender = #{item.sender,jdbcType=VARCHAR},
            </if>
            <if test="item.receiverType != null and item.receiverType != ''">
                receiver_type = #{item.receiverType,jdbcType=VARCHAR},
            </if>
            <if test="item.receivers != null and item.receivers != ''">
                receivers = #{item.receivers,jdbcType=VARCHAR},
            </if>
            <if test="item.ccReceivers != null and item.ccReceivers != ''">
                cc_receivers = #{item.ccReceivers,jdbcType=VARCHAR},
            </if>
            <if test="item.messageTemplateId != null and item.messageTemplateId != ''">
                message_template_id = #{item.messageTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sys_event_upgrade_config where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sys_event_upgrade_config where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
