package com.yhl.scp.ips.system.event.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.system.event.dto.EventLogDTO;
import com.yhl.scp.ips.system.event.service.EventLogService;
import com.yhl.scp.ips.system.event.vo.EventLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>EventLogController</code>
 * <p>
 * 事件日志表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Slf4j
@Api(tags = "事件日志表控制器")
@RestController
@RequestMapping("eventLog")
public class EventLogController extends BaseController {

    @Resource
    private EventLogService eventLogService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<EventLogVO>> page() {
        List<EventLogVO> eventLogList = eventLogService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<EventLogVO> pageInfo = new PageInfo<>(eventLogList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody EventLogDTO eventLogDTO) {
        return eventLogService.doCreate(eventLogDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody EventLogDTO eventLogDTO) {
        return eventLogService.doUpdate(eventLogDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        eventLogService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<EventLogVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, eventLogService.selectByPrimaryKey(id));
    }

}
