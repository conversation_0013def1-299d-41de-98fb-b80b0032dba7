package com.yhl.scp.ips.system.event.domain.service;

import com.yhl.scp.ips.system.event.domain.entity.EventDefinitionDO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventDefinitionDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>EventDefinitionDomainService</code>
 * <p>
 * 事件定义表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
@Service
public class EventDefinitionDomainService {

    @Resource
    private EventDefinitionDao eventDefinitionDao;

    /**
     * 数据校验
     *
     * @param eventDefinitionDO 领域对象
     */
    public void validation(EventDefinitionDO eventDefinitionDO) {
        checkNotNull(eventDefinitionDO);
        checkUniqueCode(eventDefinitionDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param eventDefinitionDO 领域对象
     */
    private void checkNotNull(EventDefinitionDO eventDefinitionDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param eventDefinitionDO 领域对象
     */
    private void checkUniqueCode(EventDefinitionDO eventDefinitionDO) {
        // TODO
    }

}
