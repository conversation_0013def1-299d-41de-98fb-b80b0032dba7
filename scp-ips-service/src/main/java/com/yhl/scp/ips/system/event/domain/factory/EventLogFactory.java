package com.yhl.scp.ips.system.event.domain.factory;

import com.yhl.scp.ips.system.event.domain.entity.EventLogDO;
import com.yhl.scp.ips.system.event.dto.EventLogDTO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventLogDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>EventLogFactory</code>
 * <p>
 * 事件日志表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Component
public class EventLogFactory {

    @Resource
    private EventLogDao eventLogDao;

    EventLogDO create(EventLogDTO dto) {
        // TODO
        return null;
    }

}
