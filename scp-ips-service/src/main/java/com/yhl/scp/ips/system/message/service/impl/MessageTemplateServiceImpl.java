package com.yhl.scp.ips.system.message.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.ips.system.message.convertor.MessageTemplateConvertor;
import com.yhl.scp.ips.system.message.domain.entity.MessageTemplateDO;
import com.yhl.scp.ips.system.message.domain.service.MessageTemplateDomainService;
import com.yhl.scp.ips.system.message.dto.MessageTemplateDTO;
import com.yhl.scp.ips.system.message.infrastructure.dao.MessageTemplateDao;
import com.yhl.scp.ips.system.message.infrastructure.po.MessageTemplatePO;
import com.yhl.scp.ips.system.message.service.MessageTemplateService;
import com.yhl.scp.ips.system.message.vo.MessageTemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MessageTemplateServiceImpl</code>
 * <p>
 * 消息模板表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:03
 */
@Slf4j
@Service
public class MessageTemplateServiceImpl extends AbstractService implements MessageTemplateService {

    @Resource
    private MessageTemplateDao messageTemplateDao;

    @Resource
    private MessageTemplateDomainService messageTemplateDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(MessageTemplateDTO messageTemplateDTO) {
        // 0.数据转换
        MessageTemplateDO messageTemplateDO = MessageTemplateConvertor.INSTANCE.dto2Do(messageTemplateDTO);
        MessageTemplatePO messageTemplatePO = MessageTemplateConvertor.INSTANCE.dto2Po(messageTemplateDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        messageTemplateDomainService.validation(messageTemplateDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(messageTemplatePO);
        messageTemplateDao.insertWithPrimaryKey(messageTemplatePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MessageTemplateDTO messageTemplateDTO) {
        // 0.数据转换
        MessageTemplateDO messageTemplateDO = MessageTemplateConvertor.INSTANCE.dto2Do(messageTemplateDTO);
        MessageTemplatePO messageTemplatePO = MessageTemplateConvertor.INSTANCE.dto2Po(messageTemplateDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        messageTemplateDomainService.validation(messageTemplateDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(messageTemplatePO);
        messageTemplateDao.update(messageTemplatePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MessageTemplateDTO> list) {
        List<MessageTemplatePO> newList = MessageTemplateConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        messageTemplateDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MessageTemplateDTO> list) {
        List<MessageTemplatePO> newList = MessageTemplateConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        messageTemplateDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return messageTemplateDao.deleteBatch(idList);
        }
        return messageTemplateDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MessageTemplateVO selectByPrimaryKey(String id) {
        MessageTemplatePO po = messageTemplateDao.selectByPrimaryKey(id);
        return MessageTemplateConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sys_message_template")
    public List<MessageTemplateVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sys_message_template")
    public List<MessageTemplateVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MessageTemplateVO> dataList = messageTemplateDao.selectByCondition(sortParam, queryCriteriaParam);
        MessageTemplateServiceImpl target = springBeanUtils.getBean(MessageTemplateServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MessageTemplateVO> selectByParams(Map<String, Object> params) {
        List<MessageTemplatePO> list = messageTemplateDao.selectByParams(params);
        return MessageTemplateConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MessageTemplateVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MESSAGE_TEMPLATE.getCode();
    }

    @Override
    public List<MessageTemplateVO> invocation(List<MessageTemplateVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
