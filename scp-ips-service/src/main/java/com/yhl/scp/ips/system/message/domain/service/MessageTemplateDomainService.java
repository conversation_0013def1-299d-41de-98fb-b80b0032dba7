package com.yhl.scp.ips.system.message.domain.service;

import com.yhl.scp.ips.system.message.domain.entity.MessageTemplateDO;
import com.yhl.scp.ips.system.message.infrastructure.dao.MessageTemplateDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>MessageTemplateDomainService</code>
 * <p>
 * 消息模板表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:03
 */
@Service
public class MessageTemplateDomainService {

    @Resource
    private MessageTemplateDao messageTemplateDao;

    /**
     * 数据校验
     *
     * @param messageTemplateDO 领域对象
     */
    public void validation(MessageTemplateDO messageTemplateDO) {
        checkNotNull(messageTemplateDO);
        checkUniqueCode(messageTemplateDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param messageTemplateDO 领域对象
     */
    private void checkNotNull(MessageTemplateDO messageTemplateDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param messageTemplateDO 领域对象
     */
    private void checkUniqueCode(MessageTemplateDO messageTemplateDO) {
        // TODO
    }

}
