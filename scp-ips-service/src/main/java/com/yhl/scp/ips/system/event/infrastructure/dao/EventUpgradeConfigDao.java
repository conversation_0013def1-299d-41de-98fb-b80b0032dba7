package com.yhl.scp.ips.system.event.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.system.event.infrastructure.po.EventUpgradeConfigPO;
import com.yhl.scp.ips.system.event.vo.EventUpgradeConfigVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>EventUpgradeConfigDao</code>
 * <p>
 * 事件升级配置表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
public interface EventUpgradeConfigDao extends BaseDao<EventUpgradeConfigPO, EventUpgradeConfigVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link EventUpgradeConfigVO}
     */
    List<EventUpgradeConfigVO> selectVOByParams(@Param("params") Map<String, Object> params);

}
