package com.yhl.scp.ips.system.message.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.system.message.infrastructure.po.MessageTemplatePO;
import com.yhl.scp.ips.system.message.vo.MessageTemplateVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>MessageTemplateDao</code>
 * <p>
 * 消息模板表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:03
 */
public interface MessageTemplateDao extends BaseDao<MessageTemplatePO, MessageTemplateVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link MessageTemplateVO}
     */
    List<MessageTemplateVO> selectVOByParams(@Param("params") Map<String, Object> params);

}
