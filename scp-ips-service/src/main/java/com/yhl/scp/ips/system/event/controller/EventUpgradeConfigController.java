package com.yhl.scp.ips.system.event.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.system.event.dto.EventUpgradeConfigDTO;
import com.yhl.scp.ips.system.event.service.EventUpgradeConfigService;
import com.yhl.scp.ips.system.event.vo.EventUpgradeConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>EventUpgradeConfigController</code>
 * <p>
 * 事件升级配置表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Slf4j
@Api(tags = "事件升级配置表控制器")
@RestController
@RequestMapping("eventUpgradeConfig")
public class EventUpgradeConfigController extends BaseController {

    @Resource
    private EventUpgradeConfigService eventUpgradeConfigService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<EventUpgradeConfigVO>> page() {
        List<EventUpgradeConfigVO> eventUpgradeConfigList = eventUpgradeConfigService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<EventUpgradeConfigVO> pageInfo = new PageInfo<>(eventUpgradeConfigList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody EventUpgradeConfigDTO eventUpgradeConfigDTO) {
        return eventUpgradeConfigService.doCreate(eventUpgradeConfigDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody EventUpgradeConfigDTO eventUpgradeConfigDTO) {
        return eventUpgradeConfigService.doUpdate(eventUpgradeConfigDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        eventUpgradeConfigService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<EventUpgradeConfigVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, eventUpgradeConfigService.selectByPrimaryKey(id));
    }

}
