package com.yhl.scp.ips.system.event.domain.service;

import com.yhl.scp.ips.system.event.domain.entity.EventConfigDO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventConfigDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>EventConfigDomainService</code>
 * <p>
 * 事件配置表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
@Service
public class EventConfigDomainService {

    @Resource
    private EventConfigDao eventConfigDao;

    /**
     * 数据校验
     *
     * @param eventConfigDO 领域对象
     */
    public void validation(EventConfigDO eventConfigDO) {
        checkNotNull(eventConfigDO);
        checkUniqueCode(eventConfigDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param eventConfigDO 领域对象
     */
    private void checkNotNull(EventConfigDO eventConfigDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param eventConfigDO 领域对象
     */
    private void checkUniqueCode(EventConfigDO eventConfigDO) {
        // TODO
    }

}
