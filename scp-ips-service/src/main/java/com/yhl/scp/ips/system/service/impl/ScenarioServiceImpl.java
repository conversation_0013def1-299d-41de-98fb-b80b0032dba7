package com.yhl.scp.ips.system.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.*;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.discovery.NotificationService;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dps.extension.forecast.vo.DemandForecastVersionVO;
import com.yhl.scp.dps.feign.DpsFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.log.infrastructure.dao.BusinessMonitorLogDao;
import com.yhl.scp.ips.log.infrastructure.po.BusinessMonitorLogPO;
import com.yhl.scp.ips.rbac.dao.UserScenarioDao;
import com.yhl.scp.ips.rbac.entity.Tenant;
import com.yhl.scp.ips.rbac.service.TenantService;
import com.yhl.scp.ips.rbac.service.UserService;
import com.yhl.scp.ips.rbac.vo.UserScenarioVO;
import com.yhl.scp.ips.system.Param;
import com.yhl.scp.ips.system.ScenarioParam;
import com.yhl.scp.ips.system.dao.ScenarioDao;
import com.yhl.scp.ips.system.dto.CompleteScenarioConfigDTO;
import com.yhl.scp.ips.system.dto.ScenarioDTO;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.entity.ScenarioConfigListVO;
import com.yhl.scp.ips.system.enums.ScenarioConfigEnum;
import com.yhl.scp.ips.system.service.ScenarioService;
import com.yhl.scp.ips.system.vo.ScenarioVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.time.vo.TimePeriodGroupVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.sop.extension.scenario.dto.ScenarioConfigDTO;
import com.yhl.scp.sop.extension.scenario.vo.ScenarioConfigVO;
import com.yhl.scp.sop.feign.SopFeign;
import liquibase.repackaged.org.apache.commons.collections4.CollectionUtils;
import liquibase.repackaged.org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>ScenarioServiceImpl</code>
 * <p>
 * 场景业务接口实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-23 10:45:20
 */
@Slf4j
@Service
public class ScenarioServiceImpl implements ScenarioService {

    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    @Value("${spring.datasource.driverClassName}")
    private String driver;

    @Value("${yhl.datasource.uri}")
    private String uri;

    @Value("${yhl.datasource.params}")
    private String databaseParams;

    @Resource
    private ScenarioDao scenarioDao;

    @Resource
    private SopFeign sopFeign;

    @Resource
    private DpsFeign dpsFeign;

    @Resource
    private MdsFeign mdsFeign;

    @Resource
    private NotificationService notificationService;

    @Resource
    private TenantService tenantService;

    @Resource
    private UserService userService;

    @Resource
    private UserScenarioDao userScenarioDao;

    @Resource
    private BusinessMonitorLogDao businessMonitorLogDao;

    public static final String UNION_SYSTEM_MODULE = "UNION";

    public static final String tableNames = "mds_rul_rule_encodings,mds_par_production_scheduling_param,mps_param_capacity_balance,mds_tim_time_period_group,mds_tim_time_period,mds_tim_planning_horizon";

    @Override
    public Scenario doCreate(ScenarioDTO scenarioDTO) throws SQLException {
        paramsCheck(scenarioDTO);
        String scenarioName = scenarioDTO.getScenarioName();
        String sourceScenarioId = scenarioDTO.getSourceScenarioId();
        Scenario sourceScenario = scenarioDao.selectByPrimaryKey(sourceScenarioId);
        if (sourceScenario == null) {
            throw new BusinessException("源场景不存在");
        }
        // 如果scenarioName为空，则默认覆盖源场景
        Scenario targetScenario = scenarioName == null ? sourceScenario : scenarioDao.selectByName(scenarioName,
                SystemHolder.getTenantId(), sourceScenario.getModuleCode());
        String timePeriodGroupId = null;

        // 新建场景
        if (null == targetScenario) {
            targetScenario = createScenario(sourceScenario.getModuleCode(), SystemHolder.getTenantId(),
                    scenarioDTO, YesOrNoEnum.NO.getCode(), timePeriodGroupId);
            userService.doInsertUserScenario(SystemHolder.getUserId(), targetScenario.getId());
        } else {
            // 覆盖场景
            targetScenario.setSourceScenarioId(sourceScenarioId);
            targetScenario.setDemandForecastVersionId(scenarioDTO.getDemandForecastVersionId());
            targetScenario.setTimePeriodGroupId(timePeriodGroupId);
            targetScenario.setModifier(SystemHolder.getUserId());
            targetScenario.setModifyTime(new Date());
            scenarioDao.updateByPrimaryKey(targetScenario);
            doTablesCopy(sourceScenario.getDataBaseName(), targetScenario.getDataBaseName(), tableNames);
            doViewsCopy(sourceScenario.getDataBaseName(), targetScenario.getDataBaseName());
        }
        notificationService.dataSourceRefresh();
        return targetScenario;
    }

    private void paramsCheck(ScenarioDTO scenarioDTO) {
        if (StringUtils.isEmpty(scenarioDTO.getScenarioName())) {
            throw new BusinessException("场景名称不能为空");
        }
        if (StringUtils.isEmpty(scenarioDTO.getSourceScenarioId())) {
            throw new BusinessException("源场景不能为空");
        }

    }

    @Override
    public void doCreate(Scenario scenario) {
        scenarioDao.insert(scenario);
    }

    @Override
    public BaseResponse<Void> doUpdate(Scenario scenario) {
        scenarioDao.updateByPrimaryKey(scenario);
        return new BaseResponse<>(true, "修改成功");
    }

    @Override
    public void doUpdate(String id, String scenarioName) {
        Scenario old = scenarioDao.selectByPrimaryKey(id);
        if (!scenarioName.equals(old.getScenarioName())) {
            Map<String, Object> params = new HashMap<>(4);
            params.put("scenarioName", scenarioName);
            List<Scenario> list = scenarioDao.selectByParams(params);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
                throw new BusinessException("修改失败，场景名重复：" + scenarioName);
            }
        }
        old.setScenarioName(scenarioName);
        old.setModifier(SystemHolder.getUserId());
        old.setModifyTime(new Date());
        scenarioDao.updateByPrimaryKey(old);
    }

    @Override
    @BusinessMonitorLog(businessCode = "场景删除", moduleCode = "IPS", businessFrequency = "MONTH")
    public BaseResponse<Void> doDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return BaseResponse.success("删除成功");
        }
        Date start = new Date();
        List<Scenario> scenarioList = scenarioDao.selectByParams(ImmutableMap.of("ids", ids));
        List<Scenario> masterScenarios = scenarioList.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getMaster()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(masterScenarios)) {
            return BaseResponse.error("主场景不能删除");
        }
        Map<String, String> map = scenarioList.stream()
                .collect(Collectors.toMap(Scenario::getId, Scenario::getDataBaseName, (t1, t2) -> t2));
        for (String id : ids) {
            scenarioDao.deleteByPrimaryKey(id);
            // 删库
            String databaseName = map.get(id);
            DatabaseUtils.dropDatabase(url, username, password, driver, databaseName);
        }
        userScenarioDao.deleteByScenarioIds(ids);
        notificationService.dataSourceRefresh();

        saveLog(start);
        return BaseResponse.success("删除成功");
    }

    /**
     * 记录操作日志
     * @param start
     */
    private void saveLog(Date start) {
        // 记录业务监控
        Date end = new Date();
        BusinessMonitorLogPO businessMonitorLogPO = new BusinessMonitorLogPO();
        String scenario = SystemHolder.getScenario();
        if (StringUtils.isEmpty(scenario)) {
            scenario = DynamicDataSourceContextHolder.getDataSource();
        }
        String userId = SystemHolder.getUserId();
        businessMonitorLogPO.setId(UUIDUtil.getUUID());
        businessMonitorLogPO.setScenario(scenario);
        businessMonitorLogPO.setBusinessFrequency("DAY");
        businessMonitorLogPO.setBusinessCode("场景删除");
        businessMonitorLogPO.setOperationMethod("MANUAL");
        businessMonitorLogPO.setOperationStatus("SUCCESS");
        businessMonitorLogPO.setModuleCode("IPS");
        businessMonitorLogPO.setOperator(userId);
        businessMonitorLogPO.setStartTime(start);
        businessMonitorLogPO.setEndTime(end);
        BasePOUtils.insertFiller(businessMonitorLogPO);
        businessMonitorLogDao.insert(businessMonitorLogPO);
    }

    @Override
    public List<Scenario> selectAll() {
        return scenarioDao.selectAll();
    }

    @Override
    public BaseResponse<Void> doSave(ScenarioParam scenarioParam) {
        log.info("ScenarioServiceImpl = {}", JacksonUtils.toJson(scenarioParam));
        List<Scenario> scenarios = scenarioDao.selectByTenantId(scenarioParam.getTenantId());
        scenarios =
                scenarios.stream().filter(item -> YesOrNoEnum.YES.getCode().equals(item.getMaster())).collect(Collectors.toList());
        Map<String, Scenario> scenarioMap = scenarios.stream().collect(Collectors.toMap(Scenario::getModuleCode,
                v -> v));
        List<Param> values = scenarioParam.getParam();
        if (CollectionUtils.isEmpty(values)) {
            return new BaseResponse<>(false, "创建失败");
        }

        for (Param param : values) {
            Scenario master = scenarioMap.get(param.getModuleCode());
            if (null != master) {
                log.info("ScenarioServiceImpl 2");
                // 覆盖场景
                master.setSourceScenarioId(param.getSourceScenarioId());
                scenarioDao.updateByPrimaryKey(master);
                // TODO 克隆
            } else {
                log.info("ScenarioServiceImpl 3");
                ScenarioDTO scenarioDTO = new ScenarioDTO();
                scenarioDTO.setSourceScenarioId(param.getSourceScenarioId());
                scenarioDTO.setScenarioName("主版本");
                createScenario(param.getModuleCode(), scenarioParam.getTenantId(), scenarioDTO,
                        YesOrNoEnum.YES.getCode(), scenarioDTO.getTimePeriodGroupId());
            }
        }
        notificationService.dataSourceRefresh();
        return new BaseResponse<>(true, "场景创建中。");
    }

    /**
     * 创建场景
     *
     * @param moduleCode        模块
     * @param tenantId          租户ID
     * @param scenarioDTO       来源场景ID
     * @param master            是否主场景
     * @param timePeriodGroupId 时段序列ID
     */
    private Scenario createScenario(String moduleCode, String tenantId, ScenarioDTO scenarioDTO,
                                    String master, String timePeriodGroupId) {
        Scenario sourceScenario = selectById(scenarioDTO.getSourceScenarioId());
        Scenario scenario = new Scenario();
        scenario.setModuleCode(UNION_SYSTEM_MODULE);
        if (StringUtils.isNotEmpty(scenarioDTO.getTenantId())) {
            tenantId = scenarioDTO.getTenantId();
        }
        // database以scp_开头，场景名称转拼音
        String database =
                Constants.SCENARIO_PREFIX + convertToPinyin(scenarioDTO.getScenarioName()).toLowerCase();
        scenario.setId(UUIDUtil.getUUID());
        scenario.setDemandForecastVersionId(scenarioDTO.getDemandForecastVersionId());
        scenario.setTimePeriodGroupId(timePeriodGroupId);
        scenario.setDataBaseName(database);
        scenario.setScenarioName(scenarioDTO.getScenarioName());
        scenario.setTenantId(tenantId);
        scenario.setEnabled(YesOrNoEnum.YES.getCode());
        scenario.setSourceScenarioId(scenarioDTO.getSourceScenarioId());
        scenario.setUrl(uri + database + databaseParams);
        scenario.setMaster(master);
        scenario.setCreator(SystemHolder.getUserId());
        scenario.setCreateTime(new Date());
        scenario.setModifier(SystemHolder.getUserId());
        scenario.setModifyTime(new Date());
        scenarioDao.insert(scenario);
        DatabaseUtils.createDatabase(url, username, password, driver, database);
        log.info("ScenarioServiceImpl 4");
        doTablesCopy(sourceScenario.getDataBaseName(), database, tableNames);
        doViewsCopy(sourceScenario.getDataBaseName(), database);
        log.info("ScenarioServiceImpl 5");
        return scenario;
    }

    @Override
    public List<Scenario> selectByModuleCode(String moduleCode) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("moduleCode", moduleCode);
        params.put("tenantId", SystemHolder.getTenantId());
        return scenarioDao.selectByParams(params);
    }

    @Override
    public List<Scenario> selectPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return scenarioDao.selectByPage(sortParam, queryCriteriaParam, SystemHolder.getTenantId());
    }

    @Override
    public List<ScenarioVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        String tenantId = SystemHolder.getTenantId();
        String moduleCode = SystemHolder.getModuleCode();
        List<Scenario> scenarios = scenarioDao.selectByPage(sortParam, queryCriteriaParam, tenantId);
        Map<String, String> scenarioMap = scenarios.stream().collect(Collectors
                .toMap(Scenario::getId, Scenario::getScenarioName, (t1, t2) -> t2));
        List<ScenarioVO> result = new ArrayList<>();
        Tenant tenant = tenantService.getById(tenantId);
        scenarios.forEach(item -> {
            String dataBaseName = item.getDataBaseName();
            ScenarioVO target = new ScenarioVO();
            BeanUtils.copyProperties(item, target);
            target.setTenantName(tenant.getTenantName());
            target.setSourceScenarioName(scenarioMap.get(item.getSourceScenarioId()));
            if (SystemModuleEnum.SOP.getCode().equals(moduleCode)) {
                DemandForecastVersionVO demandVersion = dpsFeign.getDemandVersion(dataBaseName,
                        item.getDemandForecastVersionId());
                target.setDemandForecastVersionName(demandVersion.getVersionName());
                TimePeriodGroupVO timePeriodGroup = mdsFeign.getTimePeriodGroup(dataBaseName,
                        item.getTimePeriodGroupId());
                if (null != timePeriodGroup) {
                    target.setTimePeriodGroup(timePeriodGroup.getTimePeriodGroup());
                }
            }
            result.add(target);
        });
        return result;
    }

    @Override
    public List<Scenario> selectByParams(Map<String, Object> params) {
        return scenarioDao.selectByParams(params);
    }

    @Override
    public Scenario selectById(String id) {
        return scenarioDao.selectByPrimaryKey(id);
    }

    @Override
    public Scenario selectByName(String scenarioName, String tenantId, String moduleCode) {
        return scenarioDao.selectByName(scenarioName, tenantId, moduleCode);
    }

    @Override
    @BusinessMonitorLog(businessCode = "场景删除", moduleCode = "IPS", businessFrequency = "MONTH")
    public BaseResponse<Void> doDelete(String id) {
        Date start = new Date();
        Scenario scenario = scenarioDao.selectByPrimaryKey(id);
        if (YesOrNoEnum.YES.getCode().equals(scenario.getMaster())) {
            return new BaseResponse<>(false, "主场景不能删除");
        }
        userScenarioDao.deleteByScenarioId(id);
        scenarioDao.deleteByPrimaryKey(id);
        DatabaseUtils.dropDatabase(url, username, password, driver, scenario.getDataBaseName());
        notificationService.dataSourceRefresh();
        saveLog(start);
        return new BaseResponse<>(true, "删除成功");
    }

    /**
     * 全库(表)拷贝
     */
    @Override
    public void doTablesCopy(String sourceDatabaseName, String targetDatabaseName, String tableNames) {
        scenarioDao.tablesCopy(sourceDatabaseName, targetDatabaseName, tableNames);
    }

    /**
     * 视图拷贝
     */
    @Override
    public void doViewsCopy(String sourceDatabaseName, String targetDatabaseName) {
        scenarioDao.viewsCopy(sourceDatabaseName, targetDatabaseName);
    }

    @Override
    public void doSopDataCopy(String targetDatabaseName, String demandForecastVersionId, String timePeriodGroupId) {
        Scenario sourceScenario = selectMasterScenario(SystemModuleEnum.DPS.getCode());
        if (sourceScenario != null) {
            scenarioDao.sopDataCopy(sourceScenario.getDataBaseName(), targetDatabaseName, timePeriodGroupId,
                    demandForecastVersionId);
        }
    }

    @Override
    public List<ScenarioConfigListVO> pageWithScenarioConfig(Pagination pagination, String queryCriteriaParam) {
        log.info("SystemHolder.getModuleCode():" + SystemHolder.getModuleCode()
                + ";SystemHolder.getScenario():" + SystemHolder.getScenario());
        if (StringUtils.isEmpty(SystemHolder.getModuleCode())) {
            throw new BusinessException("未找到模块");
        }
        if (StringUtils.isEmpty(SystemHolder.getScenario())) {
            throw new BusinessException("未找到场景");
        }
        // 查询当前场景参数数据
        List<Scenario> currentScenarios = selectByParams(ImmutableMap.of("moduleCode",
                Objects.requireNonNull(SystemHolder.getModuleCode()), "dataBaseName",
                Objects.requireNonNull(SystemHolder.getScenario())));
        Scenario currentScenario;
        if (CollectionUtils.isNotEmpty(currentScenarios)) {
            currentScenario = currentScenarios.get(0);
        } else {
            return new ArrayList<>();
        }
        List<ScenarioConfigListVO> result = new ArrayList<>();
        if (YesOrNoEnum.YES.getCode().equals(currentScenario.getMaster())) {
            // 如果是主场景，分页查询模块下所有场景参数
            List<Scenario> scenarios = this.selectPage(pagination, null, queryCriteriaParam);
            // 主场景在前
            scenarios =
                    scenarios.stream().sorted(Comparator.comparing(Scenario::getMaster).reversed()).collect(Collectors.toList());
            for (Scenario scenario : scenarios) {
                String sourceScenarioId = scenario.getSourceScenarioId();
                // 如果源场景为空，则跳过
                if (StringUtils.isEmpty(sourceScenarioId)) {
                    ScenarioConfigListVO scenarioConfigListVO = ScenarioConfigListVO.builder()
                            .scenarioId(scenario.getId())
                            .scenarioName(scenario.getScenarioName())
                            .build();
                    result.add(scenarioConfigListVO);
                    continue;
                }
                Scenario sourceScenario = this.selectById(sourceScenarioId);
                // 获取需求版本名
                DemandForecastVersionVO demandForecastVersionVO =
                        dpsFeign.getDemandVersion(sourceScenario.getDataBaseName(),
                                scenario.getDemandForecastVersionId());
                // 获取场景参数数据
                ScenarioConfigListVO currentScenarioConfigData =
                        sopFeign.getScenarioConfigListVO(scenario.getDataBaseName(),
                                scenario.getTimePeriodGroupId());
                // 查询当前源场景参数数据
                ScenarioConfigListVO scenarioConfigListVO = ScenarioConfigListVO.builder()
                        .scenarioId(scenario.getId())
                        .scenarioName(scenario.getScenarioName())
                        .sourceScenarioId(sourceScenario.getId())
                        .sourceScenarioName(sourceScenario.getScenarioName())
                        .demandVersion(demandForecastVersionVO.getVersionName())
                        .timePeriodRange(currentScenarioConfigData.getTimePeriodRange())
                        .productRange(currentScenarioConfigData.getProductRange())
                        .marketingOrganization(currentScenarioConfigData.getMarketingOrganization())
                        .timeLimit(currentScenarioConfigData.getTimeLimit())
                        .productionSupplyAndDemandCouple(currentScenarioConfigData.getProductionSupplyAndDemandCouple())
                        .onlyBottleneckResource(currentScenarioConfigData.getOnlyBottleneckResource())
                        .onlyBottleneckMaterial(currentScenarioConfigData.getOnlyBottleneckMaterial())
                        .disableConstraints(currentScenarioConfigData.getDisableConstraints())
                        .status("未运行")
                        .modifier(currentScenarioConfigData.getModifier())
                        .modifyTime(currentScenarioConfigData.getModifyTime())
                        .build();
                result.add(scenarioConfigListVO);
            }
        } else {
            String sourceScenarioId = currentScenario.getSourceScenarioId();
            Scenario sourceScenario = this.selectById(sourceScenarioId);
            // 获取需求版本名
            DemandForecastVersionVO demandForecastVersionVO =
                    dpsFeign.getDemandVersion(sourceScenario.getDataBaseName(),
                            currentScenario.getDemandForecastVersionId());
            // 获取场景参数数据
            ScenarioConfigListVO currentScenarioConfigData =
                    sopFeign.getScenarioConfigListVO(currentScenario.getDataBaseName(),
                            currentScenario.getTimePeriodGroupId());
            // 查询当前源场景参数数据
            ScenarioConfigListVO scenarioConfigListVO = ScenarioConfigListVO.builder()
                    .scenarioId(currentScenario.getId())
                    .scenarioName(currentScenario.getScenarioName())
                    .sourceScenarioId(sourceScenario.getId())
                    .sourceScenarioName(sourceScenario.getScenarioName())
                    .demandVersion(demandForecastVersionVO.getVersionName())
                    .timePeriodRange(currentScenarioConfigData.getTimePeriodRange())
                    .productRange(currentScenarioConfigData.getProductRange())
                    .marketingOrganization(currentScenarioConfigData.getMarketingOrganization())
                    .timeLimit(currentScenarioConfigData.getTimeLimit())
                    .productionSupplyAndDemandCouple(currentScenarioConfigData.getProductionSupplyAndDemandCouple())
                    .onlyBottleneckResource(currentScenarioConfigData.getOnlyBottleneckResource())
                    .onlyBottleneckMaterial(currentScenarioConfigData.getOnlyBottleneckMaterial())
                    .disableConstraints(currentScenarioConfigData.getDisableConstraints())
                    .status("未运行")
                    .modifier(currentScenarioConfigData.getModifier())
                    .modifyTime(currentScenarioConfigData.getModifyTime())
                    .build();
            result.add(scenarioConfigListVO);
        }
        return result;
    }

    @Override
    public CompleteScenarioConfigDTO getScenarioConfigDetail(String scenarioId) {
        Scenario scenario = this.selectById(scenarioId);
        if (YesOrNoEnum.YES.getCode().equals(scenario.getMaster())) {
            throw new BusinessException("主创建不得修改场景参数");
        }
        List<ScenarioConfigVO> scenarioConfigVOS = sopFeign.getScenarioConfig(scenario.getDataBaseName());
        String disableConstraintsJsonString = getScenarioData(ScenarioConfigEnum.DISABLE_CONSTRAINTS.getCode(),
                scenarioConfigVOS);
        LinkedHashMap<String, String> disableConstraints = JacksonUtils.toObj(disableConstraintsJsonString,
                new TypeReference<LinkedHashMap<String, String>>() {
                });
        return CompleteScenarioConfigDTO.builder()
                .sourceScenario(scenario.getSourceScenarioId())
                .targetScenario(scenario.getId())
                .demandVersion(scenario.getDemandForecastVersionId())
                .timePeriodRange(getScenarioData(ScenarioConfigEnum.TIME_PERIOD_RANGE.getCode(), scenarioConfigVOS))
                .productRange(getScenarioData(ScenarioConfigEnum.PRODUCT_RANGE.getCode(), scenarioConfigVOS))
                .marketingOrganization(getScenarioData(ScenarioConfigEnum.SALES_SEGMENT_RANGE.getCode(),
                        scenarioConfigVOS))
                .timeLimit(getScenarioData(ScenarioConfigEnum.TIME_LIMIT.getCode(), scenarioConfigVOS))
                .productionSupplyAndDemandCouple(getScenarioData(ScenarioConfigEnum.PRODUCTION_SUPPLY_AND_DEMAND_COUPLE.getCode(),
                        scenarioConfigVOS))
                .onlyBottleneckMaterial(getScenarioData(ScenarioConfigEnum.ONLY_BOTTLENECK_MATERIAL.getCode(),
                        scenarioConfigVOS))
                .onlyBottleneckResource(getScenarioData(ScenarioConfigEnum.ONLY_BOTTLENECK_RESOURCE.getCode(),
                        scenarioConfigVOS))
                .disableConstraints(disableConstraints)
                .build();
    }

    /**
     * 查询场景中的参数
     *
     * @param configType 参数类型
     * @param list       参数列表
     * @return java.lang.String
     */
    private String getScenarioData(String configType, List<ScenarioConfigVO> list) {
        List<ScenarioConfigVO> configs = list.stream().filter(k -> configType.equals(k.getConfigType()))
                .collect(Collectors.toList());
        String data = null;
        if (!CollectionUtils.isEmpty(configs)) {
            ScenarioConfigVO scenarioConfigVO = configs.get(0);
            data = scenarioConfigVO.getConfigData();
        }
        return data;
    }

    @Override
    public void doSaveCompleteScenario(CompleteScenarioConfigDTO completeScenarioConfigDTO) throws SQLException {
        // 确认目标场景是否存在，如果不存在则要进行新增处理,如果存在做覆盖处理
        ScenarioDTO scenarioDTO = new ScenarioDTO();
        scenarioDTO.setSourceScenarioId(completeScenarioConfigDTO.getSourceScenario());
        scenarioDTO.setScenarioName(completeScenarioConfigDTO.getTargetScenario());
        scenarioDTO.setDemandForecastVersionId(completeScenarioConfigDTO.getDemandVersion());
        scenarioDTO.setTimePeriodGroupId(completeScenarioConfigDTO.getTimePeriodGroupId());
        Scenario scenario = this.doCreate(scenarioDTO);
        // 处理参数问题
        this.doUpdateCompleteScenario(completeScenarioConfigDTO, scenario);

    }

    @Override
    public void doUpdateCompleteScenario(CompleteScenarioConfigDTO completeScenarioConfigDTO, Scenario scenario) {
        if (scenario == null) {
            // 如果传过来的scenario为空，则说明scenario已经存在，做修改操作
            String targetScenarioName = completeScenarioConfigDTO.getTargetScenario();
            scenario = scenarioDao.selectByName(targetScenarioName, SystemHolder.getTenantId(),
                    SystemHolder.getModuleCode());
        }
        // 场景其他参数
        List<ScenarioConfigDTO> configList = new ArrayList<>();
        //*****需求范围参数*****
        // 时段范围
        ScenarioConfigDTO timePeriodRangeScenarioConfig = getTimePeriodRangeScenarioConfig(completeScenarioConfigDTO,
                scenario.getId());
        if (timePeriodRangeScenarioConfig != null) {
            configList.add(timePeriodRangeScenarioConfig);
        }
        // 产品范围
        ScenarioConfigDTO productRangeScenarioConfig = getProductRangeScenarioConfig(completeScenarioConfigDTO,
                scenario.getId());
        if (productRangeScenarioConfig != null) {
            configList.add(productRangeScenarioConfig);
        }
        // 所属销售组织
        ScenarioConfigDTO marketingOrganizationScenarioConfig =
                getMarketingOrganizationScenarioConfig(completeScenarioConfigDTO, scenario.getId());
        if (marketingOrganizationScenarioConfig != null) {
            configList.add(marketingOrganizationScenarioConfig);
        }

        // TODO *****处理供应链模型快捷设置*****
        // 生产能力

        // 库存点能力

        // 运输能力

        //*****算法参数*****
        // 时间限制
        ScenarioConfigDTO timeLimitScenarioConfig = getTimeLimitScenarioConfig(completeScenarioConfigDTO,
                scenario.getId());
        if (timeLimitScenarioConfig != null) {
            configList.add(timeLimitScenarioConfig);
        }
        // 产供需求挂钩
        ScenarioConfigDTO productionSupplyAndDemandCoupleScenarioConfig =
                getProductionSupplyAndDemandCoupleScenarioConfig(completeScenarioConfigDTO, scenario.getId());
        if (productionSupplyAndDemandCoupleScenarioConfig != null) {
            configList.add(productionSupplyAndDemandCoupleScenarioConfig);
        }
        // 只考虑瓶颈物料
        ScenarioConfigDTO onlyBottleneckMaterialScenarioConfig =
                getOnlyBottleneckMaterialScenarioConfig(completeScenarioConfigDTO, scenario.getId());
        if (onlyBottleneckMaterialScenarioConfig != null) {
            configList.add(onlyBottleneckMaterialScenarioConfig);
        }
        // 只考虑瓶颈资源
        ScenarioConfigDTO onlyBottleneckResourceScenarioConfig =
                getOnlyBottleneckResourceScenarioConfig(completeScenarioConfigDTO, scenario.getId());
        if (onlyBottleneckResourceScenarioConfig != null) {
            configList.add(onlyBottleneckResourceScenarioConfig);
        }
        // 禁用约束
        ScenarioConfigDTO disableConstraintsScenarioConfig =
                getDisableConstraintsScenarioConfig(completeScenarioConfigDTO, scenario.getId());
        if (disableConstraintsScenarioConfig != null) {
            configList.add(disableConstraintsScenarioConfig);
        }
        // feign调用保存其他场景参数
        if (!CollectionUtils.isEmpty(configList)) {
            sopFeign.saveScenarioConfig(scenario.getDataBaseName(), configList);
        }
    }

    /**
     * 只考虑瓶颈资源
     *
     * @param completeScenarioConfigDTO 参数对象
     * @param scenarioId                场景ID
     * @return com.yhl.scp.sop.scenario.dto.ScenarioConfigDTO
     */
    private ScenarioConfigDTO getOnlyBottleneckResourceScenarioConfig(
            CompleteScenarioConfigDTO completeScenarioConfigDTO, String scenarioId) {
        if (completeScenarioConfigDTO.getOnlyBottleneckResource() == null) {
            return null;
        }
        return ScenarioConfigDTO.builder()
                .scenarioId(scenarioId)
                .configCategory(ScenarioConfigEnum.ALGORITHM_CONFIG.getCode())
                .configType(ScenarioConfigEnum.ONLY_BOTTLENECK_RESOURCE.getCode())
                .configData(completeScenarioConfigDTO.getOnlyBottleneckResource())
                .build();
    }

    /**
     * 只考虑瓶颈物料
     *
     * @param completeScenarioConfigDTO 参数对象
     * @param scenarioId                场景ID
     * @return com.yhl.scp.sop.scenario.dto.ScenarioConfigDTO
     */
    private ScenarioConfigDTO getOnlyBottleneckMaterialScenarioConfig(
            CompleteScenarioConfigDTO completeScenarioConfigDTO, String scenarioId) {
        if (completeScenarioConfigDTO.getOnlyBottleneckMaterial() == null) {
            return null;
        }
        return ScenarioConfigDTO.builder()
                .scenarioId(scenarioId)
                .configCategory(ScenarioConfigEnum.ALGORITHM_CONFIG.getCode())
                .configType(ScenarioConfigEnum.ONLY_BOTTLENECK_MATERIAL.getCode())
                .configData(completeScenarioConfigDTO.getOnlyBottleneckMaterial())
                .build();
    }

    /**
     * 禁用约束
     *
     * @param completeScenarioConfigDTO 参数对象
     * @param scenarioId                场景ID
     * @return com.yhl.scp.sop.scenario.dto.ScenarioConfigDTO
     */
    private ScenarioConfigDTO getDisableConstraintsScenarioConfig(
            CompleteScenarioConfigDTO completeScenarioConfigDTO, String scenarioId) {
        if (completeScenarioConfigDTO.getDisableConstraints() == null) {
            return null;
        }
        return ScenarioConfigDTO.builder()
                .scenarioId(scenarioId)
                .configCategory(ScenarioConfigEnum.ALGORITHM_CONFIG.getCode())
                .configType(ScenarioConfigEnum.DISABLE_CONSTRAINTS.getCode())
                .configData(JacksonUtils.toJson(completeScenarioConfigDTO.getDisableConstraints()))
                .build();
    }

    /**
     * 产供需求挂钩
     *
     * @param completeScenarioConfigDTO 参数对象
     * @param scenarioId                场景ID
     * @return com.yhl.scp.sop.scenario.dto.ScenarioConfigDTO
     */
    private ScenarioConfigDTO getProductionSupplyAndDemandCoupleScenarioConfig(
            CompleteScenarioConfigDTO completeScenarioConfigDTO, String scenarioId) {
        if (completeScenarioConfigDTO.getProductionSupplyAndDemandCouple() == null) {
            return null;
        }
        return ScenarioConfigDTO.builder()
                .scenarioId(scenarioId)
                .configCategory(ScenarioConfigEnum.ALGORITHM_CONFIG.getCode())
                .configType(ScenarioConfigEnum.PRODUCTION_SUPPLY_AND_DEMAND_COUPLE.getCode())
                .configData(completeScenarioConfigDTO.getProductionSupplyAndDemandCouple())
                .build();
    }

    /**
     * 时间限制
     *
     * @param completeScenarioConfigDTO 参数对象
     * @param scenarioId                场景ID
     * @return com.yhl.scp.sop.scenario.dto.ScenarioConfigDTO
     */
    private ScenarioConfigDTO getTimeLimitScenarioConfig(
            CompleteScenarioConfigDTO completeScenarioConfigDTO, String scenarioId) {
        if (completeScenarioConfigDTO.getTimeLimit() == null) {
            return null;
        }
        return ScenarioConfigDTO.builder()
                .scenarioId(scenarioId)
                .configCategory(ScenarioConfigEnum.ALGORITHM_CONFIG.getCode())
                .configType(ScenarioConfigEnum.TIME_LIMIT.getCode())
                .configData(completeScenarioConfigDTO.getTimeLimit())
                .build();
    }

    /**
     * 所属销售组织
     *
     * @param completeScenarioConfigDTO 参数对象
     * @param scenarioId                场景ID
     * @return com.yhl.scp.sop.scenario.dto.ScenarioConfigDTO
     */
    private ScenarioConfigDTO getMarketingOrganizationScenarioConfig(
            CompleteScenarioConfigDTO completeScenarioConfigDTO, String scenarioId) {
        if (completeScenarioConfigDTO.getMarketingOrganization() == null) {
            return null;
        }
        return ScenarioConfigDTO.builder()
                .scenarioId(scenarioId)
                .configCategory(ScenarioConfigEnum.REQUIREMENT_RANGE.getCode())
                .configType(ScenarioConfigEnum.SALES_SEGMENT_RANGE.getCode())
                .configData(completeScenarioConfigDTO.getMarketingOrganization())
                .build();
    }

    /**
     * 产品范围
     *
     * @param completeScenarioConfigDTO 参数对象
     * @param scenarioId                场景ID
     * @return com.yhl.scp.sop.scenario.dto.ScenarioConfigDTO
     */
    private ScenarioConfigDTO getProductRangeScenarioConfig(
            CompleteScenarioConfigDTO completeScenarioConfigDTO, String scenarioId) {
        if (completeScenarioConfigDTO.getProductRange() == null) {
            return null;
        }
        return ScenarioConfigDTO.builder()
                .scenarioId(scenarioId)
                .configCategory(ScenarioConfigEnum.REQUIREMENT_RANGE.getCode())
                .configType(ScenarioConfigEnum.PRODUCT_RANGE.getCode())
                .configData(completeScenarioConfigDTO.getProductRange())
                .build();
    }

    /**
     * 时段范围
     *
     * @param completeScenarioConfigDTO 前段传递过来的参数
     * @param scenarioId                场景ID
     */
    private ScenarioConfigDTO getTimePeriodRangeScenarioConfig(
            CompleteScenarioConfigDTO completeScenarioConfigDTO, String scenarioId) {
        if (completeScenarioConfigDTO.getTimePeriodRange() == null) {
            return null;
        }
        return ScenarioConfigDTO.builder()
                .scenarioId(scenarioId)
                .configCategory(ScenarioConfigEnum.REQUIREMENT_RANGE.getCode())
                .configType(ScenarioConfigEnum.TIME_PERIOD_RANGE.getCode())
                .configData(completeScenarioConfigDTO.getTimePeriodRange())
                .build();
    }

    @SuppressWarnings("unused")
    public String selectDefaultScenarioBackup(String tenantId, String moduleCode) {
        List<Scenario> scenarios = scenarioDao.selectByParams(ImmutableMap.of("tenantId", tenantId));
        List<Scenario> collect =
                scenarios.stream().filter(item -> (moduleCode.equals(item.getModuleCode())
                        || UNION_SYSTEM_MODULE.equals(item.getModuleCode()))
                        && StringUtils.isNotEmpty(item.getDataBaseName())
                        && StringUtils.isNotEmpty(item.getUrl())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return null;
        }
        String scenario = collect.get(0).getDataBaseName();
        List<Scenario> masterList = collect.stream().filter(item -> YesOrNoEnum.YES.getCode().equals(item.getMaster()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(masterList)) {
            scenario = masterList.get(0).getDataBaseName();
        }
        return scenario;
    }
    @Override
    public String selectDefaultScenario(String tenantId, String userId, String moduleCode) {
        if (StringUtils.isEmpty(userId)) {
            userId = SystemHolder.getUserId();
        }
        if (SystemHolder.BPIM_BOT.equals(userId) ) {
            return null;
        }
        List<UserScenarioVO> userScenarios = userService.selectScenarioByUser(tenantId, userId);
        if (CollectionUtils.isNotEmpty(userScenarios)) {
            List<UserScenarioVO> filterList = userScenarios.stream()
                    .filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getDataBaseName()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)){
                return filterList.get(0).getDataBaseName();
            }
        }

        // 设置默认场景
        List<Scenario> scenarios = scenarioDao.selectAll();
        return scenarios.get(0).getDataBaseName();
    }

    @Override
    public List<Scenario> selectByScenarios(List<String> scenarios) {
        return scenarioDao.selectByDataBaseName(scenarios);
    }

    @Override
    public Scenario selectMasterScenario(String moduleCode) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("tenantId", SystemHolder.getTenantId());
        params.put("moduleCode", moduleCode);
        params.put("master", YesOrNoEnum.YES.getCode());
        List<Scenario> scenarios = scenarioDao.selectByParams(params);
        if (CollectionUtils.isEmpty(scenarios)) {
            return null;
        }
        return scenarios.get(0);
    }

    public static String convertToPinyin(String chinese) {
        // 将中文字符串转换为拼音数组
        StringBuilder pinyin = new StringBuilder();
        for (char ch : chinese.toCharArray()) {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(ch);

            // 如果字符是中文，则获取其拼音的首字母
            if (pinyinArray != null && pinyinArray.length > 0) {
                pinyin.append(pinyinArray[0].charAt(0));
            } else {
                // 非中文字符直接拼接
                pinyin.append(ch);
            }
        }
        return pinyin.toString();

    }

    @Override
    public List<Scenario> selectByTenantIdAndUserId(String tenantId, String userId) {
        return scenarioDao.selectByTenantIdAndUserId(tenantId, userId);
    }

}