package com.yhl.scp.ips.system.event.job;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryRealTimeDataVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.event.service.EventConfigService;
import com.yhl.scp.ips.system.event.service.EventDefinitionService;
import com.yhl.scp.ips.system.event.vo.EventConfigVO;
import com.yhl.scp.ips.system.event.vo.EventDefinitionVO;
import com.yhl.scp.ips.system.message.dto.MessageDTO;
import com.yhl.scp.ips.system.message.enums.MessageCategoryEnum;
import com.yhl.scp.ips.system.message.enums.MessageTypeEnum;
import com.yhl.scp.ips.system.service.ScenarioService;
import com.yhl.scp.ips.untils.MessageUtils;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <code>DataConsistenceCheckJob</code>
 * <p>
 * DataConsistenceCheckJob
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-21 17:16:51
 */
@Component
@Slf4j
public class DataConsistenceCheckJob {

    @Resource
    private ScenarioService scenarioService;

    @Resource
    private EventDefinitionService eventDefinitionService;

    @Resource
    private EventConfigService eventConfigService;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private MrpFeign mrpFeign;

    @Resource
    private MessageUtils messageUtils;

    @XxlJob("dataConsistenceCheckJob")
    public ReturnT<String> dataConsistenceCheckJob() {
        String jobParam = XxlJobHelper.getJobParam();
        String tenantId = StringUtils.isBlank(jobParam) ? TenantCodeEnum.FYQB.getCode() : jobParam;

        List<Scenario> scenarios = scenarioService.selectByTenantIdAndUserId(tenantId,null);
        if (CollectionUtils.isEmpty(scenarios)) {
            log.info("租户【{}】下不存在任何场景数据", tenantId);
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            String dataBaseName = scenario.getDataBaseName();
            try {
                log.info("场景【{}】下的数据一致性检查开始", dataBaseName);
                DynamicDataSourceContextHolder.setDataSource(dataBaseName);
                checkRealTimeInventoryData(tenantId, dataBaseName);
                checkWarehouseReleaseRecordData(tenantId, dataBaseName);
                checkTransitReleaseRecordData(tenantId, dataBaseName);
                checkCapacityBalanceWeeklyData(tenantId, dataBaseName);
                checkCapacityBalanceMonthlyData(tenantId, dataBaseName);
                checkGrossDemandData(tenantId, dataBaseName);
            } finally {
                DynamicDataSourceContextHolder.clearDataSource();
            }
            log.info("场景【{}】下的数据一致性检查完成", dataBaseName);
        }
        return ReturnT.SUCCESS;
    }

    private MessageDTO assembleMessage(String tenantId, String scenario, String eventCode) {
        // 获取配置信息
        List<EventDefinitionVO> eventDefinitions = eventDefinitionService.selectByParams(ImmutableMap.of("tenantId", tenantId, "eventCode", eventCode));
        if (CollectionUtils.isEmpty(eventDefinitions)) {
            return null;
        }
        List<String> eventIds = eventDefinitions.stream().map(EventDefinitionVO::getId).collect(Collectors.toList());
        List<EventConfigVO> eventConfigs = eventConfigService.selectByParams(ImmutableMap.of("eventIds", eventIds));
        if (CollectionUtils.isEmpty(eventConfigs)) {
            return null;
        }
        EventDefinitionVO eventDefinitionVO = eventDefinitions.get(0);
        EventConfigVO eventConfigVO = eventConfigs.get(0);
        String messageContent = eventConfigVO.getMessageContent();
        // 组装消息实体
        MessageDTO message = new MessageDTO();
        message.setScenario(scenario);
        message.setEventId(eventDefinitionVO.getId());
        message.setTodoId(null);
        message.setMessageCategory(MessageCategoryEnum.BUSINESS.getCode());
        message.setMessageType(MessageTypeEnum.ALERT.getCode());
        message.setMessageLevel(eventDefinitionVO.getMessageLevel());
        message.setMessageChannel(eventConfigVO.getMessageChannel());
        message.setMessageSubject(eventConfigVO.getMessageSubject());
        message.setMessageContent(messageContent);
        message.setMessageLink(eventConfigVO.getMessageLink());
        message.setSender(SystemHolder.getUserId());
        message.setReceiverType(eventDefinitionVO.getReceiverType());
        message.setReceivers(eventDefinitionVO.getReceivers());
        message.setCcReceivers(eventDefinitionVO.getCcReceivers());
        message.setReadFlag(YesOrNoEnum.NO.getCode());
        message.setProcessStatus(null);
        message.setSendTime(null);
        message.setRemark(null);
        message.setVersionValue(1);
        message.setEnabled(YesOrNoEnum.YES.getCode());
        return message;
    }

    void checkRealTimeInventoryData(String tenantId, String scenario) {
        // 1.查询配置
        String eventCode = "REAL_TIME_INVENTORY";
        MessageDTO messageDTO = assembleMessage(tenantId, scenario, eventCode);
        if (messageDTO == null) {
            log.info("场景【{}】下不存在【{}】事件配置", scenario, eventCode);
            return;
        }
        // 2.检查数据
        List<InventoryRealTimeDataVO> dataList = dfpFeign.selectInventoryRealTimeDataConsistentCheck(scenario);
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("场景【{}】下不存在【{}】未更新的定时库存数据", scenario, eventCode);
            return;
        }
        // 3.组装消息
        String messageContent = messageDTO.getMessageContent();
        String msgContent = dataList.stream().map(x -> String.format(messageContent, x.getStockPointCode(),
                        DateUtils.dateToString(x.getCreateTime(), DateUtils.COMMON_DATE_STR1)))
                .collect(Collectors.joining("<br/>"));
        messageDTO.setMessageContent(msgContent);
        // 4.丢入队列
        messageUtils.sendMessage(Lists.newArrayList(messageDTO));
    }

    void checkWarehouseReleaseRecordData(String tenantId, String scenario) {
        // 1.查询配置
        String eventCode = "WAREHOUSE_RELEASE_RECORD";
        MessageDTO messageDTO = assembleMessage(tenantId, scenario, eventCode);
        if (messageDTO == null) {
            log.info("场景【{}】下不存在【{}】事件配置", scenario, eventCode);
            return;
        }
        // 2.检查数据
        List<WarehouseReleaseRecordVO> dataList = dfpFeign.selectWarehouseReleaseRecordDataConsistentCheck(scenario);
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("场景【{}】下不存在【{}】未更新的仓库收发货数据", scenario, eventCode);
            return;
        }
        // 3.组装消息
        WarehouseReleaseRecordVO maxData = dataList.get(0);
        String messageContent = String.format(messageDTO.getMessageContent(), DateUtils.dateToString(maxData.getCreateTime(), DateUtils.COMMON_DATE_STR1));;
        messageDTO.setMessageContent(messageContent);
        // 4.丢入队列
        messageUtils.sendMessage(Lists.newArrayList(messageDTO));
    }

    void checkTransitReleaseRecordData(String tenantId, String scenario) {
        // 1.组装信息
        String eventCode = "TRANSIT_RELEASE_RECORD";
        MessageDTO messageDTO = assembleMessage(tenantId, scenario, eventCode);
        if (messageDTO == null) {
            log.info("场景【{}】下不存在【{}】事件配置", scenario, eventCode);
            return;
        }
        // 2.检查数据
        List<WarehouseReleaseToWarehouseVO> dataList = dfpFeign.selectTransitReleaseRecordDataConsistentCheck(scenario);
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("场景【{}】下不存在【{}】未更新的中转库收发货数据", scenario, eventCode);
            return;
        }
        // 3.组装消息
        WarehouseReleaseToWarehouseVO maxData = dataList.get(0);
        String messageContent = String.format(messageDTO.getMessageContent(), DateUtils.dateToString(maxData.getCreateTime(), DateUtils.COMMON_DATE_STR1));;
        messageDTO.setMessageContent(messageContent);
        // 4.丢入队列
        messageUtils.sendMessage(Lists.newArrayList(messageDTO));
    }

    void checkCapacityBalanceWeeklyData(String tenantId, String scenario) {
        // 1.组装信息
        String eventCode = "CAPACITY_BALANCE_WEEKLY";
        MessageDTO messageDTO = assembleMessage(tenantId, scenario, eventCode);
        if (messageDTO == null) {
            log.info("场景【{}】下不存在【{}】事件配置", scenario, eventCode);
            return;
        }
        // 2.检查数据
        List<CapacitySupplyRelationshipVO> dataList = mpsFeign.selectCapacityBalanceWeeklyDataConsistentCheck(scenario);
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("场景【{}】下不存在【{}】未更新的周产能平衡数据", scenario, eventCode);
            return;
        }
        // 3.组装消息
        CapacitySupplyRelationshipVO maxData = dataList.get(0);
        String messageContent = String.format(messageDTO.getMessageContent(), DateUtils.dateToString(maxData.getCreateTime(), DateUtils.COMMON_DATE_STR1));;
        messageDTO.setMessageContent(messageContent);
        // 4.丢入队列
        messageUtils.sendMessage(Lists.newArrayList(messageDTO));
    }

    void checkCapacityBalanceMonthlyData(String tenantId, String scenario) {
        // 1.组装信息
        String eventCode = "CAPACITY_BALANCE_MONTHLY";
        MessageDTO messageDTO = assembleMessage(tenantId, scenario, eventCode);
        if (messageDTO == null) {
            log.info("场景【{}】下不存在【{}】事件配置", scenario, eventCode);
            return;
        }
        // 2.检查数据
        List<CapacitySupplyRelationshipVO> dataList =
                mpsFeign.selectCapacityBalanceMonthlyDataConsistentCheck(scenario);
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("场景【{}】下不存在【{}】未更新的月产能平衡数据", scenario, eventCode);
            return;
        }
        // 3.组装消息
        CapacitySupplyRelationshipVO maxData = dataList.get(0);
        String messageContent = String.format(messageDTO.getMessageContent(), DateUtils.dateToString(maxData.getCreateTime(), DateUtils.COMMON_DATE_STR1));;
        messageDTO.setMessageContent(messageContent);
        // 4.丢入队列
        messageUtils.sendMessage(Lists.newArrayList(messageDTO));
    }

    void checkGrossDemandData(String tenantId, String scenario) {
        // 1.组装信息
        String eventCode = "GROSS_DEMAND";
        MessageDTO messageDTO = assembleMessage(tenantId, scenario, eventCode);
        if (messageDTO == null) {
            log.info("场景【{}】下不存在【{}】事件配置", scenario, eventCode);
            return;
        }
        // 2.检查数据
        List<MaterialGrossDemandVO> dataList = mrpFeign.selectMaterialGrossDemandDataConsistentCheck(scenario);
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("场景【{}】下不存在【{}】未更新的材料毛需求数据", scenario, eventCode);
            return;
        }
        // 3.组装消息
        MaterialGrossDemandVO maxData = dataList.get(0);
        String messageContent = String.format(messageDTO.getMessageContent(),
                DateUtils.dateToString(Objects.isNull(maxData) || Objects.isNull(maxData.getCreateTime())
                                ? new Date() : maxData.getCreateTime(), DateUtils.COMMON_DATE_STR1));
        messageDTO.setMessageContent(messageContent);
        // 4.丢入队列
        messageUtils.sendMessage(Lists.newArrayList(messageDTO));
    }

}