package com.yhl.scp.ips.system.message.domain.service;

import com.yhl.scp.ips.system.message.domain.entity.MessageDO;
import com.yhl.scp.ips.system.message.infrastructure.dao.MessageDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>MessageDomainService</code>
 * <p>
 * 消息表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:02
 */
@Service
public class MessageDomainService {

    @Resource
    private MessageDao messageDao;

    /**
     * 数据校验
     *
     * @param messageDO 领域对象
     */
    public void validation(MessageDO messageDO) {
        checkNotNull(messageDO);
        checkUniqueCode(messageDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param messageDO 领域对象
     */
    private void checkNotNull(MessageDO messageDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param messageDO 领域对象
     */
    private void checkUniqueCode(MessageDO messageDO) {
        // TODO
    }

}
