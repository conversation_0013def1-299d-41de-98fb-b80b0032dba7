package com.yhl.scp.ips.system.message.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MessageTemplatePO</code>
 * <p>
 * 消息模板表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:03
 */
public class MessageTemplatePO extends BasePO implements Serializable {

    private static final long serialVersionUID = 374100863945421392L;

    /**
     * 所属租户
     */
    private String tenantId;
    /**
     * 模板代码
     */
    private String templateCode;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 模板类型
     */
    private String templateType;
    /**
     * 消息渠道
     */
    private String messageChannel;
    /**
     * 消息内容
     */
    private String messageContent;
    /**
     * 默认参数
     */
    private String defaultParams;
    /**
     * 版本值
     */
    private Integer versionValue;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public String getMessageChannel() {
        return messageChannel;
    }

    public void setMessageChannel(String messageChannel) {
        this.messageChannel = messageChannel;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getDefaultParams() {
        return defaultParams;
    }

    public void setDefaultParams(String defaultParams) {
        this.defaultParams = defaultParams;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
