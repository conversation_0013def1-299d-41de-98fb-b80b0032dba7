<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.system.message.infrastructure.dao.MessageTemplateDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.system.message.infrastructure.po.MessageTemplatePO">
        <!--@Table sys_message_template-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="template_code" jdbcType="VARCHAR" property="templateCode"/>
        <result column="template_name" jdbcType="VARCHAR" property="templateName"/>
        <result column="template_type" jdbcType="VARCHAR" property="templateType"/>
        <result column="message_channel" jdbcType="VARCHAR" property="messageChannel"/>
        <result column="message_content" jdbcType="VARCHAR" property="messageContent"/>
        <result column="default_params" jdbcType="VARCHAR" property="defaultParams"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.system.message.vo.MessageTemplateVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,tenant_id,template_code,template_name,template_type,message_channel,message_content,default_params,remark,version_value,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.tenantId != null and params.tenantId != ''">
                and tenant_id = #{params.tenantId,jdbcType=VARCHAR}
            </if>
            <if test="params.templateCode != null and params.templateCode != ''">
                and template_code = #{params.templateCode,jdbcType=VARCHAR}
            </if>
            <if test="params.templateName != null and params.templateName != ''">
                and template_name = #{params.templateName,jdbcType=VARCHAR}
            </if>
            <if test="params.templateType != null and params.templateType != ''">
                and template_type = #{params.templateType,jdbcType=VARCHAR}
            </if>
            <if test="params.messageChannel != null and params.messageChannel != ''">
                and message_channel = #{params.messageChannel,jdbcType=VARCHAR}
            </if>
            <if test="params.messageContent != null and params.messageContent != ''">
                and message_content = #{params.messageContent,jdbcType=VARCHAR}
            </if>
            <if test="params.defaultParams != null and params.defaultParams != ''">
                and default_params = #{params.defaultParams,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_message_template
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_message_template
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_message_template
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_message_template
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_message_template
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.system.message.infrastructure.po.MessageTemplatePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sys_message_template(
        id,
        tenant_id,
        template_code,
        template_name,
        template_type,
        message_channel,
        message_content,
        default_params,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{tenantId,jdbcType=VARCHAR},
        #{templateCode,jdbcType=VARCHAR},
        #{templateName,jdbcType=VARCHAR},
        #{templateType,jdbcType=VARCHAR},
        #{messageChannel,jdbcType=VARCHAR},
        #{messageContent,jdbcType=VARCHAR},
        #{defaultParams,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.system.message.infrastructure.po.MessageTemplatePO">
        insert into sys_message_template(
        id,
        tenant_id,
        template_code,
        template_name,
        template_type,
        message_channel,
        message_content,
        default_params,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{tenantId,jdbcType=VARCHAR},
        #{templateCode,jdbcType=VARCHAR},
        #{templateName,jdbcType=VARCHAR},
        #{templateType,jdbcType=VARCHAR},
        #{messageChannel,jdbcType=VARCHAR},
        #{messageContent,jdbcType=VARCHAR},
        #{defaultParams,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_message_template(
        id,
        tenant_id,
        template_code,
        template_name,
        template_type,
        message_channel,
        message_content,
        default_params,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.tenantId,jdbcType=VARCHAR},
        #{entity.templateCode,jdbcType=VARCHAR},
        #{entity.templateName,jdbcType=VARCHAR},
        #{entity.templateType,jdbcType=VARCHAR},
        #{entity.messageChannel,jdbcType=VARCHAR},
        #{entity.messageContent,jdbcType=VARCHAR},
        #{entity.defaultParams,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sys_message_template(
        id,
        tenant_id,
        template_code,
        template_name,
        template_type,
        message_channel,
        message_content,
        default_params,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.tenantId,jdbcType=VARCHAR},
        #{entity.templateCode,jdbcType=VARCHAR},
        #{entity.templateName,jdbcType=VARCHAR},
        #{entity.templateType,jdbcType=VARCHAR},
        #{entity.messageChannel,jdbcType=VARCHAR},
        #{entity.messageContent,jdbcType=VARCHAR},
        #{entity.defaultParams,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.system.message.infrastructure.po.MessageTemplatePO">
        update sys_message_template set
        tenant_id = #{tenantId,jdbcType=VARCHAR},
        template_code = #{templateCode,jdbcType=VARCHAR},
        template_name = #{templateName,jdbcType=VARCHAR},
        template_type = #{templateType,jdbcType=VARCHAR},
        message_channel = #{messageChannel,jdbcType=VARCHAR},
        message_content = #{messageContent,jdbcType=VARCHAR},
        default_params = #{defaultParams,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.system.message.infrastructure.po.MessageTemplatePO">
        update sys_message_template
        <set>
            <if test="item.tenantId != null and item.tenantId != ''">
                tenant_id = #{item.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="item.templateCode != null and item.templateCode != ''">
                template_code = #{item.templateCode,jdbcType=VARCHAR},
            </if>
            <if test="item.templateName != null and item.templateName != ''">
                template_name = #{item.templateName,jdbcType=VARCHAR},
            </if>
            <if test="item.templateType != null and item.templateType != ''">
                template_type = #{item.templateType,jdbcType=VARCHAR},
            </if>
            <if test="item.messageChannel != null and item.messageChannel != ''">
                message_channel = #{item.messageChannel,jdbcType=VARCHAR},
            </if>
            <if test="item.messageContent != null and item.messageContent != ''">
                message_content = #{item.messageContent,jdbcType=VARCHAR},
            </if>
            <if test="item.defaultParams != null and item.defaultParams != ''">
                default_params = #{item.defaultParams,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sys_message_template
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tenant_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.tenantId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="template_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.templateCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="template_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.templateName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="template_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.templateType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageChannel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="message_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.messageContent,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="default_params = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.defaultParams,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sys_message_template 
        <set>
            <if test="item.tenantId != null and item.tenantId != ''">
                tenant_id = #{item.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="item.templateCode != null and item.templateCode != ''">
                template_code = #{item.templateCode,jdbcType=VARCHAR},
            </if>
            <if test="item.templateName != null and item.templateName != ''">
                template_name = #{item.templateName,jdbcType=VARCHAR},
            </if>
            <if test="item.templateType != null and item.templateType != ''">
                template_type = #{item.templateType,jdbcType=VARCHAR},
            </if>
            <if test="item.messageChannel != null and item.messageChannel != ''">
                message_channel = #{item.messageChannel,jdbcType=VARCHAR},
            </if>
            <if test="item.messageContent != null and item.messageContent != ''">
                message_content = #{item.messageContent,jdbcType=VARCHAR},
            </if>
            <if test="item.defaultParams != null and item.defaultParams != ''">
                default_params = #{item.defaultParams,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sys_message_template where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sys_message_template where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
