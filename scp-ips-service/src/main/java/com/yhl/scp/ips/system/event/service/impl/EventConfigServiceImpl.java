package com.yhl.scp.ips.system.event.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.ips.system.event.convertor.EventConfigConvertor;
import com.yhl.scp.ips.system.event.domain.entity.EventConfigDO;
import com.yhl.scp.ips.system.event.domain.service.EventConfigDomainService;
import com.yhl.scp.ips.system.event.dto.EventConfigDTO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventConfigDao;
import com.yhl.scp.ips.system.event.infrastructure.po.EventConfigPO;
import com.yhl.scp.ips.system.event.service.EventConfigService;
import com.yhl.scp.ips.system.event.vo.EventConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>EventConfigServiceImpl</code>
 * <p>
 * 事件配置表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
@Slf4j
@Service
public class EventConfigServiceImpl extends AbstractService implements EventConfigService {

    @Resource
    private EventConfigDao eventConfigDao;

    @Resource
    private EventConfigDomainService eventConfigDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(EventConfigDTO eventConfigDTO) {
        // 0.数据转换
        EventConfigDO eventConfigDO = EventConfigConvertor.INSTANCE.dto2Do(eventConfigDTO);
        EventConfigPO eventConfigPO = EventConfigConvertor.INSTANCE.dto2Po(eventConfigDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        eventConfigDomainService.validation(eventConfigDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(eventConfigPO);
        eventConfigDao.insertWithPrimaryKey(eventConfigPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(EventConfigDTO eventConfigDTO) {
        // 0.数据转换
        EventConfigDO eventConfigDO = EventConfigConvertor.INSTANCE.dto2Do(eventConfigDTO);
        EventConfigPO eventConfigPO = EventConfigConvertor.INSTANCE.dto2Po(eventConfigDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        eventConfigDomainService.validation(eventConfigDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(eventConfigPO);
        eventConfigDao.update(eventConfigPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<EventConfigDTO> list) {
        List<EventConfigPO> newList = EventConfigConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        eventConfigDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<EventConfigDTO> list) {
        List<EventConfigPO> newList = EventConfigConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        eventConfigDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return eventConfigDao.deleteBatch(idList);
        }
        return eventConfigDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public EventConfigVO selectByPrimaryKey(String id) {
        EventConfigPO po = eventConfigDao.selectByPrimaryKey(id);
        return EventConfigConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sys_event_config")
    public List<EventConfigVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sys_event_config")
    public List<EventConfigVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<EventConfigVO> dataList = eventConfigDao.selectByCondition(sortParam, queryCriteriaParam);
        EventConfigServiceImpl target = springBeanUtils.getBean(EventConfigServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<EventConfigVO> selectByParams(Map<String, Object> params) {
        List<EventConfigPO> list = eventConfigDao.selectByParams(params);
        return EventConfigConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<EventConfigVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.EVENT_CONFIG.getCode();
    }

    @Override
    public List<EventConfigVO> invocation(List<EventConfigVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
