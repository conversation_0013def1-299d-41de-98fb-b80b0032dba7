package com.yhl.scp.ips.system.message.convertor;

import com.yhl.scp.ips.system.message.domain.entity.MessageTemplateDO;
import com.yhl.scp.ips.system.message.dto.MessageTemplateDTO;
import com.yhl.scp.ips.system.message.infrastructure.po.MessageTemplatePO;
import com.yhl.scp.ips.system.message.vo.MessageTemplateVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MessageTemplateConvertor</code>
 * <p>
 * 消息模板表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:04
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MessageTemplateConvertor {

    MessageTemplateConvertor INSTANCE = Mappers.getMapper(MessageTemplateConvertor.class);

    MessageTemplateDO dto2Do(MessageTemplateDTO obj);

    List<MessageTemplateDO> dto2Dos(List<MessageTemplateDTO> list);

    MessageTemplateDTO do2Dto(MessageTemplateDO obj);

    List<MessageTemplateDTO> do2Dtos(List<MessageTemplateDO> list);

    MessageTemplateDTO vo2Dto(MessageTemplateVO obj);

    List<MessageTemplateDTO> vo2Dtos(List<MessageTemplateVO> list);

    MessageTemplateVO po2Vo(MessageTemplatePO obj);

    List<MessageTemplateVO> po2Vos(List<MessageTemplatePO> list);

    MessageTemplatePO dto2Po(MessageTemplateDTO obj);

    List<MessageTemplatePO> dto2Pos(List<MessageTemplateDTO> obj);

    MessageTemplateVO do2Vo(MessageTemplateDO obj);

    MessageTemplatePO do2Po(MessageTemplateDO obj);

    MessageTemplateDO po2Do(MessageTemplatePO obj);

}
