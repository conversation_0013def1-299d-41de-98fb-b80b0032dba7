package com.yhl.scp.ips.system.message.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.util.Date;

/**
 * <code>MessageTemplateDO</code>
 * <p>
 * 消息模板表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:03
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MessageTemplateDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -76118504323835006L;

/**
     * 主键ID
     */
    private String id;
/**
     * 所属租户
     */
    private String tenantId;
/**
     * 模板代码
     */
    private String templateCode;
/**
     * 模板名称
     */
    private String templateName;
/**
     * 模板类型
     */
    private String templateType;
/**
     * 消息渠道
     */
    private String messageChannel;
/**
     * 消息内容
     */
    private String messageContent;
/**
     * 默认参数
     */
    private String defaultParams;
/**
     * 版本值
     */
    private Integer versionValue;

}
