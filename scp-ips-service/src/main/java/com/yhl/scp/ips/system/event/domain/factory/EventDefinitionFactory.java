package com.yhl.scp.ips.system.event.domain.factory;

import com.yhl.scp.ips.system.event.domain.entity.EventDefinitionDO;
import com.yhl.scp.ips.system.event.dto.EventDefinitionDTO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventDefinitionDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>EventDefinitionFactory</code>
 * <p>
 * 事件定义表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
@Component
public class EventDefinitionFactory {

    @Resource
    private EventDefinitionDao eventDefinitionDao;

    EventDefinitionDO create(EventDefinitionDTO dto) {
        // TODO
        return null;
    }

}
