package com.yhl.scp.ips.system.event.domain.factory;

import com.yhl.scp.ips.system.event.domain.entity.EventConfigDO;
import com.yhl.scp.ips.system.event.dto.EventConfigDTO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventConfigDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>EventConfigFactory</code>
 * <p>
 * 事件配置表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
@Component
public class EventConfigFactory {

    @Resource
    private EventConfigDao eventConfigDao;

    EventConfigDO create(EventConfigDTO dto) {
        // TODO
        return null;
    }

}
