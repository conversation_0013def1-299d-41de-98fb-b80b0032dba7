package com.yhl.scp.ips.system.event.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.util.Date;

/**
 * <code>EventDefinitionDO</code>
 * <p>
 * 事件定义表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 20:02:08
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EventDefinitionDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 692501712390715375L;

/**
     * 主键ID
     */
    private String id;
/**
     * 所属租户
     */
    private String tenantId;
/**
     * 所属模块
     */
    private String moduleCode;
/**
     * 事件代码
     */
    private String eventCode;
/**
     * 事件名称
     */
    private String eventName;
/**
     * 事件类型
     */
    private String eventType;
/**
     * 事件频率
     */
    private String eventFrequency;
/**
     * 是否虚拟
     */
    private String virtualFlag;
/**
     * 触发方式
     */
    private String triggerMethods;
/**
     * 消息类型
     */
    private String messageType;
/**
     * 接收类型
     */
    private String receiverType;
/**
     * 接收人列表
     */
    private String receivers;
/**
     * 抄送人列表
     */
    private String ccReceivers;
/**
     * Java代理
     */
    private String javaDelegator;
/**
     * 消息级别
     */
    private String messageLevel;
/**
     * 升级级别
     */
    private Integer upgradeLevel;
/**
     * 版本值
     */
    private Integer versionValue;

}
