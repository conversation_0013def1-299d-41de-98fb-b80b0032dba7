package com.yhl.scp.ips.system.event.domain.service;

import com.yhl.scp.ips.system.event.domain.entity.EventLogDO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventLogDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>EventLogDomainService</code>
 * <p>
 * 事件日志表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Service
public class EventLogDomainService {

    @Resource
    private EventLogDao eventLogDao;

    /**
     * 数据校验
     *
     * @param eventLogDO 领域对象
     */
    public void validation(EventLogDO eventLogDO) {
        checkNotNull(eventLogDO);
        checkUniqueCode(eventLogDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param eventLogDO 领域对象
     */
    private void checkNotNull(EventLogDO eventLogDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param eventLogDO 领域对象
     */
    private void checkUniqueCode(EventLogDO eventLogDO) {
        // TODO
    }

}
