package com.yhl.scp.ips.system.event.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.system.event.infrastructure.po.EventDependencyConfigPO;
import com.yhl.scp.ips.system.event.vo.EventDependencyConfigVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>EventDependencyConfigDao</code>
 * <p>
 * 事件依赖配置表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
public interface EventDependencyConfigDao extends BaseDao<EventDependencyConfigPO, EventDependencyConfigVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link EventDependencyConfigVO}
     */
    List<EventDependencyConfigVO> selectVOByParams(@Param("params") Map<String, Object> params);

}
