package com.yhl.scp.ips.system.event.domain.factory;

import com.yhl.scp.ips.system.event.domain.entity.EventUpgradeConfigDO;
import com.yhl.scp.ips.system.event.dto.EventUpgradeConfigDTO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventUpgradeConfigDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>EventUpgradeConfigFactory</code>
 * <p>
 * 事件升级配置表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Component
public class EventUpgradeConfigFactory {

    @Resource
    private EventUpgradeConfigDao eventUpgradeConfigDao;

    EventUpgradeConfigDO create(EventUpgradeConfigDTO dto) {
        // TODO
        return null;
    }

}
