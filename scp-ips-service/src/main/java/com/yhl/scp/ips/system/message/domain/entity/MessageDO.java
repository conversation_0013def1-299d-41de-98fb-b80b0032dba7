package com.yhl.scp.ips.system.message.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.util.Date;

/**
 * <code>MessageDO</code>
 * <p>
 * 消息表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:01
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MessageDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 321266806598380205L;

/**
     * 主键ID
     */
    private String id;
/**
     * 所属场景
     */
    private String scenario;
/**
     * 事件ID
     */
    private String eventId;
/**
     * 待办ID
     */
    private String todoId;
/**
     * 消息大类
     */
    private String messageCategory;
/**
     * 消息类型
     */
    private String messageType;
/**
     * 消息级别
     */
    private String messageLevel;
/**
     * 消息渠道
     */
    private String messageChannel;
/**
     * 消息主题
     */
    private String messageSubject;
/**
     * 消息内容
     */
    private String messageContent;
/**
     * 消息链接
     */
    private String messageLink;
/**
     * 发送人
     */
    private String sender;
/**
     * 接收类型
     */
    private String receiverType;
/**
     * 接收人列表
     */
    private String receivers;
/**
     * 抄送人列表
     */
    private String ccReceivers;
/**
     * 是否已读
     */
    private String readFlag;
/**
     * 处理状态
     */
    private String processStatus;
/**
     * 发送时间
     */
    private Date sendTime;
/**
     * 版本值
     */
    private Integer versionValue;

}
