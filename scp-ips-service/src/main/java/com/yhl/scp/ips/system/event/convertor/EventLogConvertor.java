package com.yhl.scp.ips.system.event.convertor;

import com.yhl.scp.ips.system.event.domain.entity.EventLogDO;
import com.yhl.scp.ips.system.event.dto.EventLogDTO;
import com.yhl.scp.ips.system.event.infrastructure.po.EventLogPO;
import com.yhl.scp.ips.system.event.vo.EventLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>EventLogConvertor</code>
 * <p>
 * 事件日志表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EventLogConvertor {

    EventLogConvertor INSTANCE = Mappers.getMapper(EventLogConvertor.class);

    EventLogDO dto2Do(EventLogDTO obj);

    List<EventLogDO> dto2Dos(List<EventLogDTO> list);

    EventLogDTO do2Dto(EventLogDO obj);

    List<EventLogDTO> do2Dtos(List<EventLogDO> list);

    EventLogDTO vo2Dto(EventLogVO obj);

    List<EventLogDTO> vo2Dtos(List<EventLogVO> list);

    EventLogVO po2Vo(EventLogPO obj);

    List<EventLogVO> po2Vos(List<EventLogPO> list);

    EventLogPO dto2Po(EventLogDTO obj);

    List<EventLogPO> dto2Pos(List<EventLogDTO> obj);

    EventLogVO do2Vo(EventLogDO obj);

    EventLogPO do2Po(EventLogDO obj);

    EventLogDO po2Do(EventLogPO obj);

}
