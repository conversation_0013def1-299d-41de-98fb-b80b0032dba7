package com.yhl.scp.ips.system.event.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.ips.system.event.convertor.EventUpgradeConfigConvertor;
import com.yhl.scp.ips.system.event.domain.entity.EventUpgradeConfigDO;
import com.yhl.scp.ips.system.event.domain.service.EventUpgradeConfigDomainService;
import com.yhl.scp.ips.system.event.dto.EventUpgradeConfigDTO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventUpgradeConfigDao;
import com.yhl.scp.ips.system.event.infrastructure.po.EventUpgradeConfigPO;
import com.yhl.scp.ips.system.event.service.EventUpgradeConfigService;
import com.yhl.scp.ips.system.event.vo.EventUpgradeConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>EventUpgradeConfigServiceImpl</code>
 * <p>
 * 事件升级配置表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Slf4j
@Service
public class EventUpgradeConfigServiceImpl extends AbstractService implements EventUpgradeConfigService {

    @Resource
    private EventUpgradeConfigDao eventUpgradeConfigDao;

    @Resource
    private EventUpgradeConfigDomainService eventUpgradeConfigDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(EventUpgradeConfigDTO eventUpgradeConfigDTO) {
        // 0.数据转换
        EventUpgradeConfigDO eventUpgradeConfigDO = EventUpgradeConfigConvertor.INSTANCE.dto2Do(eventUpgradeConfigDTO);
        EventUpgradeConfigPO eventUpgradeConfigPO = EventUpgradeConfigConvertor.INSTANCE.dto2Po(eventUpgradeConfigDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        eventUpgradeConfigDomainService.validation(eventUpgradeConfigDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(eventUpgradeConfigPO);
        eventUpgradeConfigDao.insertWithPrimaryKey(eventUpgradeConfigPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(EventUpgradeConfigDTO eventUpgradeConfigDTO) {
        // 0.数据转换
        EventUpgradeConfigDO eventUpgradeConfigDO = EventUpgradeConfigConvertor.INSTANCE.dto2Do(eventUpgradeConfigDTO);
        EventUpgradeConfigPO eventUpgradeConfigPO = EventUpgradeConfigConvertor.INSTANCE.dto2Po(eventUpgradeConfigDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        eventUpgradeConfigDomainService.validation(eventUpgradeConfigDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(eventUpgradeConfigPO);
        eventUpgradeConfigDao.update(eventUpgradeConfigPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<EventUpgradeConfigDTO> list) {
        List<EventUpgradeConfigPO> newList = EventUpgradeConfigConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        eventUpgradeConfigDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<EventUpgradeConfigDTO> list) {
        List<EventUpgradeConfigPO> newList = EventUpgradeConfigConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        eventUpgradeConfigDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return eventUpgradeConfigDao.deleteBatch(idList);
        }
        return eventUpgradeConfigDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public EventUpgradeConfigVO selectByPrimaryKey(String id) {
        EventUpgradeConfigPO po = eventUpgradeConfigDao.selectByPrimaryKey(id);
        return EventUpgradeConfigConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sys_event_upgrade_config")
    public List<EventUpgradeConfigVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sys_event_upgrade_config")
    public List<EventUpgradeConfigVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<EventUpgradeConfigVO> dataList = eventUpgradeConfigDao.selectByCondition(sortParam, queryCriteriaParam);
        EventUpgradeConfigServiceImpl target = springBeanUtils.getBean(EventUpgradeConfigServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<EventUpgradeConfigVO> selectByParams(Map<String, Object> params) {
        List<EventUpgradeConfigPO> list = eventUpgradeConfigDao.selectByParams(params);
        return EventUpgradeConfigConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<EventUpgradeConfigVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.EVENT_UPGRADE_CONFIG.getCode();
    }

    @Override
    public List<EventUpgradeConfigVO> invocation(List<EventUpgradeConfigVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
