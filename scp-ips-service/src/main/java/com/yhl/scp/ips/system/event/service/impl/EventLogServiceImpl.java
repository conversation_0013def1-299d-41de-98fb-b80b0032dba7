package com.yhl.scp.ips.system.event.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.ips.system.event.convertor.EventLogConvertor;
import com.yhl.scp.ips.system.event.domain.entity.EventLogDO;
import com.yhl.scp.ips.system.event.domain.service.EventLogDomainService;
import com.yhl.scp.ips.system.event.dto.EventLogDTO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventLogDao;
import com.yhl.scp.ips.system.event.infrastructure.po.EventLogPO;
import com.yhl.scp.ips.system.event.service.EventLogService;
import com.yhl.scp.ips.system.event.vo.EventLogVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>EventLogServiceImpl</code>
 * <p>
 * 事件日志表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Slf4j
@Service
public class EventLogServiceImpl extends AbstractService implements EventLogService {

    @Resource
    private EventLogDao eventLogDao;

    @Resource
    private EventLogDomainService eventLogDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(EventLogDTO eventLogDTO) {
        // 0.数据转换
        EventLogDO eventLogDO = EventLogConvertor.INSTANCE.dto2Do(eventLogDTO);
        EventLogPO eventLogPO = EventLogConvertor.INSTANCE.dto2Po(eventLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        eventLogDomainService.validation(eventLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(eventLogPO);
        eventLogDao.insertWithPrimaryKey(eventLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(EventLogDTO eventLogDTO) {
        // 0.数据转换
        EventLogDO eventLogDO = EventLogConvertor.INSTANCE.dto2Do(eventLogDTO);
        EventLogPO eventLogPO = EventLogConvertor.INSTANCE.dto2Po(eventLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        eventLogDomainService.validation(eventLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(eventLogPO);
        eventLogDao.update(eventLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<EventLogDTO> list) {
        List<EventLogPO> newList = EventLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        eventLogDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<EventLogDTO> list) {
        List<EventLogPO> newList = EventLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        eventLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return eventLogDao.deleteBatch(idList);
        }
        return eventLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public EventLogVO selectByPrimaryKey(String id) {
        EventLogPO po = eventLogDao.selectByPrimaryKey(id);
        return EventLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sys_event_log")
    public List<EventLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sys_event_log")
    public List<EventLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<EventLogVO> dataList = eventLogDao.selectByCondition(sortParam, queryCriteriaParam);
        EventLogServiceImpl target = springBeanUtils.getBean(EventLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<EventLogVO> selectByParams(Map<String, Object> params) {
        List<EventLogPO> list = eventLogDao.selectByParams(params);
        return EventLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<EventLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.EVENT_LOG.getCode();
    }

    @Override
    public List<EventLogVO> invocation(List<EventLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
