<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.system.event.infrastructure.dao.EventDependencyConfigDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.system.event.infrastructure.po.EventDependencyConfigPO">
        <!--@Table sys_event_dependency_config-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="event_id" jdbcType="VARCHAR" property="eventId"/>
        <result column="pre_event_id" jdbcType="VARCHAR" property="preEventId"/>
        <result column="day_offset" jdbcType="INTEGER" property="dayOffset"/>
        <result column="java_delegator" jdbcType="VARCHAR" property="javaDelegator"/>
        <result column="requisite_flag" jdbcType="VARCHAR" property="requisiteFlag"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.system.event.vo.EventDependencyConfigVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,event_id,pre_event_id,day_offset,java_delegator,requisite_flag,remark,version_value,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.eventId != null and params.eventId != ''">
                and event_id = #{params.eventId,jdbcType=VARCHAR}
            </if>
            <if test="params.preEventId != null and params.preEventId != ''">
                and pre_event_id = #{params.preEventId,jdbcType=VARCHAR}
            </if>
            <if test="params.dayOffset != null">
                and day_offset = #{params.dayOffset,jdbcType=INTEGER}
            </if>
            <if test="params.javaDelegator != null and params.javaDelegator != ''">
                and java_delegator = #{params.javaDelegator,jdbcType=VARCHAR}
            </if>
            <if test="params.requisiteFlag != null and params.requisiteFlag != ''">
                and requisite_flag = #{params.requisiteFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_dependency_config
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_dependency_config
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_event_dependency_config
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_dependency_config
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_event_dependency_config
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventDependencyConfigPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sys_event_dependency_config(
        id,
        event_id,
        pre_event_id,
        day_offset,
        java_delegator,
        requisite_flag,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{eventId,jdbcType=VARCHAR},
        #{preEventId,jdbcType=VARCHAR},
        #{dayOffset,jdbcType=INTEGER},
        #{javaDelegator,jdbcType=VARCHAR},
        #{requisiteFlag,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventDependencyConfigPO">
        insert into sys_event_dependency_config(
        id,
        event_id,
        pre_event_id,
        day_offset,
        java_delegator,
        requisite_flag,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{eventId,jdbcType=VARCHAR},
        #{preEventId,jdbcType=VARCHAR},
        #{dayOffset,jdbcType=INTEGER},
        #{javaDelegator,jdbcType=VARCHAR},
        #{requisiteFlag,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_event_dependency_config(
        id,
        event_id,
        pre_event_id,
        day_offset,
        java_delegator,
        requisite_flag,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.eventId,jdbcType=VARCHAR},
        #{entity.preEventId,jdbcType=VARCHAR},
        #{entity.dayOffset,jdbcType=INTEGER},
        #{entity.javaDelegator,jdbcType=VARCHAR},
        #{entity.requisiteFlag,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sys_event_dependency_config(
        id,
        event_id,
        pre_event_id,
        day_offset,
        java_delegator,
        requisite_flag,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.eventId,jdbcType=VARCHAR},
        #{entity.preEventId,jdbcType=VARCHAR},
        #{entity.dayOffset,jdbcType=INTEGER},
        #{entity.javaDelegator,jdbcType=VARCHAR},
        #{entity.requisiteFlag,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventDependencyConfigPO">
        update sys_event_dependency_config set
        event_id = #{eventId,jdbcType=VARCHAR},
        pre_event_id = #{preEventId,jdbcType=VARCHAR},
        day_offset = #{dayOffset,jdbcType=INTEGER},
        java_delegator = #{javaDelegator,jdbcType=VARCHAR},
        requisite_flag = #{requisiteFlag,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventDependencyConfigPO">
        update sys_event_dependency_config
        <set>
            <if test="item.eventId != null and item.eventId != ''">
                event_id = #{item.eventId,jdbcType=VARCHAR},
            </if>
            <if test="item.preEventId != null and item.preEventId != ''">
                pre_event_id = #{item.preEventId,jdbcType=VARCHAR},
            </if>
            <if test="item.dayOffset != null">
                day_offset = #{item.dayOffset,jdbcType=INTEGER},
            </if>
            <if test="item.javaDelegator != null and item.javaDelegator != ''">
                java_delegator = #{item.javaDelegator,jdbcType=VARCHAR},
            </if>
            <if test="item.requisiteFlag != null and item.requisiteFlag != ''">
                requisite_flag = #{item.requisiteFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sys_event_dependency_config
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="event_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="pre_event_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.preEventId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="day_offset = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dayOffset,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="java_delegator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.javaDelegator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="requisite_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requisiteFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sys_event_dependency_config 
        <set>
            <if test="item.eventId != null and item.eventId != ''">
                event_id = #{item.eventId,jdbcType=VARCHAR},
            </if>
            <if test="item.preEventId != null and item.preEventId != ''">
                pre_event_id = #{item.preEventId,jdbcType=VARCHAR},
            </if>
            <if test="item.dayOffset != null">
                day_offset = #{item.dayOffset,jdbcType=INTEGER},
            </if>
            <if test="item.javaDelegator != null and item.javaDelegator != ''">
                java_delegator = #{item.javaDelegator,jdbcType=VARCHAR},
            </if>
            <if test="item.requisiteFlag != null and item.requisiteFlag != ''">
                requisite_flag = #{item.requisiteFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sys_event_dependency_config where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sys_event_dependency_config where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
