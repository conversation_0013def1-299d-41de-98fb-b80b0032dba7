package com.yhl.scp.ips.system.event.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.util.Date;

/**
 * <code>EventDependencyConfigDO</code>
 * <p>
 * 事件依赖配置表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EventDependencyConfigDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -98799718407108582L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 事件定义ID
     */
    private String eventId;
    /**
     * 前置事件定义ID
     */
    private String preEventId;
    /**
     * 事件偏移日
     */
    private Integer dayOffset;
    /**
     * Java代理
     */
    private String javaDelegator;
    /**
     * 是否必须
     */
    private String requisiteFlag;
    /**
     * 版本值
     */
    private Integer versionValue;

}
