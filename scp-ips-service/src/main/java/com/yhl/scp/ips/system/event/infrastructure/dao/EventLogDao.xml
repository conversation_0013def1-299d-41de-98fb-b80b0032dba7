<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.system.event.infrastructure.dao.EventLogDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.system.event.infrastructure.po.EventLogPO">
        <!--@Table sys_event_log-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="scenario" jdbcType="VARCHAR" property="scenario"/>
        <result column="event_id" jdbcType="VARCHAR" property="eventId"/>
        <result column="event_code" jdbcType="VARCHAR" property="eventCode"/>
        <result column="event_name" jdbcType="VARCHAR" property="eventName"/>
        <result column="event_type" jdbcType="VARCHAR" property="eventType"/>
        <result column="event_frequency" jdbcType="VARCHAR" property="eventFrequency"/>
        <result column="trigger_method" jdbcType="VARCHAR" property="triggerMethod"/>
        <result column="operation_status" jdbcType="VARCHAR" property="operationStatus"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.system.event.vo.EventLogVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,scenario,event_id,event_code,event_name,event_type,event_frequency,trigger_method,operation_status,operator,start_time,end_time,remark,version_value,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.scenario != null and params.scenario != ''">
                and scenario = #{params.scenario,jdbcType=VARCHAR}
            </if>
            <if test="params.eventId != null and params.eventId != ''">
                and event_id = #{params.eventId,jdbcType=VARCHAR}
            </if>
            <if test="params.eventCode != null and params.eventCode != ''">
                and event_code = #{params.eventCode,jdbcType=VARCHAR}
            </if>
            <if test="params.eventName != null and params.eventName != ''">
                and event_name = #{params.eventName,jdbcType=VARCHAR}
            </if>
            <if test="params.eventType != null and params.eventType != ''">
                and event_type = #{params.eventType,jdbcType=VARCHAR}
            </if>
            <if test="params.eventFrequency != null and params.eventFrequency != ''">
                and event_frequency = #{params.eventFrequency,jdbcType=VARCHAR}
            </if>
            <if test="params.triggerMethod != null and params.triggerMethod != ''">
                and trigger_method = #{params.triggerMethod,jdbcType=VARCHAR}
            </if>
            <if test="params.operationStatus != null and params.operationStatus != ''">
                and operation_status = #{params.operationStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.operator != null and params.operator != ''">
                and operator = #{params.operator,jdbcType=VARCHAR}
            </if>
            <if test="params.startTime != null">
                and start_time = #{params.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endTime != null">
                and end_time = #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_log
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_log
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_event_log
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_event_log
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sys_event_log
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventLogPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sys_event_log(
        id,
        scenario,
        event_id,
        event_code,
        event_name,
        event_type,
        event_frequency,
        trigger_method,
        operation_status,
        operator,
        start_time,
        end_time,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{scenario,jdbcType=VARCHAR},
        #{eventId,jdbcType=VARCHAR},
        #{eventCode,jdbcType=VARCHAR},
        #{eventName,jdbcType=VARCHAR},
        #{eventType,jdbcType=VARCHAR},
        #{eventFrequency,jdbcType=VARCHAR},
        #{triggerMethod,jdbcType=VARCHAR},
        #{operationStatus,jdbcType=VARCHAR},
        #{operator,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventLogPO">
        insert into sys_event_log(
        id,
        scenario,
        event_id,
        event_code,
        event_name,
        event_type,
        event_frequency,
        trigger_method,
        operation_status,
        operator,
        start_time,
        end_time,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{scenario,jdbcType=VARCHAR},
        #{eventId,jdbcType=VARCHAR},
        #{eventCode,jdbcType=VARCHAR},
        #{eventName,jdbcType=VARCHAR},
        #{eventType,jdbcType=VARCHAR},
        #{eventFrequency,jdbcType=VARCHAR},
        #{triggerMethod,jdbcType=VARCHAR},
        #{operationStatus,jdbcType=VARCHAR},
        #{operator,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_event_log(
        id,
        scenario,
        event_id,
        event_code,
        event_name,
        event_type,
        event_frequency,
        trigger_method,
        operation_status,
        operator,
        start_time,
        end_time,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.scenario,jdbcType=VARCHAR},
        #{entity.eventId,jdbcType=VARCHAR},
        #{entity.eventCode,jdbcType=VARCHAR},
        #{entity.eventName,jdbcType=VARCHAR},
        #{entity.eventType,jdbcType=VARCHAR},
        #{entity.eventFrequency,jdbcType=VARCHAR},
        #{entity.triggerMethod,jdbcType=VARCHAR},
        #{entity.operationStatus,jdbcType=VARCHAR},
        #{entity.operator,jdbcType=VARCHAR},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sys_event_log(
        id,
        scenario,
        event_id,
        event_code,
        event_name,
        event_type,
        event_frequency,
        trigger_method,
        operation_status,
        operator,
        start_time,
        end_time,
        remark,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.scenario,jdbcType=VARCHAR},
        #{entity.eventId,jdbcType=VARCHAR},
        #{entity.eventCode,jdbcType=VARCHAR},
        #{entity.eventName,jdbcType=VARCHAR},
        #{entity.eventType,jdbcType=VARCHAR},
        #{entity.eventFrequency,jdbcType=VARCHAR},
        #{entity.triggerMethod,jdbcType=VARCHAR},
        #{entity.operationStatus,jdbcType=VARCHAR},
        #{entity.operator,jdbcType=VARCHAR},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventLogPO">
        update sys_event_log set
        scenario = #{scenario,jdbcType=VARCHAR},
        event_id = #{eventId,jdbcType=VARCHAR},
        event_code = #{eventCode,jdbcType=VARCHAR},
        event_name = #{eventName,jdbcType=VARCHAR},
        event_type = #{eventType,jdbcType=VARCHAR},
        event_frequency = #{eventFrequency,jdbcType=VARCHAR},
        trigger_method = #{triggerMethod,jdbcType=VARCHAR},
        operation_status = #{operationStatus,jdbcType=VARCHAR},
        operator = #{operator,jdbcType=VARCHAR},
        start_time = #{startTime,jdbcType=TIMESTAMP},
        end_time = #{endTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.system.event.infrastructure.po.EventLogPO">
        update sys_event_log
        <set>
            <if test="item.scenario != null and item.scenario != ''">
                scenario = #{item.scenario,jdbcType=VARCHAR},
            </if>
            <if test="item.eventId != null and item.eventId != ''">
                event_id = #{item.eventId,jdbcType=VARCHAR},
            </if>
            <if test="item.eventCode != null and item.eventCode != ''">
                event_code = #{item.eventCode,jdbcType=VARCHAR},
            </if>
            <if test="item.eventName != null and item.eventName != ''">
                event_name = #{item.eventName,jdbcType=VARCHAR},
            </if>
            <if test="item.eventType != null and item.eventType != ''">
                event_type = #{item.eventType,jdbcType=VARCHAR},
            </if>
            <if test="item.eventFrequency != null and item.eventFrequency != ''">
                event_frequency = #{item.eventFrequency,jdbcType=VARCHAR},
            </if>
            <if test="item.triggerMethod != null and item.triggerMethod != ''">
                trigger_method = #{item.triggerMethod,jdbcType=VARCHAR},
            </if>
            <if test="item.operationStatus != null and item.operationStatus != ''">
                operation_status = #{item.operationStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.operator != null and item.operator != ''">
                operator = #{item.operator,jdbcType=VARCHAR},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sys_event_log
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="scenario = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scenario,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="event_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="event_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="event_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="event_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="event_frequency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventFrequency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="trigger_method = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.triggerMethod,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sys_event_log 
        <set>
            <if test="item.scenario != null and item.scenario != ''">
                scenario = #{item.scenario,jdbcType=VARCHAR},
            </if>
            <if test="item.eventId != null and item.eventId != ''">
                event_id = #{item.eventId,jdbcType=VARCHAR},
            </if>
            <if test="item.eventCode != null and item.eventCode != ''">
                event_code = #{item.eventCode,jdbcType=VARCHAR},
            </if>
            <if test="item.eventName != null and item.eventName != ''">
                event_name = #{item.eventName,jdbcType=VARCHAR},
            </if>
            <if test="item.eventType != null and item.eventType != ''">
                event_type = #{item.eventType,jdbcType=VARCHAR},
            </if>
            <if test="item.eventFrequency != null and item.eventFrequency != ''">
                event_frequency = #{item.eventFrequency,jdbcType=VARCHAR},
            </if>
            <if test="item.triggerMethod != null and item.triggerMethod != ''">
                trigger_method = #{item.triggerMethod,jdbcType=VARCHAR},
            </if>
            <if test="item.operationStatus != null and item.operationStatus != ''">
                operation_status = #{item.operationStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.operator != null and item.operator != ''">
                operator = #{item.operator,jdbcType=VARCHAR},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sys_event_log where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sys_event_log where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
