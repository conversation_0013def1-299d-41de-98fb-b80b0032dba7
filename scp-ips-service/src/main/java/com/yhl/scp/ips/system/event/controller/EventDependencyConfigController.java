package com.yhl.scp.ips.system.event.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.system.event.dto.EventDependencyConfigDTO;
import com.yhl.scp.ips.system.event.service.EventDependencyConfigService;
import com.yhl.scp.ips.system.event.vo.EventDependencyConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>EventDependencyConfigController</code>
 * <p>
 * 事件依赖配置表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
@Slf4j
@Api(tags = "事件依赖配置表控制器")
@RestController
@RequestMapping("eventDependencyConfig")
public class EventDependencyConfigController extends BaseController {

    @Resource
    private EventDependencyConfigService eventDependencyConfigService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<EventDependencyConfigVO>> page() {
        List<EventDependencyConfigVO> eventDependencyConfigList = eventDependencyConfigService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<EventDependencyConfigVO> pageInfo = new PageInfo<>(eventDependencyConfigList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody EventDependencyConfigDTO eventDependencyConfigDTO) {
        return eventDependencyConfigService.doCreate(eventDependencyConfigDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody EventDependencyConfigDTO eventDependencyConfigDTO) {
        return eventDependencyConfigService.doUpdate(eventDependencyConfigDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        eventDependencyConfigService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<EventDependencyConfigVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, eventDependencyConfigService.selectByPrimaryKey(id));
    }

}
