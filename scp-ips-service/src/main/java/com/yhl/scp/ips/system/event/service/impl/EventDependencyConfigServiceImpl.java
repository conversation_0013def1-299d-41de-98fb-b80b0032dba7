package com.yhl.scp.ips.system.event.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.ips.system.event.convertor.EventDependencyConfigConvertor;
import com.yhl.scp.ips.system.event.domain.entity.EventDependencyConfigDO;
import com.yhl.scp.ips.system.event.domain.service.EventDependencyConfigDomainService;
import com.yhl.scp.ips.system.event.dto.EventDependencyConfigDTO;
import com.yhl.scp.ips.system.event.infrastructure.dao.EventDependencyConfigDao;
import com.yhl.scp.ips.system.event.infrastructure.po.EventDependencyConfigPO;
import com.yhl.scp.ips.system.event.service.EventDependencyConfigService;
import com.yhl.scp.ips.system.event.vo.EventDependencyConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>EventDependencyConfigServiceImpl</code>
 * <p>
 * 事件依赖配置表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:11
 */
@Slf4j
@Service
public class EventDependencyConfigServiceImpl extends AbstractService implements EventDependencyConfigService {

    @Resource
    private EventDependencyConfigDao eventDependencyConfigDao;

    @Resource
    private EventDependencyConfigDomainService eventDependencyConfigDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(EventDependencyConfigDTO eventDependencyConfigDTO) {
        // 0.数据转换
        EventDependencyConfigDO eventDependencyConfigDO = EventDependencyConfigConvertor.INSTANCE.dto2Do(eventDependencyConfigDTO);
        EventDependencyConfigPO eventDependencyConfigPO = EventDependencyConfigConvertor.INSTANCE.dto2Po(eventDependencyConfigDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        eventDependencyConfigDomainService.validation(eventDependencyConfigDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(eventDependencyConfigPO);
        eventDependencyConfigDao.insertWithPrimaryKey(eventDependencyConfigPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(EventDependencyConfigDTO eventDependencyConfigDTO) {
        // 0.数据转换
        EventDependencyConfigDO eventDependencyConfigDO = EventDependencyConfigConvertor.INSTANCE.dto2Do(eventDependencyConfigDTO);
        EventDependencyConfigPO eventDependencyConfigPO = EventDependencyConfigConvertor.INSTANCE.dto2Po(eventDependencyConfigDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        eventDependencyConfigDomainService.validation(eventDependencyConfigDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(eventDependencyConfigPO);
        eventDependencyConfigDao.update(eventDependencyConfigPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<EventDependencyConfigDTO> list) {
        List<EventDependencyConfigPO> newList = EventDependencyConfigConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        eventDependencyConfigDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<EventDependencyConfigDTO> list) {
        List<EventDependencyConfigPO> newList = EventDependencyConfigConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        eventDependencyConfigDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return eventDependencyConfigDao.deleteBatch(idList);
        }
        return eventDependencyConfigDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public EventDependencyConfigVO selectByPrimaryKey(String id) {
        EventDependencyConfigPO po = eventDependencyConfigDao.selectByPrimaryKey(id);
        return EventDependencyConfigConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sys_event_dependency_config")
    public List<EventDependencyConfigVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sys_event_dependency_config")
    public List<EventDependencyConfigVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<EventDependencyConfigVO> dataList = eventDependencyConfigDao.selectByCondition(sortParam, queryCriteriaParam);
        EventDependencyConfigServiceImpl target = springBeanUtils.getBean(EventDependencyConfigServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<EventDependencyConfigVO> selectByParams(Map<String, Object> params) {
        List<EventDependencyConfigPO> list = eventDependencyConfigDao.selectByParams(params);
        return EventDependencyConfigConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<EventDependencyConfigVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.EVENT_DEPENDENCY_CONFIG.getCode();
    }

    @Override
    public List<EventDependencyConfigVO> invocation(List<EventDependencyConfigVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
