package com.yhl.scp.ips.system.message.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.system.message.infrastructure.po.MessagePO;
import com.yhl.scp.ips.system.message.vo.MessageVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>MessageDao</code>
 * <p>
 * 消息表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:59:59
 */
public interface MessageDao extends BaseDao<MessagePO, MessageVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link MessageVO}
     */
    List<MessageVO> selectVOByParams(@Param("params") Map<String, Object> params);

}
