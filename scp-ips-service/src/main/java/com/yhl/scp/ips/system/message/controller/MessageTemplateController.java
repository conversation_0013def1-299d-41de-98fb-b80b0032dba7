package com.yhl.scp.ips.system.message.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.system.message.dto.MessageTemplateDTO;
import com.yhl.scp.ips.system.message.service.MessageTemplateService;
import com.yhl.scp.ips.system.message.vo.MessageTemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MessageTemplateController</code>
 * <p>
 * 消息模板表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:03
 */
@Slf4j
@Api(tags = "消息模板表控制器")
@RestController
@RequestMapping("messageTemplate")
public class MessageTemplateController extends BaseController {

    @Resource
    private MessageTemplateService messageTemplateService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MessageTemplateVO>> page() {
        List<MessageTemplateVO> messageTemplateList = messageTemplateService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MessageTemplateVO> pageInfo = new PageInfo<>(messageTemplateList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MessageTemplateDTO messageTemplateDTO) {
        return messageTemplateService.doCreate(messageTemplateDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MessageTemplateDTO messageTemplateDTO) {
        return messageTemplateService.doUpdate(messageTemplateDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        messageTemplateService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MessageTemplateVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, messageTemplateService.selectByPrimaryKey(id));
    }

}
