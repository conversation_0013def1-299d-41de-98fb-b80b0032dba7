package com.yhl.scp.ips.system.message.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MessagePO</code>
 * <p>
 * 消息表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 17:00:01
 */
public class MessagePO extends BasePO implements Serializable {

    private static final long serialVersionUID = -51326894158507325L;

    /**
     * 所属场景
     */
    private String scenario;
    /**
     * 事件ID
     */
    private String eventId;
    /**
     * 待办ID
     */
    private String todoId;
    /**
     * 消息大类
     */
    private String messageCategory;
    /**
     * 消息类型
     */
    private String messageType;
    /**
     * 消息级别
     */
    private String messageLevel;
    /**
     * 消息渠道
     */
    private String messageChannel;
    /**
     * 消息主题
     */
    private String messageSubject;
    /**
     * 消息内容
     */
    private String messageContent;
    /**
     * 消息链接
     */
    private String messageLink;
    /**
     * 发送人
     */
    private String sender;
    /**
     * 接收类型
     */
    private String receiverType;
    /**
     * 接收人列表
     */
    private String receivers;
    /**
     * 抄送人列表
     */
    private String ccReceivers;
    /**
     * 是否已读
     */
    private String readFlag;
    /**
     * 处理状态
     */
    private String processStatus;
    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 版本值
     */
    private Integer versionValue;

    public String getScenario() {
        return scenario;
    }

    public void setScenario(String scenario) {
        this.scenario = scenario;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getTodoId() {
        return todoId;
    }

    public void setTodoId(String todoId) {
        this.todoId = todoId;
    }

    public String getMessageCategory() {
        return messageCategory;
    }

    public void setMessageCategory(String messageCategory) {
        this.messageCategory = messageCategory;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getMessageLevel() {
        return messageLevel;
    }

    public void setMessageLevel(String messageLevel) {
        this.messageLevel = messageLevel;
    }

    public String getMessageChannel() {
        return messageChannel;
    }

    public void setMessageChannel(String messageChannel) {
        this.messageChannel = messageChannel;
    }

    public String getMessageSubject() {
        return messageSubject;
    }

    public void setMessageSubject(String messageSubject) {
        this.messageSubject = messageSubject;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getMessageLink() {
        return messageLink;
    }

    public void setMessageLink(String messageLink) {
        this.messageLink = messageLink;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getReceiverType() {
        return receiverType;
    }

    public void setReceiverType(String receiverType) {
        this.receiverType = receiverType;
    }

    public String getReceivers() {
        return receivers;
    }

    public void setReceivers(String receivers) {
        this.receivers = receivers;
    }

    public String getCcReceivers() {
        return ccReceivers;
    }

    public void setCcReceivers(String ccReceivers) {
        this.ccReceivers = ccReceivers;
    }

    public String getReadFlag() {
        return readFlag;
    }

    public void setReadFlag(String readFlag) {
        this.readFlag = readFlag;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
