package com.yhl.scp.ips.system.event.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;

/**
 * <code>EventDefinitionPO</code>
 * <p>
 * 事件定义表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 20:02:08
 */
public class EventDefinitionPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -33674438792053912L;

    /**
     * 所属租户
     */
    private String tenantId;
    /**
     * 所属模块
     */
    private String moduleCode;
    /**
     * 事件代码
     */
    private String eventCode;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 事件类型
     */
    private String eventType;
    /**
     * 事件频率
     */
    private String eventFrequency;
    /**
     * 是否虚拟
     */
    private String virtualFlag;
    /**
     * 触发方式
     */
    private String triggerMethods;
    /**
     * 消息类型
     */
    private String messageType;
    /**
     * 接收类型
     */
    private String receiverType;
    /**
     * 接收人列表
     */
    private String receivers;
    /**
     * 抄送人列表
     */
    private String ccReceivers;
    /**
     * Java代理
     */
    private String javaDelegator;
    /**
     * 消息级别
     */
    private String messageLevel;
    /**
     * 升级级别
     */
    private Integer upgradeLevel;
    /**
     * 版本值
     */
    private Integer versionValue;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventFrequency() {
        return eventFrequency;
    }

    public void setEventFrequency(String eventFrequency) {
        this.eventFrequency = eventFrequency;
    }

    public String getVirtualFlag() {
        return virtualFlag;
    }

    public void setVirtualFlag(String virtualFlag) {
        this.virtualFlag = virtualFlag;
    }

    public String getTriggerMethods() {
        return triggerMethods;
    }

    public void setTriggerMethods(String triggerMethods) {
        this.triggerMethods = triggerMethods;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getReceiverType() {
        return receiverType;
    }

    public void setReceiverType(String receiverType) {
        this.receiverType = receiverType;
    }

    public String getReceivers() {
        return receivers;
    }

    public void setReceivers(String receivers) {
        this.receivers = receivers;
    }

    public String getCcReceivers() {
        return ccReceivers;
    }

    public void setCcReceivers(String ccReceivers) {
        this.ccReceivers = ccReceivers;
    }

    public String getJavaDelegator() {
        return javaDelegator;
    }

    public void setJavaDelegator(String javaDelegator) {
        this.javaDelegator = javaDelegator;
    }

    public String getMessageLevel() {
        return messageLevel;
    }

    public void setMessageLevel(String messageLevel) {
        this.messageLevel = messageLevel;
    }

    public Integer getUpgradeLevel() {
        return upgradeLevel;
    }

    public void setUpgradeLevel(Integer upgradeLevel) {
        this.upgradeLevel = upgradeLevel;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
