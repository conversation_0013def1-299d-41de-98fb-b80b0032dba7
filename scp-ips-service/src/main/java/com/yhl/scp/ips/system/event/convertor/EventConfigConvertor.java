package com.yhl.scp.ips.system.event.convertor;

import com.yhl.scp.ips.system.event.domain.entity.EventConfigDO;
import com.yhl.scp.ips.system.event.dto.EventConfigDTO;
import com.yhl.scp.ips.system.event.infrastructure.po.EventConfigPO;
import com.yhl.scp.ips.system.event.vo.EventConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>EventConfigConvertor</code>
 * <p>
 * 事件配置表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 16:55:10
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EventConfigConvertor {

    EventConfigConvertor INSTANCE = Mappers.getMapper(EventConfigConvertor.class);

    EventConfigDO dto2Do(EventConfigDTO obj);

    List<EventConfigDO> dto2Dos(List<EventConfigDTO> list);

    EventConfigDTO do2Dto(EventConfigDO obj);

    List<EventConfigDTO> do2Dtos(List<EventConfigDO> list);

    EventConfigDTO vo2Dto(EventConfigVO obj);

    List<EventConfigDTO> vo2Dtos(List<EventConfigVO> list);

    EventConfigVO po2Vo(EventConfigPO obj);

    List<EventConfigVO> po2Vos(List<EventConfigPO> list);

    EventConfigPO dto2Po(EventConfigDTO obj);

    List<EventConfigPO> dto2Pos(List<EventConfigDTO> obj);

    EventConfigVO do2Vo(EventConfigDO obj);

    EventConfigPO do2Po(EventConfigDO obj);

    EventConfigDO po2Do(EventConfigPO obj);

}
