package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.enmus.MesApiReqCodeEnum;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <code>MesFYEInventoryBatchDetailHandler</code>
 * <p>
 *FYE MES库存批次明细同步
 * 同步方式：全量
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 11:23:52
 */
@Component
@Slf4j
public class MesFYEInventoryBatchDetailHandler extends SyncDataHandler<List<MesInventoryBatchDetail>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private DfpFeign dfpFeign;

    @Override
    protected List<MesInventoryBatchDetail> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES获取外部库存批次明细数据为空");
            return null;
        }
        List<MesInventoryBatchDetail> mesInventoryBatchDetail = JSONArray.parseArray(body, MesInventoryBatchDetail.class);
        return mesInventoryBatchDetail;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesInventoryBatchDetail> mesInventoryBatchDetails) {
        String plantIds = (String) params.get("plantId");
        StopWatch handleWatch = new StopWatch("handleBody数据处理-库存点" + plantIds);

        long handleStartTime = System.currentTimeMillis();

        handleWatch.start("参数校验");
        if (CollectionUtils.isEmpty(mesInventoryBatchDetails)) {
            log.error("库存批次明细为空");
            handleWatch.stop();
            log.info("库存点{}: {}", plantIds, handleWatch.prettyPrint(TimeUnit.SECONDS));
            return null;
        }
        handleWatch.stop();

        handleWatch.start("解析scenario参数");
        String scenario = (String) params.get("scenario");

        List<String> orgIds = Arrays.asList(plantIds.split(","));
        handleWatch.stop();
        handleWatch.start("调用DFP服务");
        dfpFeign.handleInventoryBatchDetail(scenario, mesInventoryBatchDetails, orgIds);
        handleWatch.stop();

        // 统计总耗时
        long handleEndTime = System.currentTimeMillis();
        double totalHandleTimeSeconds = (handleEndTime - handleStartTime) / 1000.0;

        log.info("库存点{}: 数据处理完成，总处理耗时：{}秒", plantIds, totalHandleTimeSeconds);
        log.info("库存点{}: {}", plantIds, handleWatch.prettyPrint(TimeUnit.SECONDS));

        return null;
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES库存批次:{},{}", apiConfigVO, params);
        }

        long methodStartTime = System.currentTimeMillis();
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);

        Object plantId = params.get("plantId");
        Object isb = params.get("isb");

        StopWatch mainWatch = new StopWatch("MES库存批次明细接口同步-库存点" + plantId);
        ExecutorService executor = null;

        try {
            // 1. 获取Token
            mainWatch.start("请求MESToken");
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            mainWatch.stop();

            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();
            String url = apiUri + apiParams;

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);

            String reqCode = MesApiReqCodeEnum.FY_PKN_AND_SGL_FOR_BPIM.getCode();
            int pageSize = Objects.isNull(apiConfigVO.getOffsetSize()) ? 10000 : apiConfigVO.getOffsetSize();

            // 2. 请求第一页
            Map<String, Object> firstParam = new HashMap<>();
            firstParam.put("reqCode", reqCode);
            firstParam.put("plantId", plantId);
            firstParam.put("currentPage", 1);
            firstParam.put("pageSize", pageSize);
            List<HashMap<Object, Object>> conditions = new ArrayList<>();
            HashMap<Object, Object> conditionMap = MapUtil.newHashMap();
            if(Objects.equals(isb,"N")){
                conditionMap.put("cKey","isb");
                conditionMap.put("cValue","N");
                conditions.add(conditionMap);
                firstParam.put("conditions",conditions);
            }

            ExtApiLogDTO firstLog = extApiLogService.createLog(apiConfigVO, params, mainLog, httpHeaders.toString(), JSONObject.toJSONString(firstParam));
            HttpEntity<String> httpEntity = new HttpEntity<>(JSONObject.toJSONString(firstParam), httpHeaders);
            mainWatch.start("请求MES第一页数据");
            ResponseEntity<String> firstResponse = restTemplate.postForEntity(url, httpEntity, String.class);
            mainWatch.stop();
            mainWatch.start("解析MES第一页数据");
            Assert.isTrue(HttpStatus.OK.value() == firstResponse.getStatusCodeValue(), "MES请求失败");

            MesResponse firstParsed = JSON.parseObject(firstResponse.getBody(), MesResponse.class);
            if (firstParsed == null || firstParsed.getData() == null) {
                log.error("库存点{}: MES返回数据格式异常", plantId);
                throw new BusinessException("MES返回数据格式异常");
            }

            MesResponseData firstData = firstParsed.getData();
            List<Object> firstPageData = firstData.getMessage();

            List<Object> result = Collections.synchronizedList(new ArrayList<>(
                firstPageData != null ? firstPageData : new ArrayList<>()));
            mainWatch.stop();

            int totalRecords = firstData.getTotal();
            int totalPages = Math.max((int) Math.ceil((double) totalRecords / pageSize), 1); // 确保至少为1

            log.info("库存点{}: 总记录数：{}，分页大小：{}，总页数：{}", plantId, totalRecords, pageSize, totalPages);

            extApiLogService.updateResponse(firstLog, null, firstData.getMessage().size(),DcpConstants.TASKS_STATUS_SUCCESS);

            int remainingPages = Math.max(totalPages - 1, 0); // 剩余需要处理的页数
            List<CompletableFuture<Void>> futures = new ArrayList<>(Math.max(remainingPages, 0));

            if (remainingPages > 0) {
                int optimalThreads = Math.min(remainingPages, 5); // 最多5个线程
                executor = Executors.newFixedThreadPool(optimalThreads);
                log.info("库存点{}: 需要并发处理{}页数据，创建{}个线程", plantId, remainingPages, optimalThreads);
            } else {
                log.info("库存点{}: 只有1页数据，无需创建线程池", plantId);
            }

            final String finalReqCode = reqCode;
            final Object finalPlantId = plantId;
            final int finalPageSize = pageSize;

            for (int i = 2; i <= totalPages; i++) {
                final int pageIndex = i;
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    StopWatch pageWatch = new StopWatch("分页请求第" + pageIndex + "页-库存点" + finalPlantId);
                    Map<String, Object> pageParam = new HashMap<>(4);
                    pageParam.put("reqCode", finalReqCode);
                    pageParam.put("plantId", finalPlantId);
                    pageParam.put("currentPage", pageIndex);
                    pageParam.put("pageSize", finalPageSize);
                    pageParam.put("conditions",conditions);

                    String pageParamJson = JSONObject.toJSONString(pageParam);
                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog, httpHeaders.toString(), pageParamJson);
                    ResponseEntity<String> pageResp=null;
                    try {

                        HttpEntity<String> pageEntity = new HttpEntity<>(pageParamJson, httpHeaders);

                        pageWatch.start("请求MES第" + pageIndex + "页数据");
                        pageResp = restTemplate.postForEntity(url, pageEntity, String.class);
                        pageWatch.stop();
                        if (HttpStatus.OK.value() != pageResp.getStatusCodeValue()) {
                            extApiLogService.updateResponse(subLog, pageResp, null,DcpConstants.TASKS_STATUS_ERROR);
                            throw new BusinessException("分页请求失败：第" + pageIndex + "页");
                        }

                        pageWatch.start("解析MES第" + pageIndex + "页数据");
                        MesResponse pageResult = JSON.parseObject(pageResp.getBody(), MesResponse.class);

                        int pageDataSize = 0;
                        if (pageResult != null && pageResult.getData() != null) {
                            List<Object> pageList = pageResult.getData().getMessage();
                            if (pageList != null && !pageList.isEmpty()) {
                                result.addAll(pageList);
                                pageDataSize = pageList.size();
                            }
                        } else {
                            log.warn("库存点{}: 第{}页数据为空或格式异常", finalPlantId, pageIndex);
                        }
                        pageWatch.stop();

                        extApiLogService.updateResponse(subLog, null, pageDataSize,DcpConstants.TASKS_STATUS_SUCCESS);

                    } catch (Exception e) {
                        String errorResponseBody = (pageResp != null) ? pageResp.getBody() : "[无法获取响应体]";
                        log.error("库存点{}: 第{}页请求异常: {},响应报文:{}", plantId, pageIndex, e.getMessage(),errorResponseBody);
                        extApiLogService.updateResponse(subLog, pageResp, null, DcpConstants.TASKS_STATUS_ERROR);
                        throw e;
                    } finally {
                        if (pageWatch.isRunning()) pageWatch.stop();
                        log.info("库存点{}: {}", plantId, pageWatch.prettyPrint(TimeUnit.SECONDS));
                    }
                }, executor);
                futures.add(future);
            }

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            log.info("库存点{}: {}", plantId, mainWatch.prettyPrint(TimeUnit.SECONDS));

            long methodEndTime = System.currentTimeMillis();
            double totalTimeSeconds = (methodEndTime - methodStartTime) / 1000.0;
            log.info("库存点{}: 总数据量：{}，总耗时：{}秒", plantId, result.size(), totalTimeSeconds);
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_SUCCESS);

            return JSONObject.toJSONString(result);

        } catch (Exception e) {
            long errorTime = System.currentTimeMillis();
            double errorTimeSeconds = (errorTime - methodStartTime) / 1000.0;
            log.error("库存点{}: 调用MES并发接口异常，已执行耗时：{}秒，错误：{}", plantId, errorTimeSeconds, e.getMessage());
            log.info("库存点{}: {}", plantId, mainWatch.prettyPrint(TimeUnit.SECONDS));
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        } finally {
            if (executor != null) {
                executor.shutdown();
                try {
                    if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                        log.warn("线程池未能在30秒内正常关闭，强制关闭");
                        executor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    log.warn("等待线程池关闭时被中断");
                    executor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.FYE_MES_INVENTORY_BATCH_DETAIL.getCode());

    }
}
