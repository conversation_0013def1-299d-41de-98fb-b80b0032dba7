package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesRealTimeInventory;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.enmus.MesApiReqCodeEnum;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <code>MesRealTimeInventoryHandler</code>
 * <p>
 *MES实时库存批次查询
 * 同步方式：全量
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 11:23:52
 */
@Component
@Slf4j
public class MesRealTimeInventoryHandler extends SyncDataHandler<List<MesRealTimeInventory>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private DfpFeign dfpFeign;

    @Override
    protected List<MesRealTimeInventory> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES获取外部库存批次明细数据为空");
            return null;
        }
        List<MesRealTimeInventory> mesInventoryBatchDetail = JSONArray.parseArray(body, MesRealTimeInventory.class);
        return mesInventoryBatchDetail;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesRealTimeInventory> mesInventoryBatchDetails) {
        String plantIds = (String) params.get("plantId");
        StopWatch handleWatch = new StopWatch("handleBody数据处理-库存点" + plantIds);

        long handleStartTime = System.currentTimeMillis();

        handleWatch.start("参数校验");
        if (CollectionUtils.isEmpty(mesInventoryBatchDetails)) {
            log.error("库存批次明细为空");
            handleWatch.stop();
            log.info("库存点{}: {}", plantIds, handleWatch.prettyPrint(TimeUnit.SECONDS));
            return null;
        }
        handleWatch.stop();

        handleWatch.start("解析scenario参数");
        String scenario = (String) params.get("scenario");
        String syncUser = (String) params.get("syncUser");
        List<String> orgIds = Arrays.asList(plantIds.split(","));
        handleWatch.stop();

        handleWatch.start("调用DFP服务");
        dfpFeign.handleRealTimeInventoryBatchDetail(scenario, mesInventoryBatchDetails, orgIds,syncUser);
        handleWatch.stop();

        // 统计总耗时
        long handleEndTime = System.currentTimeMillis();
        double totalHandleTimeSeconds = (handleEndTime - handleStartTime) / 1000.0;

        log.info("库存点{}: 数据处理完成，总处理耗时：{}秒", plantIds, totalHandleTimeSeconds);
        log.info("库存点{}: {}", plantIds, handleWatch.prettyPrint(TimeUnit.SECONDS));

        return null;
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES实时库存:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();

            String url = apiUri + apiParams;
            String reqCode = MesApiReqCodeEnum.FY_INV_ONHAND_FOR_BPIM.getCode();

            String lastUpdateDateStr = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDateStr, DateUtils.COMMON_DATE_STR3);
            Date currentDate = DateUtils.truncateTimeOfDate(new Date());
            if (log.isInfoEnabled()) {
                log.info("apiUri={},url={},reqCode={},lastUpdateDateStr={},currentDate={}", apiUri,url, reqCode,
                        lastUpdateDateStr, currentDate);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            int period = (int) DateUtil.between(calculateDate, currentDate, DateUnit.DAY);
            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            int pageSize = Objects.isNull(apiConfigVO.getOffsetSize())?10000:apiConfigVO.getOffsetSize();
            List<Object> result = Lists.newArrayList();
            Object plantId = params.get("plantId");
            Object colsIn = params.get("colsIn");

            for (int i = 0; i < count; i++) {
                int currentPage = 1;
                boolean hasNextSize = true;
                while (hasNextSize) {
                    HashMap<Object, Object> paramMap = MapUtil.newHashMap();
                    paramMap.put("currentPage", currentPage);
                    paramMap.put("plantId", plantId);
                    paramMap.put("currentPage", currentPage);
                    paramMap.put("pageSize", pageSize);
                    paramMap.put("colsIn", colsIn);
                    List<HashMap<Object, Object>> conditions = new ArrayList<>();

                    HashMap<Object, Object> conditionMap = MapUtil.newHashMap();
                    conditionMap.put("cKey","isb");
                    conditionMap.put("cValue","N");
                    conditions.add(conditionMap);
                    paramMap.put("conditions",conditions);
                    // 创建子日志
                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                            httpHeaders.toString(),
                            JSONObject.toJSONString(paramMap));
                    HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(paramMap), httpHeaders);
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                    int statusCodeValue = responseEntity.getStatusCodeValue();
                    if (HttpStatus.OK.value() != statusCodeValue) {
                        extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    }
                    Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MES同步实时库存失败！");
                    String body = responseEntity.getBody();
                    log.info("请求MES实时库存完成,返回数据:{}!", body);
                    MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                    MesResponseData data = Objects.requireNonNull(mesResponse).getData();
                    extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                    if (Objects.nonNull(data)) {
                        result.addAll(data.getMessage());
                        if (data.getTotalPage() <= data.getCurrentPage()) {
                            hasNextSize = false;
                        } else {
                            currentPage++;
                        }
                    }
                }

            }
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, result.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            return JSON.toJSONString(result);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.MES_REALTIME_INVENTORY.getCode());

    }
}
