package com.yhl.scp.dcp.openapi.srm;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.dcp.enmus.ApiTypeEnum;
import com.yhl.scp.dcp.oauth.BpimResponse;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedFeedbackDTO;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedFeedbackDetailsDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * <code>MaterialNeedReceiveController</code>
 * <p>
 * 要货计划（接受供应商要货反馈）控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-27 14:23:24
 */
@Api(tags = "要货计划（接受供应商要货反馈）控制器")
@RestController
@RequestMapping("/openapi/srm/materialPlanNeedReceive")
@Slf4j
public class SrmNeedReceive {

    @Resource
    private MrpFeign mrpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @ApiOperation("反馈")
    @PostMapping("feedback")
    public BpimResponse feedback(@RequestParam(value = "stockPointCode") String stockPointCode,
                                 @RequestBody List<MaterialPlanNeedFeedbackDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("要货计划反馈请求参数为空");
            return BpimResponse.builder()
                    .status("200")
                    .statusCodeValue(200)
                    .statusCode("OK")
                    .message("OK")
                    .payload(ImmutableMap.of(
                            "success", false,
                            "code", "error.system",
                            "message", "请求参数不能为空",
                            "type", "error",
                            "responseData", ""))
                    .build();
        }

        log.info("feedback payload:{}", JSON.toJSONString(list));

        List<ScenarioBusinessRangeVO> scenarioBusinessRangeVOList = ipsNewFeign.selectScenarioBusinessByParams(
                ImmutableMap.of("rangeData", stockPointCode));
        if (CollectionUtils.isEmpty(scenarioBusinessRangeVOList)) {
            log.error("{}未找到数据源", stockPointCode);
            return BpimResponse.builder()
                    .status("200")
                    .statusCodeValue(200)
                    .statusCode("OK")
                    .message("OK")
                    .payload(ImmutableMap.of(
                            "success", false,
                            "code", "error.system",
                            "message", "未找到数据源",
                            "type", "error",
                            "responseData", ""))
                    .build();
        }
        String scenario = scenarioBusinessRangeVOList.get(0).getScenario();
        log.info("当前反馈所用数据源{}", scenario);

        try {
            BaseResponse<Void> response = mrpFeign.receiveData(scenario, list);

            if (Boolean.TRUE.equals(response.getSuccess())) {
                // 记录下发日志
                MaterialPlanNeedFeedbackDetailsDTO materialPlanNeedFeedbackDTO = new MaterialPlanNeedFeedbackDetailsDTO();
                materialPlanNeedFeedbackDTO.setStatus(Boolean.TRUE.toString());
                materialPlanNeedFeedbackDTO.setInterfaceParameters(JSON.toJSONString(list));
                materialPlanNeedFeedbackDTO.setExceptionReason(ApiTypeEnum.SUCCESS.getDesc());
                mrpFeign.addMaterialPlanNeedFeedbackDetails(scenario, Collections.singletonList(materialPlanNeedFeedbackDTO));

                // 成功情况下返回标准格式
                return BpimResponse.builder()
                        .status("200")
                        .statusCode("OK")
                        .message("OK")
                        .payload(ImmutableMap.of("success", true, "code", "OK", "message", "OK", "type", ApiTypeEnum.SUCCESS.getCode(), "responseData", ""))
                        .build();
            } else {
                // 记录下发日志
                MaterialPlanNeedFeedbackDetailsDTO materialPlanNeedFeedbackDTO = new MaterialPlanNeedFeedbackDetailsDTO();
                materialPlanNeedFeedbackDTO.setStatus(Boolean.FALSE.toString());
                materialPlanNeedFeedbackDTO.setInterfaceParameters(JSON.toJSONString(list));
                materialPlanNeedFeedbackDTO.setExceptionReason(ApiTypeEnum.RESPONSE_ERROR.getDesc() + "，日志{" + response.getMsg() + "}");
                mrpFeign.addMaterialPlanNeedFeedbackDetails(scenario, Collections.singletonList(materialPlanNeedFeedbackDTO));

                // 失败情况下返回错误信息
                return BpimResponse.builder()
                        .status(response.getCode())
                        .message(response.getMsg())
                        .payload(ImmutableMap.of("success", false, "code", response.getCode(), "message", response.getMsg(), "type", ApiTypeEnum.RESPONSE_ERROR.getCode(), "responseData", ""))
                        .build();
            }
        } catch (Exception e) {
            // 记录下发日志
            MaterialPlanNeedFeedbackDetailsDTO materialPlanNeedFeedbackDTO = new MaterialPlanNeedFeedbackDetailsDTO();
            materialPlanNeedFeedbackDTO.setStatus(Boolean.FALSE.toString());
            materialPlanNeedFeedbackDTO.setInterfaceParameters(JSON.toJSONString(list));
            materialPlanNeedFeedbackDTO.setExceptionReason(ApiTypeEnum.CALL_ERROR.getDesc() + "，日志{" + e.getMessage() + "}");
            mrpFeign.addMaterialPlanNeedFeedbackDetails(scenario, Collections.singletonList(materialPlanNeedFeedbackDTO));
            log.error("处理请求失败", e);

            // 异常情况下的返回
            return BpimResponse.builder()
                    .body(null)
                    .status("200")
                    .statusCode("OK")
                    .message("OK")
                    .payload(ImmutableMap.of("success", false, "code", "error.system", "message", e.getMessage(), "type", ApiTypeEnum.CALL_ERROR.getCode(), "responseData", ""))
                    .build();
        }
    }

}
