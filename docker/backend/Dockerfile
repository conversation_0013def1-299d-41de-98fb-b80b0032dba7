FROM harbor.fuyaogroup.com/bpim/basejdk:4.0
MAINTAINER feiyuming
ARG JAVA_OPTS
ARG SPRING_PROFILE
ENV J_OPTS=${JAVA_OPTS} \
    S_PROFILE=${SPRING_PROFILE}
ADD *.jar /home/<USER>/app.jar
RUN mkdir -p /home/<USER>/logs
CMD ["sh", "-c", "java $J_OPTS -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:InitiatingHeapOccupancyPercent=45 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:/home/<USER>/logs/gc-%t.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dfile.encoding=utf-8 -Duser.timezone=Asia/Shanghai -Dspring.profiles.active=$S_PROFILE -jar /home/<USER>/app.jar"]