FROM ubuntu:20.04
MAINTAINER feiyuming
ENV JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y curl bash telnet tini \
    && apt-get install -y openjdk-8-jdk \
    && apt-get install -y python3 python3-pip \
    && apt-get install -y python3-dev libffi-dev libxml2-dev libxslt1-dev zlib1g-dev \
    && rm -f /etc/localtime \
    && ln -sv /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && mkdir -p /usr/local/aps/workspace \
    && mkdir -p /usr/local/aps/app \
    && mkdir -p /usr/local/mps/workspace \
    && mkdir -p /usr/local/mps/app \
    && mkdir -p /usr/local/dfp/workspace \
    && mkdir -p /usr/local/dfp/app
VOLUME /tmp
WORKDIR /home/<USER>
COPY --from=hengyunabc/arthas:latest /opt/arthas opt/arthas