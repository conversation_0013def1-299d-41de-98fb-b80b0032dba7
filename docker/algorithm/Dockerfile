FROM harbor.fuyaogroup.com/bpim/cplex-bpim:fuyao
MAINTAINER feiyuming
ARG JAVA_OPTS
ARG SPRING_PROFILE
ENV J_OPTS=${JAVA_OPTS} \
    S_PROFILE=${SPRING_PROFILE}
ADD dfp/ /usr/local/dfp/app/
ADD mps/ /usr/local/mps/app/
ADD ams/APS_*.sh /usr/local/aps/app/APS_INSTALL.sh
ADD *.jar app.jar
RUN bash /usr/local/aps/app/APS_INSTALL.sh --skip-license \
    && chmod 777 /usr/local/aps/app/APS \
    && chmod 777 /usr/local/mps/app/launch_mps \
    && echo "source /etc/profile" >> ~/.bashrc
RUN mkdir -p /home/<USER>/logs
CMD ["sh", "-c", "java $J_OPTS -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:InitiatingHeapOccupancyPercent=45 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:/home/<USER>/logs/gc-%t.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dfile.encoding=utf-8 -Duser.timezone=Asia/Shanghai -Dspring.profiles.active=$S_PROFILE -jar app.jar"]