from data_reader import DataReader
from data_processor import DataProcessor
from general_algo import GeneralAlgorithm
from logging_config import setup_logging
import pandas as pd
from datetime import datetime
from collections import defaultdict
import subprocess
import os


class Context:
    def __init__(self):
        self.logger = setup_logging()

    def out_pid(self):
        pid = os.getpid()
        col = ['pid']
        record_lt = [{'pid': str(pid)}]
        df = pd.DataFrame(record_lt, columns=col)
        df.to_csv('pid.csv', index=False)

    def out_status(
            self
            , out_index
    ):
        col = ['status']
        record_lt = [{'status': str(out_index)}]
        df = pd.DataFrame(record_lt, columns=col)
        df.to_csv('status.csv', index=False)

    def log_run_info(self):
        code_dir = os.path.dirname(os.path.realpath(__file__))
        try:
            commit_info = subprocess.check_output(
                "git rev-parse HEAD", shell=True, cwd=code_dir
            )
            with open(code_dir + "/commit_info.txt", 'w') as f:
                f.write(commit_info.decode('utf-8'))

        except Exception as e:
            if os.path.exists(code_dir + "/commit_info.txt"):
                with open(code_dir + "/commit_info.txt", 'r') as f:
                    commit_info = f.readline()
            else:
                commit_info = "commit info not found"
        work_dir = os.getcwd()
        self.logger.info("work_dir : {}".format(work_dir))
        self.logger.info("commit info : {}".format(commit_info))

    def run(self):    
        # Step 1: Read the data
        self.log_run_info()
        self.reader = DataReader(
            'historyOrder.csv',
            'passengerCarMarketInfo.csv',
            'projectMapping.csv',
            'oemProjectInfo.csv',
            'oemStockInfo.csv',
            'marketInfo.csv',
            'policyInfo.csv',
            'oemForecast.csv',
            'competitionGroup.csv',
            'oemVehicleModel.csv'
        )
        self.reader.read_data()
        # Step 2: Process the data
        self.processor = DataProcessor(self.reader)
        history_sales, coming_month_sales_history, coming_month_sales_df = self.processor.sale_data_process()
        df_market_feature_data, df_oem_stock_feature_data = self.processor.other_data_process()
        (
            df_market_class_feature_data,
            df_policy_class_feature_data,
            df_oem_project_feature_data,
            df_item_group_feature_data,
            df_oem_project_start_feature_data
        ) = self.processor.other_class_process()
        df_time_data = self.processor.time_process()
        df_oem_data, df_project_data = self.processor.oem_product_process()
        df_oem_forecast_data = self.processor.oem_forecast_process()
        df_oem_project_feature_future_data = self.processor.oem_project_info_future_data_process()
        df_oem_forecast_future_data = self.processor.oem_forecast_future_process()
        df_time_future_data = self.processor.time_future_process()
        df_item_group_feature_future_data = self.processor.item_group_info_future_data_process()
        df_oem_project_start_feature_future_data = self.processor.oem_project_start_info_future_data_process()

        # Step 3: Model forecasting
        history_sales = history_sales
        other_info_time_series = []
        other_info_time_series.append(df_oem_data)  # i = 0  主机厂信息
        other_info_time_series.append(df_project_data)  # i = 1  车型信息
        other_info_time_series.append(df_market_feature_data)  # i = 2  车型销量信息
        other_info_time_series.append(df_oem_stock_feature_data)  # i = 3  主机厂库存信息
        # other_info_time_series.append(df_market_class_feature_data)  # 市场信息 取消
        # other_info_time_series.append(df_policy_class_feature_data)  # 政策信息 取消
        other_info_time_series.append(df_oem_project_feature_data)  # i = 4  主机厂车型信息（XX阶段）
        other_info_time_series.append(df_time_data)  # i = 5  时间信息
        other_info_time_series.append(df_oem_forecast_data)  # i = 6  主机厂预测信息
        other_info_time_series.append(df_item_group_feature_data)  # i = 7  车型竞对信息
        other_info_time_series.append(df_oem_project_start_feature_data)  # i = 7  车型竞对信息
        forecast_periods = self.processor.forecast_periods  # range(1, 13)
        key_list = ['itemCode']
        other_need_list = [0, 0, 1, 1, 0, 0, -1, 0, 0]
        other_type_list = [0, 0, 1, 1, 0, -1, 0, 0, -1]

        future_feature_dict = dict()
        future_feature_dict[4] = df_oem_project_feature_future_data
        future_feature_dict[5] = df_time_future_data
        future_feature_dict[6] = df_oem_forecast_future_data
        future_feature_dict[7] = df_item_group_feature_future_data
        future_feature_dict[8] = df_oem_project_start_feature_future_data

        self.forecaster = GeneralAlgorithm(history_sales, forecast_periods
                                        , key_list, other_info_time_series
                                        , other_need_list)

        forecast_result = self.forecaster.forecast_xgb(
            history_sales,
            coming_month_sales_history,
            coming_month_sales_df,
            forecast_periods,
            key_list,
            other_info_time_series,
            other_need_list,
            other_type_list,
            future_feature_dict
        )
        # 输出结果
        # forecastResult.csv的输出
        forecast_result.loc[forecast_result['forecastQuantity'] < 0, 'forecastQuantity'] = 0
        forecast_result['forecastQuantity'] = forecast_result['forecastQuantity'].apply(round)
        last_date = self.processor.time_ser[-1]
        last_year, last_month = int(last_date[:4]), int(last_date[4:])
        last_datetime = datetime(last_year, last_month, 1)

        def to_forecast_date(period):
            return last_datetime + period * pd.offsets.MonthBegin(1)

        forecast_result['forecastDate'] = forecast_result['forecastPeriod'].apply(to_forecast_date)
        forecast_result.rename(columns={'forecastQuantity': 'forecastValue'}, inplace=True)

        # 后处理4：最终预测值应≤当前主机厂的预测数据值
        # 将当前主机厂的预测数据在curr_time转化为日期格式
        df_oem_forecast_future_data['curr_time'] = pd.to_datetime(df_oem_forecast_future_data['curr_time'], format='%Y%m')

        # 进行左连接，保留 forecast_result 中的所有数据
        df_combined = pd.merge(forecast_result, df_oem_forecast_future_data, left_on=['itemCode', 'forecastDate'],
                               right_on=['itemCode', 'curr_time'], how='left')

        df_combined['of6'] = df_combined['of6'].fillna(0.0)
        # 对于在主机厂的预测数据(df_oem_forecast_future_data)中的itemCode，比较相应预测日期的主机厂预测值和算法预测值，取较小的那个作为新的算法预测值

        def oem_forecast_replace(r):
            if r['forecastValue'] > r['of6']:
                return r['of6']

            # elif r['forecastValue'] < r['of6'] * 0.8 and abs(r['of6'] - r['forecastValue']) >= 1000:
            #     return r['of6'] * 0.8

            else:
                return r['forecastValue']

        forecast_result['newForecastValue'] = df_combined.apply(oem_forecast_replace, axis=1)

        # 后处理5：是否有明确的EOP时间，若有，则EOP时间后的预测值为0
        # 遍历每个物品
        for idx, row in df_oem_project_feature_data.iterrows():
            item_code = row['itemCode']
            # 找到退市阶段的月份
            stop_sale_months = row[row == '退市阶段'].index.tolist()
            # 如果找到了退市阶段
            if stop_sale_months:
                first_stop_sale_month = stop_sale_months[0]
                # 把 forecast_value 中与 item_code 对应，且 forecastDate >= first_stop_sale_month 的 newForecastValue 设为0
                forecast_result.loc[
                    (forecast_result['itemCode'] == item_code) &
                    (forecast_result['forecastDate'] >= pd.to_datetime(first_stop_sale_month + '01', format='%Y%m%d')),
                    'newForecastValue'
                ] = 0
        forecast_result['newForecastValue'] = forecast_result['newForecastValue'].apply(round)
        forecast_result[['itemCode', 'forecastDate', 'newForecastValue']].to_csv('forecastResult.csv', index=False)
        
        # forecastFactorAnalysis.csv的输出
        # 需根据特征名字做汇总
        # 此处衡量特征重要性的指标是weight，其他选择为gain or cover(gain也是比较常用的)
        feature_importance_dict = self.forecaster.model.get_score(importance_type="gain")
        feature_dict = {
            "passengerCarMarketInfo": ['of2shift1', 'of2shift2', 'of2shift3', 'of2ttm3', 'of2ttm6'],
            "oemStockInfo": ['of3shift1', 'of3shift2', 'of3shift3', 'of3ttm3', 'of3ttm6'],
            "oemProjectInfo": ['of4'],
            "oemForecast": ['of6'],
            "itemGroup": ['of7'],
            "historyOrder": ['shift1', 'shift2', 'shift3', 'ttm3', 'ttm6'],
            "otherFeature": ['of0', 'of1', 'of5']
        }
        cn_name_map = {
            "passengerCarMarketInfo": "乘用车市场信息",
            "oemStockInfo": "主机厂存货信息",
            "oemProjectInfo": "主机厂车型信息",
            "oemForecast": "主机厂预测",
            "historyOrder": "历史订单",
            "itemGroup": "竞对信息",
            "otherFeature": "其他特征",
        }
        factor_importance_dict = defaultdict(float)
        for idx, val in feature_dict.items():
            for _v in val:
                if _v in feature_importance_dict:
                    factor_importance_dict[cn_name_map[idx]] += feature_importance_dict[_v]

        df_forecast_factor_analysis = pd.DataFrame.from_dict(factor_importance_dict, orient='index', columns=['importanceValue'])
        df_forecast_factor_analysis.reset_index(inplace=True)
        df_forecast_factor_analysis.rename(columns={'index': "factorCode"}, inplace=True)
        df_forecast_factor_analysis.to_csv('forecastFactorAnalysis.csv', index=False)