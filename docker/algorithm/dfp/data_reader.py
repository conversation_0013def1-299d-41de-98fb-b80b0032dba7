import os
import pandas as pd
from logging_config import setup_logging

logger = setup_logging()


class DataReader:
    def __init__(self,
                 history_order_file_path,
                 passenger_car_market_info_file_path,
                 project_mapping_file_path,
                 oem_project_info_file_path,
                 oem_stock_info_file_path,
                 market_info_file_path,
                 policy_info_file_path,
                 oem_forecast_file_path,
                 competition_group_file_path,
                 oem_vehicle_model_file_path
                 ):
        data_input_path = "./"
        competition_group_path = "/usr/local/dfp/app/"
        self.history_order_file_path = data_input_path + history_order_file_path
        self.passenger_car_market_info_file_path = data_input_path + passenger_car_market_info_file_path
        self.project_mapping_file_path = data_input_path + project_mapping_file_path
        self.oem_project_info_file_path = data_input_path + oem_project_info_file_path
        self.oem_stock_info_file_path = data_input_path + oem_stock_info_file_path
        self.market_info_file_path = data_input_path + market_info_file_path
        self.policy_info_file_path = data_input_path + policy_info_file_path
        self.oem_forecast_file_path = data_input_path + oem_forecast_file_path
        self.competition_group_file_path = competition_group_path + competition_group_file_path
        self.oem_vehicle_model_file_path = data_input_path + oem_vehicle_model_file_path

    def read_csv_if_exists(self, file_path, **kwargs):
        encoding = kwargs.pop('encoding', 'utf-8')
        # 检查文件是否存在
        if not os.path.exists(file_path):
            # 记录日志或返回 None
            logger.info(f"File not found: {file_path}")
            return None
        # 尝试使用不同编码读取文件
        try:
            return pd.read_csv(file_path, encoding=encoding, **kwargs)
        except UnicodeDecodeError:
            try:
                return pd.read_csv(file_path, encoding='gbk', **kwargs)
            except UnicodeDecodeError:
                return pd.read_csv(file_path, encoding='latin1', **kwargs)

    def read_data(self):
        """读取所有数据文件，如果文件存在则读取"""
        self.data_history_order = self.read_csv_if_exists(
            self.history_order_file_path, encoding='utf-8',
            dtype={'itemCode': str, 'projectCode': str, 'oemCode': str}
        )

        self.data_passenger_car_market_info = self.read_csv_if_exists(
            self.passenger_car_market_info_file_path, encoding='utf-8',
            dtype={'itemCode': str, 'projectCode': str, 'oemCode': str, 'globalProjectCode': str}
        )

        self.data_project_mapping = self.read_csv_if_exists(
            self.project_mapping_file_path, encoding='utf-8',
            dtype={'itemCode': str, 'projectCode': str, 'oemCode': str, 'globalProjectCode': str}
        )

        self.data_oem_project_info = self.read_csv_if_exists(
            self.oem_project_info_file_path, encoding='utf-8',
            dtype={'itemCode': str, 'projectCode': str, 'oemCode': str}
        )
        self.data_oem_project_info = self.data_oem_project_info.drop_duplicates('itemCode')

        self.data_oem_stock_info = self.read_csv_if_exists(
            self.oem_stock_info_file_path, encoding='utf-8',
            dtype={'itemCode': str, 'projectCode': str, 'oemCode': str}
        )

        self.data_market_info = self.read_csv_if_exists(
            self.market_info_file_path, encoding='utf-8',
            dtype={'itemCode': str, 'projectCode': str, 'oemCode': str}
        )

        self.data_policy_info = self.read_csv_if_exists(
            self.policy_info_file_path, encoding='utf-8',
            dtype={'itemCode': str, 'projectCode': str, 'oemCode': str}
        )

        self.data_oem_forecast = self.read_csv_if_exists(
            self.oem_forecast_file_path, encoding='utf-8',
            dtype={'itemCode': str, 'projectCode': str, 'oemCode': str}
        )

        self.data_competition_group = self.read_csv_if_exists(
            self.competition_group_file_path, encoding='utf-8',
            dtype={'priceLowerBound': float, 'priceUpperBound': float, 'type': str, 'group': str}
        )

        self.data_oem_vehicle_model = self.read_csv_if_exists(
            self.oem_vehicle_model_file_path, encoding='utf-8',
            dtype={
                'oemCode': str,
                'oemName': str,
                'oemVehicleModelCode': str,
                'vehicleModelPrice': str,
                'vehicleType': str}
        )