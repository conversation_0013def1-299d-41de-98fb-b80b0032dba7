from cx_Freeze import setup, Executable

# 要打包的 Python 脚本路径
script = "launch.py"

# 创建可执行文件的配置
exe = Executable(
    script=script,
    base=None,  # 对于 GUI 应用，可以设置为 "Win32GUI" 来隐藏控制台窗口
    target_name="launch_forecast"  # 生成的可执行文件名称
)

# 打包的参数配置
options = {
    "build_exe": {
        "packages": ["numpy", "pandas", "xgboost", "sklearn", "xgboost", "matplotlib"],  # 需要打包的额外 Python 包列表
        "excludes": [],  # 不需要打包的 Python 包列表
        "include_files": [
		"/root/miniconda3/envs/rzz/lib/libmkl_core.so.1",
		"/root/miniconda3/envs/rzz/lib/libmkl_intel_thread.so.1",
		"/root/miniconda3/envs/rzz/lib/libmkl_intel_lp64.so.1",
		"/root/miniconda3/envs/rzz/lib/libmkl_avx2.so.1",
		"/root/miniconda3/envs/rzz/lib/libmkl_def.so.1"
],  # 需要包含的文件或文件夹列表
        "include_msvcr": True  # 是否包含 Microsoft Visual C++ 运行时库
    }
}

# 打包配置
setup(
    name="launch",
    version="1.0",
    description="描述你的应用",
    options=options,
    executables=[exe]
)
