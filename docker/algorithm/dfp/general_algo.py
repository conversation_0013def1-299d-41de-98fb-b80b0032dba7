import pandas as pd
import numpy as np
from itertools import chain
import xgboost as xgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.arima.model import ARIMA
from logging_config import setup_logging
import warnings
from statsmodels.tsa.holtwinters import Holt
warnings.filterwarnings("ignore")
import sys
import logging

logger = setup_logging()
print("Python版本：", sys.version)


class GeneralAlgorithm:

    def __init__(self,
                 history_sales,
                 forecast_periods,
                 key_list,
                 other_info_time_series,
                 other_need_list,
                 ):
        self.history_sales = history_sales
        self.forecast_periods = forecast_periods
        self.key_list = key_list
        self.other_info_time_series = other_info_time_series
        self.other_need_list = other_need_list

    def forecast_xgb(
            self,
            history_sales,
            coming_sales_history,
            coming_month_sales,
            forecast_periods,
            key_list,
            other_info_time_series,
            other_need_list,
            other_type_list,
            future_feature_dict
    ):
        '''
        用xgboost模型预测，内容包括：归一化、创建特征矩阵、训练和预测
        参数：
            history_sales:历史销售数据
            coming_sales:预测当月销量
            forecast_periods：预测期间
            other_info_time_series：其他特征数据，如定价、促销、是否开始停产、量产。list类型，元素为pd.dataframe类型。
            key_list:主键的索引名称
            other_need_list:表示other_info_time_series中的数据是否需要构建滞后特征和窗口特征的list：
            1表示需要，
            0表示不需要(构建当前时间的特征，即能够获取预测所在时间的特征信息)
            -1表示前处理已处理好，可直接加入列表
            other_type_list:表示other_info_time_series中的数据是否要做归一化（是否数值型）。1表示数值型，0表示非数值型(无需处理),-1表示无需处理的数值
            future_feature_dict：表示
        '''

        # 对销量进行归一化
        standardized_history_sales, SKU_dict = self.standardized(history_sales, key_list)
        standardized_coming_sales_history = self.standardized_coming_sales(coming_sales_history, SKU_dict, key_list)
        standardized_coming_month_sales = self.standardized_coming_sales(coming_month_sales, SKU_dict, key_list)
        # 对其他特征进行归一化
        standardized_other_info_time_series = []
        SKU_other_info_dict = []
        for i, d in enumerate(other_info_time_series):
            if other_type_list[i] != 1:
                standardized_other_info_time_series.append(d)
                continue
            standardized_data, standardized_dict = self.standardized(d, key_list)
            standardized_other_info_time_series.append(standardized_data)
            SKU_other_info_dict.append(standardized_dict)
        # 构造特征矩阵
        feature_matrix = self.create_feature_matrix(
            standardized_history_sales,
            standardized_coming_sales_history,
            standardized_other_info_time_series,
            other_need_list,
            key_list,
            forecast_periods
        )

        # 预测最新销量
        forecast_result = self.xgb_model_train_and_forecast(
            feature_matrix,
            standardized_history_sales,
            standardized_coming_month_sales,
            standardized_other_info_time_series,
            other_need_list,
            other_type_list,
            key_list,
            forecast_periods,
            future_feature_dict,
            shift_len=3,
            ttm_set=[3, 6]
        )

        forecast_result = self.back_process(forecast_result, SKU_dict, key_list)
        return forecast_result

    def standardized(self, df, key_list):
        '''
        归一化
        '''
        logging.info("standardized")

        data = df.copy()
        key_num = len(key_list)
        SKU_dict = {}
        SKU_list = data.index
        for i in range(len(data)):
            min_value = data.iloc[i, key_num:].min()
            max_value = data.iloc[i, key_num:].max()
            SKU_dict[tuple(data.iloc[i, :key_num])] = {'min_value': min_value, 'max_value': max_value}
            if max_value - min_value != 0:
                data.iloc[i, key_num:] = (data.iloc[i, key_num:] - min_value) / (max_value - min_value)
            else:
                data.iloc[i, key_num:] = 0.0
        return data, SKU_dict

    def standardized_coming_sales(self, df, max_min_dict, key_list):
        logging.info("standardized_coming_sales")
        std_df_lt = list()
        for k, sub_df in df.groupby(key_list):
            min_value, max_value = max_min_dict[k]['min_value'], max_min_dict[k]['max_value']
            if max_value - min_value != 0:
                std_ser = (sub_df['coming_month_qty'] - min_value) / (max_value - min_value)
                sub_df['coming_month_qty'] = std_ser
            else:
                sub_df['coming_month_qty'] = 0.0
            std_df_lt.append(sub_df)
        std_df = pd.concat(std_df_lt, axis=0)
        return std_df

    def create_feature_matrix(
            self,
            standardized_history_sales,
            standardized_coming_sales,
            standardized_other_info_time_series,
            other_need_list,
            key_list,
            forecast_periods
    ):
        '''
        特征矩阵的构造:包括自相关特征和其他特征
        '''
        logging.info("create_feature_matrix")
        sales_feature_matrix = self.autocorrelation_feature(standardized_history_sales, key_list, forecast_periods)
        other_feature_matrix_list = self.other_feature(standardized_other_info_time_series, key_list, forecast_periods,
                                                  other_need_list)

        on_list = [item for item in key_list]
        on_list.append('curr_time')
        feature_matrix = sales_feature_matrix
        feature_matrix = pd.merge(
            left=feature_matrix,
            right=standardized_coming_sales,
            on=on_list,
            how='left'
        )
        feature_matrix['day_passed'] = feature_matrix['day_passed'].fillna(feature_matrix['day_passed'].max())
        feature_matrix['coming_month_qty'] = feature_matrix['coming_month_qty'].fillna(0.0)
        on_list.append('pre_period')
        # 循环合并剩余的DataFrame
        for df in other_feature_matrix_list:
            feature_matrix = pd.merge(left=feature_matrix, right=df, on=on_list, how='left')
        feature_matrix = feature_matrix.drop(columns=['curr_time'])
        return feature_matrix

    def autocorrelation_feature(self, standardized_data, key_list, forecast_periods, shift_len=3, ttm_set=[3, 6]):
        '''
        自相关特征，如滞后、窗口特征的构造
        '''
        logging.info("autocorrelation_feature")
        df_list = []
        for _, d in standardized_data.iterrows():
            SKU = d[key_list]
            d = pd.DataFrame(d.iloc[len(key_list):])
            d.columns = ['current']
            for i in range(shift_len):
                d['shift' + str(i + 1)] = d['current'].shift(i + 1).fillna(0)
            for i in ttm_set:
                d['ttm' + str(i)] = d['shift1'].rolling(window=i, min_periods=1).sum()
            standard_mat = d.iloc[:, 1:]
            for k in key_list:
                standard_mat[k] = SKU[k]
            target_list = d.iloc[:, 0]

            for m in forecast_periods:
                standard_mat['target'] = target_list.values
                standard_mat['pre_period'] = m
                df_list.append(standard_mat[:].copy())
                standard_mat = standard_mat.iloc[:-1]
                target_list = target_list.iloc[1:]
        data = pd.concat(df_list, axis=0)
        data = data.dropna()
        data.reset_index(drop=False, inplace=True)
        data['curr_time'] = data['index'].astype(str)
        data = data.drop(columns='index')

        return data

    def other_feature(self, standardized_other_info_time_series, key_list, forecast_periods, other_need_list, shift_len=3,
                      ttm_set=[3, 6]):
        '''
        不同项目可能会提供不同信息
        一些其他特征的构造
        '''
        logging.info("other_feature")
        other_feature_matrix_list = []
        for i in range(len(standardized_other_info_time_series)):
            standardized_data = standardized_other_info_time_series[i]
            name = 'of' + str(i)
            if other_need_list[i] == 1:
                df_list = []
                for _, d in standardized_data.iterrows():
                    SKU = d[key_list]
                    d = pd.DataFrame(d.iloc[len(key_list):])
                    d.columns = ['current']
                    for i in range(shift_len):
                        d[name+'shift' + str(i + 1)] = d['current'].shift(i + 1)
                    for i in ttm_set:
                        d[name+'ttm' + str(i)] = d[name+'shift1'].rolling(i).sum()
                    standard_mat = d.iloc[:, 1:]
                    for k in key_list:
                        standard_mat[k] = SKU[k]
                    target_list = d.iloc[:, 0]

                    for m in forecast_periods:
                        # todo 这个特征可能是未知
                        # standard_mat[name] = target_list.values
                        standard_mat['pre_period'] = m
                        df_list.append(standard_mat[:])
                        standard_mat = standard_mat.iloc[:-1]
                        # target_list = target_list.iloc[1:]
                data = pd.concat(df_list, axis=0)
                data = data.dropna()
                data.reset_index(drop=False, inplace=True)
                data['curr_time'] = data['index'].astype(str)
                data = data.drop(columns='index')
                other_feature_matrix_list.append(data)

            elif other_need_list[i] == 0:
                # 创建空的列表来保存DataFrame
                df_list = []
                # 循环生成不同的DataFrame

                for _, d in standardized_data.iterrows():
                    SKU = d[key_list]
                    d = pd.DataFrame(d.iloc[len(key_list):])
                    d.columns = ['current']
                    standard_mat = d.iloc[:, 1:]
                    for k in key_list:
                        standard_mat[k] = SKU[k]
                    target_list = d.iloc[:, 0]

                    for m in forecast_periods:
                        standard_mat[name] = target_list.values
                        standard_mat['pre_period'] = m
                        df_list.append(standard_mat[:])
                        standard_mat = standard_mat.iloc[:-1]
                        target_list = target_list.iloc[1:]
                data = pd.concat(df_list, axis=0)
                data = data.dropna()
                data.reset_index(drop=False, inplace=True)
                data['curr_time'] = data['index'].astype(str)
                data = data.drop(columns='index')
                other_feature_matrix_list.append(data)
            elif other_need_list[i] == -1:
                standardized_data.rename(columns={'feature_value': name}, inplace=True)
                other_feature_matrix_list.append(standardized_data)

        return other_feature_matrix_list

    def xgb_model_train_and_forecast(
            self,
            feature_matrix,
            standardized_history_sales,
            standardized_coming_month_sales,
            standardized_other_info_time_series,
            other_need_list,
            other_type_list,
            key_list,
            forecast_periods,
            future_feature_dict,
            shift_len=3,
            ttm_set=[3, 6]
    ):
        logging.info("xgb_model_train_and_forecast")
        all_columns = feature_matrix.columns.tolist()
        all_columns.remove('target')
        categorical_features = key_list.copy()
        for i in range(len(other_type_list)):
            if other_type_list[i] == 0:
                categorical_features.append('of' + str(i))
        value_features = [col for col in all_columns if col not in categorical_features]
        data_df = feature_matrix

        # 转换类型
        logger.info("categorical_features: %s" % categorical_features)
        for feature in categorical_features:
            data_df[feature] = data_df[feature].astype('category')

        logger.info("value_features: %s" % value_features)
        for feature in value_features:
            data_df[feature] = data_df[feature].astype('float')
        # 训练集和测试集的划分
        # 获取特征值和目标值
        X, y = data_df.drop(columns=['target']), data_df['target']
        # 随机划分

        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=0)

        # 将数据转换为 DMatrix 格式，并设置 enable_categorical 参数为 True
        dtrain = xgb.DMatrix(data=X_train, label=y_train, enable_categorical=True)
        dtest = xgb.DMatrix(data=X_test, label=y_test, enable_categorical=True)

        # 定义参数
        params = {
            'objective': 'reg:squarederror',  # 目标
            # 指定目标为回归任务，并采用平方误差
            'booster': 'gbtree',  # 树模型
            # 使用梯度提升树模型
            'eval_metric': 'rmse',  # 评估指标为均方根误差
            # 训练过程中的评估指标
            # 'eta': 0.01,  # 学习率
            # 学习速率，控制每次迭代更新的步长。较小的学习速率可以使得模型更加稳定，但可能需要更多的迭代次数。
            # 'max_depth': 15,  # 树的最大深度
            # 树的最大深度，决定了模型的复杂度，取值越大模型越复杂，可能出现过拟合
            'subsample': 1.0,  # 每棵树的样本抽样比例
            # 每次训练随机采样的比例
            'colsample_bytree': 1.0,  # 每棵树的特征抽样比例
            # 每次训练特征抽样的比例
            'lambda': 0.1,  # L2 正则化参数
            'alpha': 0.1,  # L1 正则化参数
            # 正则化参数，防止过拟合
            'gamma': 0,  # 控制叶子数量
            # 分裂最小收益量，取值越大则分裂越保守
            # 'min_child_weight': 1,  # 最小叶子节点权重和
            # 最小叶子节点权重和，用于控制树的生长
            'verbosity': 0  # 可视化设置
            # 可视化设置，用于决定输出信息详细程度，取值越大，输出越详细
        }

        # 训练模型
        num_rounds = 1000  # 迭代次数
        # 控制了模型的训练轮数，即每次模型更新的次数。增加迭代次数可能会提高模型的性能，但也可能增加训练时间和过拟合的风险。
        early_stop_round = 5
        # 通过设定提前停止轮数，可以避免模型过拟合，并提高训练效率。
        evals_result = {}  # 保存训练过程中的评估指标

        # 模型训练
        model = xgb.train(params=params,
                          #   模型的参数设置字典
                          dtrain=dtrain,
                          #   训练数据集
                          num_boost_round=num_rounds,
                          #   迭代总轮数
                          evals=[(dtrain, 'train'), (dtest, 'test')],
                          #   验证集，在模型训练过程中记录下验证集的指标
                          # 其中训练集上的指标以及测试集上的指标分开记录，便于后续调参及可视化
                          # 该部分指标并不会指导模型训练，模型训练仍然基于训练数据集
                          evals_result=evals_result,
                          #   训练过程中的评估指标将被记录在 eval_result
                          verbose_eval=50,
                          # 可视化参数设置，迭代多少轮后输出一次结果
                          early_stopping_rounds=early_stop_round
                          #   提前终止轮数，当连续 early_stopping_rounds 轮未提升后，将提前终止训练
                          )

        # 模型预测
        y_pred = model.predict(dtest)
        
        # 储存模型，后续用作输出特征重要性
        self.model = model 

        # 预测
        data_list = []

        # 构建销售特征矩阵
        data = standardized_history_sales[key_list].copy()
        for i in range(shift_len):
            data.loc[:, 'shift' + str(i + 1)] = standardized_history_sales.iloc[:, -i - 1].values
        for i in ttm_set:
            data.loc[:, 'ttm' + str(i)] = standardized_history_sales.iloc[:, -i:].sum(axis=1).values

        data = pd.merge(left=data, right=standardized_coming_month_sales, on=key_list, how='left')
        data['coming_month_qty'] = data['coming_month_qty'].fillna(0.0)
        data['day_passed'] = data['day_passed'].fillna(data['day_passed'].max())

        for m in forecast_periods:
            temp_data = data[:].copy()
            temp_data.loc[:, 'pre_period'] = m
            data_list.append(temp_data)
        data = pd.concat(data_list, axis=0)
        data.reset_index(drop=True, inplace=True)

        # 构建其他特征矩阵
        features_df = []
        for i in range(len(standardized_other_info_time_series)):
            standardized_data = standardized_other_info_time_series[i]
            name = 'of' + str(i)
            if other_need_list[i] == 1:
                df_list = []
                data_last = standardized_data[key_list]
                for i in range(shift_len):
                    data_last.loc[:, name+'shift' + str(i + 1)] = standardized_data.iloc[:, -i - 1].values
                for i in ttm_set:
                    data_last.loc[:, name+'ttm' + str(i)] = standardized_data.iloc[:, -i:].sum(axis=1).values
                for m in forecast_periods:
                    temp_data = data_last[:]
                    temp_data.loc[:, 'pre_period'] = m
                    df_list.append(temp_data)
                data_last = pd.concat(df_list, axis=0)
                data_last.reset_index(drop=True, inplace=True)
                features_df.append(data_last)

            elif i in future_feature_dict:
                # 这里直接多传一张表，因为这个数据是当前时间预计未来的一个结果
                # 因此不与此前信息一起传以免混淆【过去时间的其他特征用于训练，而该部分用于训练】
                future_feature_dict[i].rename(columns={'feature_value': name}, inplace=True)
                features_df.append(future_feature_dict[i])
            else:
                # 创建空的列表来保存DataFrame
                df_list = []
                data_last = standardized_data[key_list]
                for m in forecast_periods:
                    temp_data = data_last[:].copy()
                    temp_data.loc[:, 'pre_period'] = m
                    temp_data.loc[:, name] = standardized_data.iloc[:, -1]
                    df_list.append(temp_data)
                data_last = pd.concat(df_list, axis=0)
                data_last.reset_index(drop=True, inplace=True)
                features_df.append(data_last)
        on_list = [item for item in key_list]
        on_list.append('pre_period')
        for df in features_df:
            data = pd.merge(left=data, right=df, on=on_list, how='left')
        data = data[all_columns]

        # 设定特征数据类型
        for feature in categorical_features:
            data[feature] = data[feature].astype('category')
        for feature in value_features:
            data[feature] = data[feature].astype('float')

        # 模型预测
        data_forecast = xgb.DMatrix(data=data, enable_categorical=True)
        y_pred = model.predict(data_forecast)

        # 输出矩阵的构建
        forecast_product_sales = pd.DataFrame(data[key_list])
        repetitions = len(standardized_history_sales)
        forecast_product_sales['forecastPeriod'] = list(
            chain.from_iterable([x] * repetitions for x in forecast_periods))
        forecast_product_sales['forecastQuantity'] = y_pred

        # 绘制训练和测试过程中的 RMSE
        max_iterations = 50  # 绘制前50次迭代
        train_rmse = evals_result['train']['rmse'][:max_iterations]
        test_rmse = evals_result['test']['rmse'][:max_iterations]
        iterations = range(len(train_rmse))

        logger.info('xgb_model_train_and_forecast')
        logger.info(params)
        return forecast_product_sales

    def back_process(self, forecast_product_sales, SKU_sales_dict, key_list):
        col_names = forecast_product_sales.columns
        SKU_list = []
        for key, value in SKU_sales_dict.items():
            temp_list = []
            for i in key:
                temp_list.append(i)
            temp_list.append(value['max_value'])
            temp_list.append(value['min_value'])
            SKU_list.append(temp_list)
        cols = key_list.copy()
        cols.append('max_value')
        cols.append('min_value')
        SKU_df = pd.DataFrame(SKU_list)
        SKU_df.columns = cols

        merge_df = pd.merge(forecast_product_sales, SKU_df, on=key_list, how='left')
        merge_df['forecastQuantity'] = merge_df['forecastQuantity'] * (merge_df['max_value'] - merge_df['min_value']) + \
                                       merge_df['min_value']
        forecast_product_sales = merge_df[col_names]
        return forecast_product_sales

    def forecast_ma(self, df, key_list, pre_period, windows=3):
        df_list = []
        for _, row in df.iterrows():
            temp_df = pd.DataFrame()
            for k in key_list:
                temp_df[k] = [row[k]]
            for m in pre_period:
                forecast_value = row[-windows:].mean()
                row[f'forecast_{m}'] = forecast_value
                temp_df['forecastPeriod'] = m
                temp_df['forecastQuantity'] = forecast_value
                df_list.append(temp_df[:])
        data = pd.concat(df_list, axis=0)
        data = data.dropna()
        data.reset_index(drop=False, inplace=True)
        logger.info(f'ma_forecast:windows:{windows}')
        return data

    def forecast_wma(self, df, key_list, pre_period, windows=3):
        '''
        使用加权移动平均法 (WMA) 预测未来数据
        df: 数据框，包含历史销售数据
        key_list: 关键字段列表，例如 SKU 信息
        pre_period: 预测的未来时间段列表
        '''
        weights = np.array(range(windows + 1, 1, -1))
        sum_weights = np.sum(weights)
        df_list = []

        for _, row in df.iterrows():
            temp_df = pd.DataFrame()
            for k in key_list:
                temp_df[k] = [row[k]]
            for m in pre_period:
                sales_data = row[-windows:]  # 假设时间序列数据在最后几列
                forecast_value = np.sum(weights * sales_data) / sum_weights
                row[f'forecast_{m}'] = forecast_value
                temp_df['forecastPeriod'] = m
                temp_df['forecastQuantity'] = forecast_value
                df_list.append(temp_df[:])

        data = pd.concat(df_list, axis=0)
        data = data.dropna()
        data.reset_index(drop=True, inplace=True)
        logger.info(f'wma_forecast:windows:{windows}')
        return data

    def forecast_arima(self, df, key_list, pre_period):
        df_list = []
        # 定义 ARIMA 模型的参数(p, d, q)
        p = 3  # 自回归项数
        d = 1  # 差分阶数
        q = 1  # 移动平均项数
        for _, row in df.iterrows():
            # 数据准备
            temp_df = pd.DataFrame()
            for k in key_list:
                temp_df[k] = [row[k]]

            # 计算平滑值
            row = pd.DataFrame(row.iloc[len(key_list):])
            row.columns = ['current']
            row = row[row['current'] != 0]
            # 初始化并拟合 ARIMA 模型
            row['current'].index = pd.to_datetime(row['current'].index, format='%Y%m')
            date_range = row['current'].astype(float)
            date_range.index = pd.DatetimeIndex(date_range.index).to_period('M').to_timestamp()
            model = ARIMA(date_range, order=(p, d, q))
            model_fit = model.fit()
            # 预测未来的值
            forecast = model_fit.forecast(steps=len(pre_period))
            # 预测未来
            for idx, m in enumerate(pre_period):
                temp_df['forecastPeriod'] = m
                temp_df['forecastQuantity'] = forecast.iloc[idx]
                df_list.append(temp_df[:])

        data = pd.concat(df_list, axis=0)
        data = data.dropna()
        data.reset_index(drop=True, inplace=True)
        logger.info(f'arima_forecast:p:{p},d:{d},q:{q}')
        return data

    def forecast_ses(self, df, key_list, pre_period, alpha):
        df_list = []
        for _, row in df.iterrows():

            # 数据准备
            temp_df = pd.DataFrame()
            for k in key_list:
                temp_df[k] = [row[k]]

            # 计算平滑值
            row = pd.DataFrame(row.iloc[len(key_list):])
            row.columns = ['current']
            row = row[row['current'] != 0]
            row['ewm1'] = row['current'].ewm(alpha=alpha).mean()
            row['forecastQuantity'] = row['ewm1'].shift(1)

            # 预测未来
            for m in pre_period:
                temp_df['forecastPeriod'] = m
                temp_df['forecastQuantity'] = row['ewm1'].iloc[-1]
                df_list.append(temp_df[:])

        data = pd.concat(df_list, axis=0)
        data = data.dropna()
        data.reset_index(drop=True, inplace=True)
        logger.info(f'ses_forecast:alpha:{alpha}')
        return data

    def forecast_des(self, df, key_list, pre_period, alpha, beta):

        df_list = []

        for _, row in df.iterrows():
            # 数据准备
            temp_df = pd.DataFrame()
            for k in key_list:
                temp_df[k] = [row[k]]

            # 计算平滑值
            row = pd.DataFrame(row.iloc[len(key_list):])
            row.columns = ['current']
            row = row[row['current'] != 0]

            holt_model = Holt(row['current'].astype(float)).fit(smoothing_level=alpha, smoothing_slope=beta)

            forecast = holt_model.forecast(steps=len(pre_period))
            forecast.reset_index(drop=True, inplace=True)
            # 预测未来
            for idx, m in enumerate(pre_period):
                temp_df['forecastPeriod'] = m
                temp_df['forecastQuantity'] = forecast[idx]
                df_list.append(temp_df[:])

        data = pd.concat(df_list, axis=0)
        data = data.dropna()
        data.reset_index(drop=True, inplace=True)
        logger.info(f'des_forecast:smoothing_level={alpha},smoothing_slope={beta}')
        return data

    def forecast_ces(self, df, key_list, pre_period):
        '''
        用三次指数平滑模型预测
        '''
        df_list = []
        # 参数
        trend = 'add'
        seasonal = 'mul'
        seasonal_periods = 4
        for _, row in df.iterrows():
            # 数据准备
            temp_df = pd.DataFrame()
            for k in key_list:
                temp_df[k] = [row[k]]

            # 计算平滑值
            row = pd.DataFrame(row.iloc[len(key_list):])
            row.columns = ['current']
            row = row[row['current'] != 0]
            row['current'].index = pd.to_datetime(row['current'].index, format='%Y%m')
            date_range = row['current'].astype(float)
            date_range.index = pd.DatetimeIndex(date_range.index).to_period('M').to_timestamp()
            model = ExponentialSmoothing(date_range, trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods)
            fit = model.fit(optimized=True)

            # 预测未来12个时间点的值
            forecast = fit.forecast(steps=len(pre_period))
            forecast.reset_index(drop=True, inplace=True)

            # 预测未来
            for idx, m in enumerate(pre_period):
                temp_df['forecastPeriod'] = m
                temp_df['forecastQuantity'] = forecast[idx]
                df_list.append(temp_df[:])

        data = pd.concat(df_list, axis=0)
        data = data.dropna()
        data.reset_index(drop=True, inplace=True)
        logger.info(f'ces_forecast:trend={trend} ,seasonal={seasonal}, seasonal_periods={seasonal_periods}')
        return data

    def find_best_forecast(self, history_sales, other_info_time_series,other_need_list, key_list, forecast_periods):
        '''预测最后的十个周期的销量，计算rmse指标'''
        alg_score = dict()

        # 对销量进行归一化
        standardized_history_sales, SKU_dict = self.standardized(history_sales, key_list)

        # 对其他特征进行归一化
        standardized_other_info_time_series = []
        SKU_other_info_dict = []
        for d in other_info_time_series:
            standardized_data, standardized_dict = self.standardized(d, key_list)
            standardized_other_info_time_series.append(standardized_data)
            SKU_other_info_dict.append(standardized_dict)

        # 划分出测试集
        train_data = standardized_history_sales.iloc[:, :-10]
        test_data = standardized_history_sales.iloc[:, list(range(len(key_list))) + list(range(-10, 0))]

        # 准备真实数据的格式
        df_list = []
        for _, row in test_data.iterrows():
            temp_df = pd.DataFrame(row.iloc[len(key_list):].copy())
            temp_df.columns = ['trueSales']
            for k in key_list:
                temp_df[k] = row[k]
            temp_df['forecastPeriod'] = 0
            for i in range(len(temp_df)):
                temp_df['forecastPeriod'][i] = i + 1
            df_list.append(temp_df)
        true_df = pd.concat(df_list, axis=0)
        true_df.reset_index(drop=True, inplace=True)
        on_list = key_list.copy()
        on_list.append('forecastPeriod')

        # xgb-构造特征矩阵
        feature_matrix = self.create_feature_matrix(standardized_history_sales, standardized_other_info_time_series,
                                               self.other_need_list, key_list, forecast_periods)

        # xgb预测最新销量
        xgb_pred = self.xgb_model_train_and_forecast(feature_matrix, standardized_history_sales,
                                                standardized_other_info_time_series,
                                                self.other_need_list, key_list, forecast_periods, shift_len=3,
                                                ttm_set=[3, 6])
        xgb_pred_merged = pd.merge(xgb_pred, true_df, on=on_list)
        alg_score['xgb'] = np.sqrt(
            mean_squared_error(xgb_pred_merged['trueSales'], xgb_pred_merged['forecastQuantity']))

        # ma
        ma_pred = self.forecast_ma(train_data, key_list, forecast_periods, windows=3)
        ma_pred_merged = pd.merge(ma_pred, true_df, on=on_list)
        alg_score['ma'] = np.sqrt(mean_squared_error(ma_pred_merged['trueSales'], ma_pred_merged['forecastQuantity']))

        # wma
        wma_pred = self.forecast_wma(train_data, key_list, forecast_periods, windows=3)
        wma_pred_merged = pd.merge(wma_pred, true_df, on=on_list)
        alg_score['wma'] = np.sqrt(
            mean_squared_error(wma_pred_merged['trueSales'], wma_pred_merged['forecastQuantity']))

        # 一次指数平滑模型
        ses_pred = self.forecast_ses(train_data, key_list, forecast_periods, alpha=0.3)
        ses_pred_merged = pd.merge(ses_pred, true_df, on=on_list)
        alg_score['ses'] = np.sqrt(
            mean_squared_error(ses_pred_merged['trueSales'], ses_pred_merged['forecastQuantity']))

        # 二次指数平滑模型
        des_pred = self.forecast_des(train_data, key_list, forecast_periods, 0.3, 0.3)
        des_pred_merged = pd.merge(des_pred, true_df, on=on_list)
        alg_score['des'] = np.sqrt(
            mean_squared_error(des_pred_merged['trueSales'], des_pred_merged['forecastQuantity']))

        # 三次指数平滑模型
        ces_pred = self.forecast_ces(train_data, key_list, forecast_periods)
        ces_pred_merged = pd.merge(ces_pred, true_df, on=on_list)
        alg_score['ces'] = np.sqrt(
            mean_squared_error(ces_pred_merged['trueSales'], ces_pred_merged['forecastQuantity']))

        # arima
        arima_pred = self.forecast_arima(train_data, key_list, forecast_periods)
        arima_pred_merged = pd.merge(arima_pred, true_df, on=on_list)
        alg_score['arima'] = np.sqrt(
            mean_squared_error(arima_pred_merged['trueSales'], arima_pred_merged['forecastQuantity']))

        logger.info(f'find_best_forecast')
        logger.info(alg_score)

        return alg_score