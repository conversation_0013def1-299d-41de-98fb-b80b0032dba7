package com.yhl.scp.mps.plan.domain.factory;

import com.yhl.scp.mps.plan.domain.entity.MasterPlanRecordDO;
import com.yhl.scp.mps.plan.dto.MasterPlanRecordDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanRecordDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>MasterPlanRecordFactory</code>
 * <p>
 * 领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 10:23:11
 */
@Component
public class MasterPlanRecordFactory {

    @Resource
    private MasterPlanRecordDao masterPlanRecordDao;

    MasterPlanRecordDO create(MasterPlanRecordDTO dto) {
        // TODO
        return null;
    }

}
