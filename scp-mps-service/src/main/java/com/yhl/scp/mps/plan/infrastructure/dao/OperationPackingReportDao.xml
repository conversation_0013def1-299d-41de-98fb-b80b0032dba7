<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.plan.infrastructure.dao.OperationPackingReportDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.plan.infrastructure.po.OperationPackingReportPO">
        <!--@Table sds_ord_operation_packing_report-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="operation_id" jdbcType="VARCHAR" property="operationId"/>
        <result column="quantity" jdbcType="VARCHAR" property="quantity"/>
        <result column="finished_quantity" jdbcType="VARCHAR" property="finishedQuantity"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="line_time" jdbcType="INTEGER" property="lineTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.plan.vo.OperationPackingReportVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,order_id,operation_id,quantity,finished_quantity,start_time,end_time,line_time,remark,enabled,creator,create_time,modifier,modify_time

    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.orderId != null and params.orderId != ''">
                and order_id = #{params.orderId,jdbcType=VARCHAR}
            </if>
            <if test="params.operationId != null and params.operationId != ''">
                and operation_id = #{params.operationId,jdbcType=VARCHAR}
            </if>
            <if test="params.quantity != null">
                and quantity = #{params.quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.finishedQuantity != null">
                and finished_quantity = #{params.finishedQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.startTime != null">
                and start_time = #{params.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endTime != null">
                and end_time = #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lineTime != null">
                and line_time = #{params.lineTime,jdbcType=INTEGER}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_ord_operation_packing_report
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_ord_operation_packing_report
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_sds_ord_operation_packing_report
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_ord_operation_packing_report
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from sds_ord_operation_packing_report
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增（带主键） -->
    <insert id="insert" parameterType="com.yhl.scp.mps.plan.infrastructure.po.OperationPackingReportPO">
        insert into sds_ord_operation_packing_report(id,
                                                     order_id,
                                                     operation_id,
                                                     quantity,
                                                     finished_quantity,
                                                     start_time,
                                                     end_time,
                                                     line_time,
                                                     remark,
                                                     enabled,
                                                     creator,
                                                     create_time,
                                                     modifier,
                                                     modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{orderId,jdbcType=VARCHAR},
                #{operationId,jdbcType=VARCHAR},
                #{quantity,jdbcType=VARCHAR},
                #{finishedQuantity,jdbcType=VARCHAR},
                #{startTime,jdbcType=TIMESTAMP},
                #{endTime,jdbcType=TIMESTAMP},
                #{lineTime,jdbcType=INTEGER},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 批量新增（带主键） -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sds_ord_operation_packing_report(
        id,
        order_id,
        operation_id,
        quantity,
        finished_quantity,
        start_time,
        end_time,
        line_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.orderId,jdbcType=VARCHAR},
            #{entity.operationId,jdbcType=VARCHAR},
            #{entity.quantity,jdbcType=VARCHAR},
            #{entity.finishedQuantity,jdbcType=VARCHAR},
            #{entity.startTime,jdbcType=TIMESTAMP},
            #{entity.endTime,jdbcType=TIMESTAMP},
            #{entity.lineTime,jdbcType=INTEGER},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.plan.infrastructure.po.OperationPackingReportPO">
        update sds_ord_operation_packing_report
        set order_id          = #{orderId,jdbcType=VARCHAR},
            operation_id      = #{operationId,jdbcType=VARCHAR},
            quantity          = #{quantity,jdbcType=VARCHAR},
            finished_quantity = #{finishedQuantity,jdbcType=VARCHAR},
            start_time        = #{startTime,jdbcType=TIMESTAMP},
            end_time          = #{endTime,jdbcType=TIMESTAMP},
            line_time         = #{lineTime,jdbcType=INTEGER},
            remark            = #{remark,jdbcType=VARCHAR},
            enabled           = #{enabled,jdbcType=VARCHAR},
            modifier          = #{modifier,jdbcType=VARCHAR},
            modify_time       = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.plan.infrastructure.po.OperationPackingReportPO">
        update sds_ord_operation_packing_report
        <set>
            <if test="item.orderId != null and item.orderId != ''">
                order_id = #{item.orderId,jdbcType=VARCHAR},
            </if>
            <if test="item.operationId != null and item.operationId != ''">
                operation_id = #{item.operationId,jdbcType=VARCHAR},
            </if>
            <if test="item.quantity != null">
                quantity = #{item.quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.finishedQuantity != null">
                finished_quantity = #{item.finishedQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lineTime != null">
                line_time = #{item.lineTime,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sds_ord_operation_packing_report
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="finished_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.finishedQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="line_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lineTime,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update sds_ord_operation_packing_report
            <set>
                <if test="item.orderId != null and item.orderId != ''">
                    order_id = #{item.orderId,jdbcType=VARCHAR},
                </if>
                <if test="item.operationId != null and item.operationId != ''">
                    operation_id = #{item.operationId,jdbcType=VARCHAR},
                </if>
                <if test="item.quantity != null">
                    quantity = #{item.quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.finishedQuantity != null">
                    finished_quantity = #{item.finishedQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.startTime != null">
                    start_time = #{item.startTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.endTime != null">
                    end_time = #{item.endTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lineTime != null">
                    line_time = #{item.lineTime,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from sds_ord_operation_packing_report
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sds_ord_operation_packing_report where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteByOperationId">
        delete from sds_ord_operation_packing_report where operation_id = #{operationId,jdbcType=VARCHAR}
    </delete>
</mapper>
