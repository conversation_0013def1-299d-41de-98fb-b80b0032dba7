package com.yhl.scp.mps.product.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldToolingGroupDir;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.routing.dto.ProductCandidateResourceDTO;
import com.yhl.scp.mps.product.convertor.ChainLineInventoryLogConvertor;
import com.yhl.scp.mps.product.domain.entity.ChainLineInventoryLogDO;
import com.yhl.scp.mps.product.domain.service.ChainLineInventoryLogDomainService;
import com.yhl.scp.mps.product.dto.ChainLineInventoryLogDTO;
import com.yhl.scp.mps.product.infrastructure.dao.ChainLineInventoryLogDao;
import com.yhl.scp.mps.product.infrastructure.po.ChainLineInventoryLogPO;
import com.yhl.scp.mps.product.service.ChainLineInventoryLogService;
import com.yhl.scp.mps.product.vo.ChainLineInventoryLogVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ChainLineInventoryLogServiceImpl</code>
 * <p>
 * 链式生产线_中间表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-08 16:28:15
 */
@Slf4j
@Service
public class ChainLineInventoryLogServiceImpl extends AbstractService implements ChainLineInventoryLogService {

    @Resource
    private ChainLineInventoryLogDao chainLineInventoryLogDao;

    @Resource
    private ChainLineInventoryLogDomainService chainLineInventoryLogDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(ChainLineInventoryLogDTO chainLineInventoryLogDTO) {
        // 0.数据转换
        ChainLineInventoryLogDO chainLineInventoryLogDO = ChainLineInventoryLogConvertor.INSTANCE.dto2Do(chainLineInventoryLogDTO);
        ChainLineInventoryLogPO chainLineInventoryLogPO = ChainLineInventoryLogConvertor.INSTANCE.dto2Po(chainLineInventoryLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        chainLineInventoryLogDomainService.validation(chainLineInventoryLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(chainLineInventoryLogPO);
        chainLineInventoryLogDao.insert(chainLineInventoryLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(ChainLineInventoryLogDTO chainLineInventoryLogDTO) {
        // 0.数据转换
        ChainLineInventoryLogDO chainLineInventoryLogDO = ChainLineInventoryLogConvertor.INSTANCE.dto2Do(chainLineInventoryLogDTO);
        ChainLineInventoryLogPO chainLineInventoryLogPO = ChainLineInventoryLogConvertor.INSTANCE.dto2Po(chainLineInventoryLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        chainLineInventoryLogDomainService.validation(chainLineInventoryLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(chainLineInventoryLogPO);
        chainLineInventoryLogDao.update(chainLineInventoryLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ChainLineInventoryLogDTO> list) {
        List<ChainLineInventoryLogPO> newList = ChainLineInventoryLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        chainLineInventoryLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ChainLineInventoryLogDTO> list) {
        List<ChainLineInventoryLogPO> newList = ChainLineInventoryLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        chainLineInventoryLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return chainLineInventoryLogDao.deleteBatch(idList);
        }
        return chainLineInventoryLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ChainLineInventoryLogVO selectByPrimaryKey(String id) {
        ChainLineInventoryLogPO po = chainLineInventoryLogDao.selectByPrimaryKey(id);
        return ChainLineInventoryLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "CHAIN_LINE_INVENTORY_LOG")
    public List<ChainLineInventoryLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "CHAIN_LINE_INVENTORY_LOG")
    public List<ChainLineInventoryLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ChainLineInventoryLogVO> dataList = chainLineInventoryLogDao.selectByCondition(sortParam, queryCriteriaParam);
        ChainLineInventoryLogServiceImpl target = springBeanUtils.getBean(ChainLineInventoryLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ChainLineInventoryLogVO> selectByParams(Map<String, Object> params) {
        List<ChainLineInventoryLogPO> list = chainLineInventoryLogDao.selectByParams(params);
        return ChainLineInventoryLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ChainLineInventoryLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> handleChainLine(List<ChainLineInventoryLogDTO> chainLineList) {
        List<ChainLineInventoryLogDTO> insertDtoS = new ArrayList<>();
        List<ChainLineInventoryLogDTO> updateDtoS = new ArrayList<>();
        Set<String> relationIds =
                chainLineList.stream().map(ChainLineInventoryLogDTO::getRelationId).collect(Collectors.toSet());
        HashMap<String, Object> map = MapUtil.newHashMap(3);
        map.put("relationIds", relationIds);
        List<ChainLineInventoryLogPO> oldPos = chainLineInventoryLogDao.selectByParams(map);
        Map<String, ChainLineInventoryLogPO> oldPosMap = oldPos.stream().collect(
                Collectors.toMap(ChainLineInventoryLogPO::getRelationId, Function.identity(), (v1, v2) -> v1));

        for (ChainLineInventoryLogDTO dto : chainLineList) {

            if (oldPosMap.containsKey(dto.getRelationId())) {
                ChainLineInventoryLogPO oldPo =  oldPosMap.get(dto.getRelationId());
                String enabled = "Y".equals(dto.getEnableFlag()) ? YesOrNoEnum.YES.getCode() :
                        YesOrNoEnum.NO.getCode();
                dto.setEnableFlag(enabled);
                dto.setEnabled(enabled);
                dto.setId(oldPo.getId());
                dto.setCreator(oldPo.getCreator());
                dto.setCreateTime(oldPo.getCreateTime());
                dto.setVersionValue(oldPo.getVersionValue());
                dto.setModifier(oldPo.getModifier());
                dto.setModifyTime(oldPo.getModifyTime());
                updateDtoS.add(dto);
            } else {
                String enabled = "Y".equals(dto.getEnableFlag()) ? YesOrNoEnum.YES.getCode() :
                        YesOrNoEnum.NO.getCode();
                dto.setEnabled(enabled);
                dto.setEnableFlag(enabled);
                insertDtoS.add(dto);
            }
        }
        if (CollectionUtils.isNotEmpty(insertDtoS)) {
            doCreateBatch(insertDtoS);
        }
        if (CollectionUtils.isNotEmpty(updateDtoS)) {
            doUpdateBatch(updateDtoS);
        }
        return BaseResponse.success();
    }

    @Override
    public BaseResponse<Void> syncChainLine(String tenantId,String scenario) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("scenario", scenario);
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.CHAIN_LINE.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CHAIN_LINE.getCode();
    }

    @Override
    public List<ChainLineInventoryLogVO> invocation(List<ChainLineInventoryLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
