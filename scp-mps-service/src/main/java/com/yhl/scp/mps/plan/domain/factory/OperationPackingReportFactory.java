package com.yhl.scp.mps.plan.domain.factory;

import com.yhl.scp.mps.plan.domain.entity.OperationPackingReportDO;
import com.yhl.scp.mps.plan.dto.OperationPackingReportDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationPackingReportDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>OperationPackingReportFactory</code>
 * <p>
 * OperationPackingReportFactory
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:42:06
 */
@Component
public class OperationPackingReportFactory {

    @Resource
    private OperationPackingReportDao operationPackingReportDao;

    OperationPackingReportDO create(OperationPackingReportDTO dto) {
        // TODO
        return null;
    }

}
