package com.yhl.scp.mps.plan.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OperationPackingReportPO</code>
 * <p>
 * OperationPackingReportPO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:42:06
 */
public class OperationPackingReportPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -54028243489308422L;

    /**
     * 制造订单ID
     */
    private String orderId;
    /**
     * 工序id
     */
    private String operationId;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 完工数量
     */
    private BigDecimal finishedQuantity;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 流水线时间
     */
    private Integer lineTime;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOperationId() {
        return operationId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getFinishedQuantity() {
        return finishedQuantity;
    }

    public void setFinishedQuantity(BigDecimal finishedQuantity) {
        this.finishedQuantity = finishedQuantity;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getLineTime() {
        return lineTime;
    }

    public void setLineTime(Integer lineTime) {
        this.lineTime = lineTime;
    }

}
