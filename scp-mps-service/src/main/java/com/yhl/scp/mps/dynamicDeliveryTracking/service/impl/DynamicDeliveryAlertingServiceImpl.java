package com.yhl.scp.mps.dynamicDeliveryTracking.service.impl;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mps.config.WorkReportWebSocketHandler;
import com.yhl.scp.mps.dynamicDeliveryTracking.enums.AlertTypeEnum;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryAlertingService;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryTrackingTaskService;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.AlertMessageVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DynamicDeliveryAlertingService</code>
 * <p>
 * DynamicDeliveryAlertingService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20 15:14:36
 */
@Service
@Slf4j
public class DynamicDeliveryAlertingServiceImpl implements DynamicDeliveryAlertingService {

    private static final int REMINDER_MINUTES_AHEAD = 5;

    @Resource
    private WorkReportWebSocketHandler workReportWebSocketHandler;

    @Resource
    private DynamicDeliveryTrackingTaskService dynamicDeliveryTrackingTaskService;

    @Override
    public BaseResponse<AlertMessageVO> checkWorkPeriod(String scenario, String physicalResourceId, String taskId) {
        try {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            Date now = new Date();
            List<DynamicDeliveryTrackingTaskVO> list =
                    dynamicDeliveryTrackingTaskService.selectTaskGroupByPhysicalResourceId(physicalResourceId);
            List<DynamicDeliveryTrackingTaskVO> subTaskList =
                    dynamicDeliveryTrackingTaskService.selectSubTaskByPhysicalResourceId(physicalResourceId, taskId);
            if (CollectionUtils.isNotEmpty(list)) {
                AlertMessageVO alertMessageVO = handlePeriodAlertReturn(scenario, physicalResourceId, subTaskList, now);
                return BaseResponse.success(alertMessageVO);
            } else {
                String periodKey = CollectionUtils.isEmpty(subTaskList) ? "" :
                        DateUtils.dateToString(subTaskList.get(subTaskList.size() - 1).getStartTime(),
                                DateUtils.COMMON_TIME_STR1)
                                + "-" + DateUtils.dateToString(subTaskList.get(subTaskList.size() - 1).getEndTime(),
                                DateUtils.COMMON_TIME_STR1);
                AlertMessageVO alertMessageVO = this.cancelAlertReturn(periodKey);
                return BaseResponse.success(alertMessageVO);
            }
        } finally {
            DynamicDataSourceContextHolder.clearDataSource();
        }
    }

    /**
     * 每分钟检查一次报工状态
     */
    @Override
    // @Scheduled(fixedRate = 60 * 1000)
    @XxlJob("checkWorkPeriodsJob")
    public void checkWorkPeriods() {
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("win")) {
            return;
        }
        IpsNewFeign ipsNewFeign = SpringBeanUtils.getBean(IpsNewFeign.class);
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MPS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            log.info("租户下不存在MPS模块信息");
            return;
        }
        Date now = new Date();
        for (Scenario scenarioItem : scenarios) {
            String scenario = scenarioItem.getDataBaseName();
            log.info("场景 [{}] 下的所有消息准备发送", scenario);
            try {
                DynamicDataSourceContextHolder.setDataSource(scenario);
                DynamicDeliveryTrackingTaskService dynamicDeliveryTrackingTaskService =
                        SpringBeanUtils.getBean(DynamicDeliveryTrackingTaskService.class);
                List<DynamicDeliveryTrackingTaskVO> taskList =
                        dynamicDeliveryTrackingTaskService.selectTaskGroupByPhysicalResourceId(null);
                Map<String, DynamicDeliveryTrackingTaskVO> trackingTaskMap = taskList.stream().collect(Collectors
                        .toMap(DynamicDeliveryTrackingTaskVO::getPhysicalResourceId, Function.identity(),
                                (t1, t2) -> t2));
                trackingTaskMap.forEach((key, value) -> {
                    List<DynamicDeliveryTrackingTaskVO> subTaskList =
                            dynamicDeliveryTrackingTaskService.selectSubTaskByPhysicalResourceId(key, null);
                    handlePeriodAlert(scenario, key, subTaskList, now);
                });
            } finally {
                DynamicDataSourceContextHolder.clearDataSource();
            }
            log.info("场景 [{}] 下的所有消息已发送", scenario);
        }
    }

    private void handlePeriodAlert(String scenario, String physicalResourceId,
                                   List<DynamicDeliveryTrackingTaskVO> allPeriods, Date now) {
        if (CollectionUtils.isEmpty(allPeriods)) {
            return;
        }
        Map<String, List<DynamicDeliveryTrackingTaskVO>> taskGroup = allPeriods.stream().collect(Collectors
                .groupingBy(DynamicDeliveryTrackingTaskVO::getTaskId));
        DynamicDeliveryTrackingTaskVO minPeriod = allPeriods.stream().min(Comparator
                .comparing(DynamicDeliveryTrackingTaskVO::getStartTime)).orElseGet(null);
        allPeriods = taskGroup.get(minPeriod.getTaskId());
        for (int i = 0; i < allPeriods.size(); i++) {
            DynamicDeliveryTrackingTaskVO item = allPeriods.get(i);
            if (item.getStartTime().getTime() <= now.getTime() && item.getEndTime().getTime() >= now.getTime()) {
                item.setIsCurrent(true);
                if (i < allPeriods.size() - 1) {
                    DynamicDeliveryTrackingTaskVO nextItem = allPeriods.get(i + 1);
                    nextItem.setHasNext(true);
                }
                break;
            }
        }
        Optional<DynamicDeliveryTrackingTaskVO> currentOptional = allPeriods.stream().filter(x ->
                Objects.nonNull(x.getIsCurrent()) && x.getIsCurrent()).findFirst();
        DynamicDeliveryTrackingTaskVO currentPeriod = currentOptional.orElse(null);
        Optional<DynamicDeliveryTrackingTaskVO> nextOptional = allPeriods.stream().filter(x->
                        Objects.nonNull(x.getHasNext()) && x.getHasNext()).findFirst();
        DynamicDeliveryTrackingTaskVO nextPeriod = nextOptional.orElse(null);
        if (currentPeriod != null && nextPeriod != null) {
            Date startTime = currentPeriod.getStartTime();
            Date endTime = currentPeriod.getEndTime();
            String periodKey = DateUtils.dateToString(startTime, DateUtils.COMMON_TIME_STR1)
                    + "-" + DateUtils.dateToString(endTime, DateUtils.COMMON_TIME_STR1);
            BigDecimal plannedQuantity = currentPeriod.getPlannedQuantity();
            BigDecimal finishedQuantity = currentPeriod.getFinishedQuantity();

            Date alertTime = DateUtils.moveCalendar(endTime, Calendar.MINUTE, REMINDER_MINUTES_AHEAD);
            boolean shouldAlert = alertTime.getTime() <= now.getTime() && endTime.getTime() >= now.getTime()
                    && plannedQuantity.compareTo(finishedQuantity) > 0;
            if (shouldAlert) {
                // 触发告警
                triggerAlert(scenario, physicalResourceId, periodKey, AlertTypeEnum.YELLOW_ALERT.getCode());
            } else {
                // 取消告警
                clearCurrentAlert(scenario, physicalResourceId, periodKey);
            }
        } else {
            DynamicDeliveryTrackingTaskVO lastPeriod = allPeriods.get(allPeriods.size() - 1);
            Date startTime = lastPeriod.getStartTime();
            Date endTime = lastPeriod.getEndTime();
            String periodKey = DateUtils.dateToString(startTime, DateUtils.COMMON_TIME_STR1)
                    + "-" + DateUtils.dateToString(endTime, DateUtils.COMMON_TIME_STR1);
            Date alertTime = DateUtils.moveCalendar(endTime, Calendar.MINUTE, REMINDER_MINUTES_AHEAD);
            boolean shouldAlert = (alertTime.getTime() <= now.getTime() && endTime.getTime() >= now.getTime())
                    || (endTime.getTime() <= now.getTime());
            if (shouldAlert) {
                triggerAlert(scenario, physicalResourceId, periodKey, AlertTypeEnum.RED_ALERT.getCode());
            }
        }
    }

    private AlertMessageVO handlePeriodAlertReturn(String scenario, String physicalResourceId,
                                   List<DynamicDeliveryTrackingTaskVO> allPeriods, Date now) {
        if (CollectionUtils.isEmpty(allPeriods)) {
            return errorAlertReturn(scenario, physicalResourceId, "");
        }
        for (int i = 0; i < allPeriods.size(); i++) {
            DynamicDeliveryTrackingTaskVO item = allPeriods.get(i);
            if (item.getStartTime().getTime() <= now.getTime() && item.getEndTime().getTime() >= now.getTime()) {
                item.setIsCurrent(true);
                if (i < allPeriods.size() - 1) {
                    DynamicDeliveryTrackingTaskVO nextItem = allPeriods.get(i + 1);
                    nextItem.setHasNext(true);
                }
                break;
            }
        }
        Optional<DynamicDeliveryTrackingTaskVO> currentOptional = allPeriods.stream().filter(x ->
                Objects.nonNull(x.getIsCurrent()) && x.getIsCurrent()).findFirst();
        DynamicDeliveryTrackingTaskVO currentPeriod = currentOptional.orElse(null);
        Optional<DynamicDeliveryTrackingTaskVO> nextOptional = allPeriods.stream().filter(x->
                Objects.nonNull(x.getHasNext()) && x.getHasNext()).findFirst();
        DynamicDeliveryTrackingTaskVO nextPeriod = nextOptional.orElse(null);
        if (currentPeriod != null && nextPeriod != null) {
            Date startTime = currentPeriod.getStartTime();
            Date endTime = currentPeriod.getEndTime();
            String periodKey = DateUtils.dateToString(startTime, DateUtils.COMMON_TIME_STR1)
                    + "-" + DateUtils.dateToString(endTime, DateUtils.COMMON_TIME_STR1);
            BigDecimal plannedQuantity = currentPeriod.getPlannedQuantity();
            BigDecimal finishedQuantity = currentPeriod.getFinishedQuantity();

            Date alertTime = DateUtils.moveCalendar(endTime, Calendar.MINUTE, REMINDER_MINUTES_AHEAD);
            boolean shouldAlert = alertTime.getTime() <= now.getTime() && endTime.getTime() >= now.getTime()
                    && plannedQuantity.compareTo(finishedQuantity) > 0;
            if (shouldAlert) {
                // 触发告警
                return triggerAlertReturn(periodKey, AlertTypeEnum.YELLOW_ALERT.getCode());
            } else {
                // 取消告警
                return clearCurrentAlertReturn(periodKey);
            }
        } else {
            DynamicDeliveryTrackingTaskVO lastPeriod = allPeriods.get(allPeriods.size() - 1);
            Date startTime = lastPeriod.getStartTime();
            Date endTime = lastPeriod.getEndTime();
            String periodKey = DateUtils.dateToString(startTime, DateUtils.COMMON_TIME_STR1)
                    + "-" + DateUtils.dateToString(endTime, DateUtils.COMMON_TIME_STR1);
            Date alertTime = DateUtils.moveCalendar(endTime, Calendar.MINUTE, REMINDER_MINUTES_AHEAD);
            boolean shouldAlert = (alertTime.getTime() <= now.getTime() && endTime.getTime() >= now.getTime())
                    || (endTime.getTime() <= now.getTime());
            if (shouldAlert) {
                return triggerAlertReturn(periodKey, AlertTypeEnum.RED_ALERT.getCode());
            } else {
                return cancelAlertReturn(periodKey);
            }
        }
    }

    /**
     * 企业微信报工
     *
     * @param scenario           场景
     * @param physicalResourceId 物理资源ID
     * @param periodKey          时段键
     * @param current            任务对象
     */
    @Override
    public void processWorkReport(String scenario, String physicalResourceId, String periodKey,
                                  DynamicDeliveryTrackingSubTaskVO current) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        if (Objects.isNull(current)) {
            errorAlert(scenario, physicalResourceId, periodKey);
            return;
        }
        String taskId = current.getTaskId();
        DynamicDeliveryTrackingTaskService dynamicDeliveryTrackingTaskService =
                SpringBeanUtils.getBean(DynamicDeliveryTrackingTaskService.class);
        List<DynamicDeliveryTrackingTaskVO> allPeriods =
                dynamicDeliveryTrackingTaskService.selectSubTaskByPhysicalResourceId(null, taskId);
        if (CollectionUtils.isEmpty(allPeriods)) {
            return;
        }
        Date now = new Date();
        for (int i = 0; i < allPeriods.size(); i++) {
            DynamicDeliveryTrackingTaskVO item = allPeriods.get(i);
            if (item.getStartTime().getTime() <= now.getTime() && item.getEndTime().getTime() >= now.getTime()) {
                item.setIsCurrent(true);
                if (i < allPeriods.size() - 1) {
                    DynamicDeliveryTrackingTaskVO nextItem = allPeriods.get(i + 1);
                    nextItem.setHasNext(true);
                }
                break;
            }
        }
        // 设置当前时段的完成数量
        for (DynamicDeliveryTrackingTaskVO period : allPeriods) {
            Date startTime = period.getStartTime();
            Date endTime = period.getEndTime();
            String periodKey1 = DateUtils.dateToString(startTime, DateUtils.COMMON_TIME_STR1)
                    + "-" + DateUtils.dateToString(endTime, DateUtils.COMMON_TIME_STR1);
            if (periodKey.equals(periodKey1)) {
                period.setFinishedQuantity(current.getFinishedQuantity());
                break;
            }
        }
        Optional<DynamicDeliveryTrackingTaskVO> currentOptional = allPeriods.stream().filter(x ->
                Objects.nonNull(x.getIsCurrent()) && x.getIsCurrent()).findFirst();
        DynamicDeliveryTrackingTaskVO currentPeriod = currentOptional.orElse(null);
        Optional<DynamicDeliveryTrackingTaskVO> nextOptional = allPeriods.stream().filter(x->
                Objects.nonNull(x.getHasNext()) && x.getHasNext()).findFirst();
        DynamicDeliveryTrackingTaskVO nextPeriod = nextOptional.orElse(null);
        if (currentPeriod != null && nextPeriod != null) {
            Date endTime = currentPeriod.getEndTime();
            Date alertTime = DateUtils.moveCalendar(endTime, Calendar.MINUTE, REMINDER_MINUTES_AHEAD);
            boolean clearCurrentAlert = alertTime.getTime() <= now.getTime() && alertTime.getTime() >= now.getTime()
                    && current.getPlannedQuantity().compareTo(current.getFinishedQuantity()) <= 0;
            if (clearCurrentAlert) {
                // 清除当前告警
                clearCurrentAlert(scenario, physicalResourceId, periodKey);
            }
        } else {
            BigDecimal totalPlannedQty = allPeriods.stream().map(DynamicDeliveryTrackingTaskVO::getPlannedQuantity)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalFinishedQty = allPeriods.stream().map(DynamicDeliveryTrackingTaskVO::getFinishedQuantity)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            DynamicDeliveryTrackingTaskVO lastPeriod = allPeriods.get(allPeriods.size() - 1);
            Date endTime = lastPeriod.getEndTime();
            Date alertTime = DateUtils.moveCalendar(endTime, Calendar.MINUTE, REMINDER_MINUTES_AHEAD);
            boolean cancelAlert = (alertTime.getTime() <= now.getTime() && endTime.getTime() >= now.getTime())
                    || (endTime.getTime() <= now.getTime()) && totalPlannedQty.compareTo(totalFinishedQty) <= 0;
            if (cancelAlert) {
                // 取消告警
                cancelAlert(scenario, physicalResourceId, periodKey);
            }
        }
        DynamicDataSourceContextHolder.clearDataSource();
    }

    /**
     * 触发告警
     *
     * @param scenario           场景
     * @param physicalResourceId 物理资源ID
     * @param periodKey          时段键
     * @param alertType          告警类型
     */
    private void triggerAlert(String scenario, String physicalResourceId, String periodKey, String alertType) {
        AlertMessageVO alert = new AlertMessageVO(alertType, "时段 [" + periodKey + "] 报工未达标，请及时处理！",
                periodKey, true);
        String dataRange = String.join(Constants.DELIMITER, scenario, physicalResourceId);
        log.info("报工告警: " + alert.getMessage());
        workReportWebSocketHandler.sendMessageToUser(dataRange, alert);
    }

    /**
     * 触发告警
     *
     * @param periodKey          时段键
     * @param alertType          告警类型
     */
    public AlertMessageVO triggerAlertReturn(String periodKey, String alertType) {
        AlertMessageVO alert = new AlertMessageVO(alertType, "时段 [" + periodKey + "] 报工未达标，请及时处理！",
                periodKey, true);
        log.info("报工告警: " + alert.getMessage());
        return alert;
    }

    /**
     * 报工告警取消
     *
     * @param scenario           场景
     * @param physicalResourceId 物理资源ID
     * @param periodKey          时段键
     */
    private void cancelAlert(String scenario, String physicalResourceId, String periodKey) {
        AlertMessageVO alert = new AlertMessageVO(AlertTypeEnum.NORMAL.getCode(),
                "时段 [" + periodKey + "] 报工已达标！", periodKey, false);
        String dataRange = String.join(Constants.DELIMITER, scenario, physicalResourceId);
        log.info("报工告警取消: " + alert.getMessage());
        workReportWebSocketHandler.sendMessageToUser(dataRange, alert);
    }

    /**
     * 报工告警取消
     *
     * @param periodKey          时段键
     */
    public AlertMessageVO cancelAlertReturn(String periodKey) {
        AlertMessageVO alert = new AlertMessageVO(AlertTypeEnum.NORMAL.getCode(),
                "时段 [" + periodKey + "] 报工已达标！", periodKey, false);
        log.info("报工告警取消: " + alert.getMessage());
        return alert;
    }

    /**
     * 清除当前时段的告警
     *
     * @param scenario           场景
     * @param physicalResourceId 物理资源ID
     * @param periodKey          时段键
     */
    private void clearCurrentAlert(String scenario, String physicalResourceId, String periodKey) {
        AlertMessageVO alert = new AlertMessageVO(AlertTypeEnum.NORMAL.getCode(),
                "时段 [" + periodKey + "] 告警已取消！", periodKey, false);
        String dataRange = String.join(Constants.DELIMITER, scenario, physicalResourceId);
        log.info("清除当前时段的告警: " + alert.getMessage());
        workReportWebSocketHandler.sendMessageToUser(dataRange, alert);
    }

    /**
     * 清除当前时段的告警
     *
     * @param periodKey          时段键
     */
    public AlertMessageVO clearCurrentAlertReturn(String periodKey) {
        AlertMessageVO alert = new AlertMessageVO(AlertTypeEnum.NORMAL.getCode(),
                "时段 [" + periodKey + "] 告警已取消！", periodKey, false);
        log.info("清除当前时段的告警: " + alert.getMessage());
        return alert;
    }

    /**
     * 清除当前时段的告警
     *
     * @param scenario           场景
     * @param physicalResourceId 物理资源ID
     * @param periodKey          时段键
     */
    private void errorAlert(String scenario, String physicalResourceId, String periodKey) {
        AlertMessageVO alert = new AlertMessageVO(AlertTypeEnum.ERROR.getCode(), "报工逻辑有问题，系统错误！");
        String dataRange = String.join(Constants.DELIMITER, scenario, physicalResourceId);
        log.error("报工逻辑有问题，系统错误，数据坐标：{}！", String.join(Constants.DELIMITER, scenario, physicalResourceId, periodKey));
        workReportWebSocketHandler.sendMessageToUser(dataRange, alert);
    }

    /**
     * 清除当前时段的告警
     *
     * @param scenario           场景
     * @param physicalResourceId 物理资源ID
     * @param periodKey          时段键
     */
    public AlertMessageVO errorAlertReturn(String scenario, String physicalResourceId, String periodKey) {
        AlertMessageVO alert = new AlertMessageVO(AlertTypeEnum.ERROR.getCode(), "报工逻辑有问题，系统错误！");
        log.error("报工逻辑有问题，系统错误，数据坐标：{}！", String.join(Constants.DELIMITER, scenario, physicalResourceId, periodKey));
        return alert;
    }

    @Override
    public void testSendMessage(String userId, String message) {
        workReportWebSocketHandler.sendMessageToUser(userId, message);
    }

    public void testSendMessageToGroup(List<String> userIds, String message) {
        workReportWebSocketHandler.sendMessageToGroups(userIds, message);
    }
}