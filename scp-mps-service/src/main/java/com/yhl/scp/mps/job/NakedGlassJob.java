package com.yhl.scp.mps.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mps.nakedGlass.service.NakedGlassService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * description: 获取裸玻成品映射
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/27
 */
@Component
@Slf4j
public class NakedGlassJob {
    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NakedGlassService nakedGlassService;

    @XxlJob("nakedGlassJob")
    public ReturnT<String> nakedGlassJobHandler() {
        XxlJobHelper.log("开始同步裸玻成品映射");
        List<Scenario> data = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MPS.getCode()).getData();
        if (data.isEmpty()) {
            XxlJobHelper.log("租户不存在该数据");
            return ReturnT.FAIL;
        }
        for (Scenario scenario : data) {
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            try {
                if (StringUtils.isNotEmpty(scenario.getTenantId())) {
                    nakedGlassService.syncData(scenario.getTenantId(),scenario.getDataBaseName());
                }
            } catch (Exception e) {
                XxlJobHelper.log("裸玻成品映射定时任务报错{}。", e.getMessage());
                return ReturnT.FAIL;
            }
        }
        XxlJobHelper.log("结束同步主资源");
        return ReturnT.SUCCESS;
    }

}
