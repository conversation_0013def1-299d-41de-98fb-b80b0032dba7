package com.yhl.scp.mps.capacityBalance.service.impl;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.util.PageUtils;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.routing.vo.RoutingStepResourceBasicVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepResourceVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mps.capacityBalance.convertor.CapacitySupplyRelationshipConvertor;
import com.yhl.scp.mps.capacityBalance.domain.entity.CapacitySupplyRelationshipDO;
import com.yhl.scp.mps.capacityBalance.domain.entity.CapacitySupplyRelationshipEditorDataDO;
import com.yhl.scp.mps.capacityBalance.domain.service.CapacitySupplyRelationshipDomainService;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipDTO;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipEditorDTO;
import com.yhl.scp.mps.capacityBalance.dto.OutsourcingAdjustmentDTO;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceRule;
import com.yhl.scp.mps.capacityBalance.enums.LockStatusEnum;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipPO;
import com.yhl.scp.mps.capacityBalance.service.CapacityLoadService;
import com.yhl.scp.mps.capacityBalance.service.CapacitySupplyRelationshipReadOnlyService;
import com.yhl.scp.mps.capacityBalance.service.CapacitySupplyRelationshipService;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.demand.convertor.OutsourceTransferSummaryConvertor;
import com.yhl.scp.mps.demand.service.OutsourceTransferSummaryService;
import com.yhl.scp.mps.enums.ChangeTypeEnum;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.enums.SupplyModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CapacitySupplyRelationshipServiceImpl</code>
 * <p>
 * 产能供应关系应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 15:41:29
 */
@Slf4j
@Service
public class CapacitySupplyRelationshipServiceImpl extends AbstractService implements CapacitySupplyRelationshipService {

    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;
    @Resource
    private OutsourceTransferSummaryService outsourceTransferSummaryService;

    @Resource
    private CapacitySupplyRelationshipDomainService capacitySupplyRelationshipDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;


    @Resource
    private CapacityLoadService capacityLoadService;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private CapacitySupplyRelationshipReadOnlyService capacitySupplyRelationshipReadOnlyService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(CapacitySupplyRelationshipDTO capacitySupplyRelationshipDTO) {
        // 0.数据转换
        CapacitySupplyRelationshipDO capacitySupplyRelationshipDO = CapacitySupplyRelationshipConvertor.INSTANCE.dto2Do(capacitySupplyRelationshipDTO);
        CapacitySupplyRelationshipPO capacitySupplyRelationshipPO = CapacitySupplyRelationshipConvertor.INSTANCE.dto2Po(capacitySupplyRelationshipDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        capacitySupplyRelationshipDomainService.validation(capacitySupplyRelationshipDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(capacitySupplyRelationshipPO);
        capacitySupplyRelationshipDao.insert(capacitySupplyRelationshipPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(CapacitySupplyRelationshipDTO capacitySupplyRelationshipDTO) {
        // 0.数据转换
        CapacitySupplyRelationshipDO capacitySupplyRelationshipDO = CapacitySupplyRelationshipConvertor.INSTANCE.dto2Do(capacitySupplyRelationshipDTO);
        CapacitySupplyRelationshipPO capacitySupplyRelationshipPO = CapacitySupplyRelationshipConvertor.INSTANCE.dto2Po(capacitySupplyRelationshipDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        capacitySupplyRelationshipDomainService.validation(capacitySupplyRelationshipDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(capacitySupplyRelationshipPO);
        capacitySupplyRelationshipDao.update(capacitySupplyRelationshipPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CapacitySupplyRelationshipDTO> list) {
        List<CapacitySupplyRelationshipPO> newList = CapacitySupplyRelationshipConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        capacitySupplyRelationshipDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<CapacitySupplyRelationshipDTO> list) {
        List<CapacitySupplyRelationshipPO> newList = CapacitySupplyRelationshipConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        capacitySupplyRelationshipDao.updateBatch(newList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        List<CapacitySupplyRelationshipPO> pos = capacitySupplyRelationshipDao.selectByPrimaryKeys(idList);
        List<CapacitySupplyRelationshipPO> collect = pos.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getImportFlag())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)){
            throw new BusinessException("导入数据不允许删除");
        }
        int deleteCount;
        if (idList.size() > 1) {
            deleteCount = capacitySupplyRelationshipDao.deleteBatch(idList);
        }else {
            deleteCount = capacitySupplyRelationshipDao.deleteByPrimaryKey(idList.get(0));
        }
        capacityLoadService.saveCapacityLoadBasedOnVersionNew();
        capacitySupplyRelationshipReadOnlyService.doRefresh();
        return deleteCount;
    }

    @Override
    public CapacitySupplyRelationshipVO selectByPrimaryKey(String id) {
        CapacitySupplyRelationshipPO po = capacitySupplyRelationshipDao.selectByPrimaryKey(id);
        return CapacitySupplyRelationshipConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "CAPACITY_SUPPLY_RELATIONSHIP")
    public List<CapacitySupplyRelationshipVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        // PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "CAPACITY_SUPPLY_RELATIONSHIP")
    public List<CapacitySupplyRelationshipVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        if (queryCriteriaParam.contains("supply_time")) {
            queryCriteriaParam = queryCriteriaParam.replace("supply_time", "CONCAT(DATE_FORMAT(supply_time, '%Y-%m'), '-01')");
        }
        if (queryCriteriaParam.contains("forecast_time")){
            queryCriteriaParam = queryCriteriaParam.replace("forecast_time", "CONCAT(DATE_FORMAT(forecast_time, '%Y-%m'), '-01')");
        }
        List<CapacitySupplyRelationshipVO> dataList = capacitySupplyRelationshipDao.selectByCondition(sortParam, queryCriteriaParam);
        CapacitySupplyRelationshipServiceImpl target = springBeanUtils.getBean(CapacitySupplyRelationshipServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CapacitySupplyRelationshipVO> selectByParams(Map<String, Object> params) {
        List<CapacitySupplyRelationshipPO> list = capacitySupplyRelationshipDao.selectByParams(params);
        return CapacitySupplyRelationshipConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CapacitySupplyRelationshipVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CAPACITY_SUPPLY_RELATIONSHIP.getCode();
    }

    @Override
    public List<CapacitySupplyRelationshipVO> invocation(List<CapacitySupplyRelationshipVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void doSaveCapacitySupplyRelationshipBasedOnVersion(String versionId) {
        if (StringUtils.isEmpty(versionId)) {
            return;
        }
        String uuid = UUIDUtil.getUUID();
        // 调用存储过程
        capacitySupplyRelationshipDao.saveCapacitySupplyRelationshipOnVersion(versionId, uuid, new Date(), SystemHolder.getUserId());

        // 保存一版最新数据
        // List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = CapacitySupplyRelationshipConvertor.INSTANCE.vos2Pos(capacitySupplyRelationshipVOList);
        // if (CollectionUtils.isEmpty(capacitySupplyRelationshipVOList)) {
        //     return;
        // }
        // for (CapacitySupplyRelationshipPO capacitySupplyRelationshipPO : capacitySupplyRelationshipPOList) {
        //     capacitySupplyRelationshipPO.setId(UUIDUtil.getUUID());
        //     capacitySupplyRelationshipPO.setVersionId(versionId);
        //     BasePOUtils.insertFiller(capacitySupplyRelationshipPO);
        // }
        // List<List<CapacitySupplyRelationshipPO>> lists = com.yhl.platform.common.utils.CollectionUtils.splitList(capacitySupplyRelationshipPOList, 5000);
        // for (List<CapacitySupplyRelationshipPO> list : lists) {
        //     capacitySupplyRelationshipDao.insertBatchWithPrimaryKey(list);
        // }
    }

    @Override
    public void doUpdateVersionIds(String versionId, List<String> list) {
        if (CollectionUtils.isEmpty(list) || StringUtils.isEmpty(versionId)){
            return;
        }
        capacitySupplyRelationshipDao.updateVersionIds(versionId, list);
    }

    @Override
    public void lockStatusBatchNew(List<CapacitySupplyRelationshipVO> voList, String lockStatus){
        if (CollectionUtils.isEmpty(voList)){
            throw new BusinessException("参数不能为空");
        }
        for (CapacitySupplyRelationshipVO vo : voList) {
            lockCheck(vo);
        }
        if (StringUtils.isEmpty(lockStatus)){
            throw  new BusinessException("锁状态值不能为空");
        }
        boolean exists = Arrays.stream(LockStatusEnum.values()).anyMatch(enumValue -> enumValue.getCode().equals(lockStatus));
        if (!exists){
            throw new BusinessException("锁状态值不存在");
        }
        List<String> operationCodeList = voList.stream().map(CapacitySupplyRelationshipVO::getOperationCode).distinct().collect(Collectors.toList());
        List<CollectionValueVO> specialTechnology = ipsFeign.getByCollectionCode("SPECIAL_TECHNOLOGY");
        List<String> specialTechnologyList = specialTechnology.stream().map(CollectionValueVO::getValueMeaning).collect(Collectors.toList());
        List<String> specialOperationCodeList = operationCodeList.stream().filter(specialTechnologyList::contains).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(specialOperationCodeList)){
            throw new BusinessException("特殊工艺不允许修改锁定状态");
        }

        List<String> productCodeList = voList.stream().map(CapacitySupplyRelationshipVO::getProductCode).distinct().collect(Collectors.toList());
        List<String> supplyMonthList = voList.stream().map(CapacitySupplyRelationshipVO::getSupplyMonth).distinct().collect(Collectors.toList());
        supplyMonthList = supplyMonthList.stream().distinct().collect(Collectors.toList());
        List<String> resourceCodeList = voList.stream().map(CapacitySupplyRelationshipVO::getResourceCode).distinct().collect(Collectors.toList());
        resourceCodeList = resourceCodeList.stream().distinct().collect(Collectors.toList());

        Map<String, Object> params = new HashMap<>();
        params.put("productCodeList", productCodeList);
        params.put("latestFlag", true);
        params.put("operationCode", operationCodeList.get(0));
        params.put("supplyMonthList", supplyMonthList);
        params.put("resourceCodeList", resourceCodeList);
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = capacitySupplyRelationshipDao.selectByParams(params);
        if (CollectionUtils.isEmpty(capacitySupplyRelationshipPOList)){
            throw new BusinessException("数据不存在");
        }
        List<CapacitySupplyRelationshipPO> updateList = new ArrayList<>();
        for (CapacitySupplyRelationshipVO dto : voList) {
            List<CapacitySupplyRelationshipPO> sourceDataList = capacitySupplyRelationshipPOList.stream()
                    .filter(t -> dto.getSupplyMonth().equals(t.getSupplyMonth()) && dto.getResourceCode().equals(t.getResourceCode())
                            && dto.getRule().equals(t.getRule()) && dto.getSupplyModel().equals(t.getSupplyModel()) && dto.getLockStatus().equals(t.getLockStatus()))
                    .collect(Collectors.toList());
            sourceDataList.forEach(
                    t->t.setLockStatus(lockStatus)
            );
            updateList.addAll(sourceDataList);
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            BasePOUtils.updateBatchFiller(updateList);
            capacitySupplyRelationshipDao.updateBatch(updateList);
        }
        capacitySupplyRelationshipReadOnlyService.doRefresh();
    }


    public void doLineEditNew(CapacitySupplyRelationshipEditorDTO dto){
        //参数校验
        check(dto);
        //特殊工艺校验
        checkSpecialOperation(dto.getOperationCode());

        List<RoutingStepResourceVO> stepResourceVOS = newMdsFeign.selectRoutingStepResourceByRoutingStepIds(SystemHolder.getScenario(), Collections.singletonList(dto.getRoutingStepId()));
        Map<String, RoutingStepResourceVO> routingStepResourceVOMap = stepResourceVOS.stream()
                .collect(Collectors.toMap(t -> t.getRoutingStepId() + "-" + t.getPhysicalResourceId(), Function.identity(), (v1, v2) -> v2));
        String resourceKey = dto.getRoutingStepId()+ "-" +dto.getResourceId();
        RoutingStepResourceVO routingStepResourceVO = routingStepResourceVOMap.get(resourceKey);
        String newResourceCode = routingStepResourceVO.getPhysicalResourceCode();
        //供应月份变化
        boolean monthChange = !dto.getNewSupplyMonth().equals(dto.getSupplyMonth());
        //资源变化
        boolean resourceChange = !dto.getResourceCode().equals(newResourceCode);
        //数量变化
        boolean qtyChange = dto.getNewSupplyQuantity().compareTo(dto.getSupplyQuantity()) != 0;
        if (!monthChange && !resourceChange && !qtyChange){
            return;
        }
        if (!monthChange && !resourceChange && qtyChange){
            throw new BusinessException("在资源，供应月份不变时，数量不允许修改！");
        }
        if (dto.getNewSupplyQuantity().compareTo(dto.getSupplyQuantity()) > 0){
            throw new BusinessException("修改后的数量不能大于原数量！");
        }
        //这里传入的是汇总之后的数据，要获取原数据
        Map<String, Object> params = new HashMap<>();
        params.put("productCode", dto.getProductCode());
        params.put("latestFlag", true);
        params.put("operationCode", dto.getOperationCode());
        params.put("supplyMonthList", Arrays.asList(dto.getSupplyMonth(), dto.getNewSupplyMonth()));
        params.put("resourceCodeList", Arrays.asList(dto.getResourceCode(), newResourceCode));
        params.put("supplyModel", dto.getSupplyModel());
        params.put("lockStatus", dto.getLockStatus());
        params.put("rule", dto.getRule());
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = capacitySupplyRelationshipDao.selectByParams(params);
        if (CollectionUtils.isEmpty(capacitySupplyRelationshipPOList)){
            throw new BusinessException("数据不存在");
        }
        List<CapacitySupplyRelationshipPO> sourceDataList = capacitySupplyRelationshipPOList.stream()
                .filter(t -> dto.getSupplyMonth().equals(t.getSupplyMonth()) && dto.getResourceCode().equals(t.getResourceCode()))
                .collect(Collectors.toList());

        List<CapacitySupplyRelationshipPO> updateList = new ArrayList<>();
        List<CapacitySupplyRelationshipPO> insertList = new ArrayList<>();
        BigDecimal abs = dto.getNewSupplyQuantity().abs();
        BigDecimal var = BigDecimal.ZERO;
        if (qtyChange){
            for (CapacitySupplyRelationshipPO relationshipPO : sourceDataList) {
                if (abs.compareTo(relationshipPO.getSupplyQuantity()) > 0){
                    var = var.add(relationshipPO.getSupplyQuantity());
                    abs = abs.subtract(relationshipPO.getSupplyQuantity());
                }else {
                    CapacitySupplyRelationshipPO insert = new CapacitySupplyRelationshipPO();
                    BeanUtils.copyProperties(relationshipPO, insert);
                    insert.setSupplyQuantity(relationshipPO.getSupplyQuantity().subtract(abs));
                    insert.setId(UUIDUtil.getUUID());
                    insertList.add(insert);
                    relationshipPO.setSupplyQuantity(abs);
                    var = var.add(abs);
                    abs = BigDecimal.ZERO;
                }
                updateList.add(relationshipPO);
                if (abs.compareTo(BigDecimal.ZERO) == 0){
                    break;
                }
            }
        }else {
            updateList = sourceDataList;
        }
        //供应月份发生变化
        handleMonthOrResourceChange(dto, updateList, routingStepResourceVO, newResourceCode, monthChange, resourceChange);
        //更新数据
        commonDataUpdate(insertList, updateList, null);
        //刷新负荷数据
        capacityLoadService.saveCapacityLoadBasedOnVersionNew();
        // 刷新只读表数据
        capacitySupplyRelationshipReadOnlyService.doRefresh();
    }

    public void doBatchEdit(List<CapacitySupplyRelationshipEditorDTO> dtoList){
        if (CollectionUtils.isEmpty(dtoList)){
            throw new BusinessException("参数不能为空");
        }
        for (CapacitySupplyRelationshipEditorDTO dto : dtoList) {
            check(dto);
        }
        List<CapacitySupplyRelationshipEditorDTO> outDataList = dtoList.stream().filter(t -> SupplyModelEnum.OUTSOURCED.getCode().equals(t.getSupplyModel())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outDataList)){
            throw new BusinessException("委外数据不允许批量修改");
        }
        List<String> operationCodeList = dtoList.stream().map(CapacitySupplyRelationshipEditorDTO::getOperationCode).distinct().collect(Collectors.toList());
        if (operationCodeList.size()>1){
            throw new BusinessException("勾选数据工序必须相同");
        }
        //特殊工艺校验
        checkSpecialOperation(operationCodeList.get(0));

        List<String> routingStepIds = dtoList.stream().map(CapacitySupplyRelationshipEditorDTO::getRoutingStepId).distinct().collect(Collectors.toList());
        List<RoutingStepResourceVO> stepResourceVOS = newMdsFeign.selectRoutingStepResourceByRoutingStepIds(SystemHolder.getScenario(),routingStepIds);
        List<String> routingStepResourceCodeList = stepResourceVOS.stream().map(RoutingStepResourceVO::getPhysicalResourceCode).distinct().collect(Collectors.toList());

        Map<String, RoutingStepResourceVO> routingStepResourceVOMap = stepResourceVOS.stream()
                .collect(Collectors.toMap(t -> t.getRoutingStepId() + "-" + t.getPhysicalResourceId(), Function.identity(), (v1, v2) -> v2));
        List<String> productCodeList = dtoList.stream().map(CapacitySupplyRelationshipEditorDTO::getProductCode).distinct().collect(Collectors.toList());
        List<String> supplyMonthList = dtoList.stream().map(CapacitySupplyRelationshipEditorDTO::getSupplyMonth).distinct().collect(Collectors.toList());
        supplyMonthList.addAll(dtoList.stream().map(CapacitySupplyRelationshipEditorDTO::getNewSupplyMonth).distinct().collect(Collectors.toList()));
        supplyMonthList = supplyMonthList.stream().distinct().collect(Collectors.toList());
        List<String> resourceCodeList = dtoList.stream().map(CapacitySupplyRelationshipEditorDTO::getResourceCode).distinct().collect(Collectors.toList());
        resourceCodeList.addAll(routingStepResourceCodeList);
        resourceCodeList = resourceCodeList.stream().distinct().collect(Collectors.toList());


        Map<String, Object> params = new HashMap<>();
        params.put("productCodeList", productCodeList);
        params.put("latestFlag", true);
        params.put("operationCode", operationCodeList.get(0));
        params.put("supplyMonthList", supplyMonthList);
        params.put("resourceCodeList", resourceCodeList);
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = capacitySupplyRelationshipDao.selectByParams(params);
        if (CollectionUtils.isEmpty(capacitySupplyRelationshipPOList)){
            throw new BusinessException("数据不存在");
        }
        List<CapacitySupplyRelationshipPO> updateList = new ArrayList<>();
        for (CapacitySupplyRelationshipEditorDTO dto : dtoList) {
            List<CapacitySupplyRelationshipPO> sourceDataList = capacitySupplyRelationshipPOList.stream()
                    .filter(t -> dto.getSupplyMonth().equals(t.getSupplyMonth()) && dto.getResourceCode().equals(t.getResourceCode())
                    && dto.getRule().equals(t.getRule()) && dto.getSupplyModel().equals(t.getSupplyModel()) && dto.getLockStatus().equals(t.getLockStatus()))
                    .collect(Collectors.toList());

            String resourceKey = dto.getRoutingStepId()+ "-" +dto.getResourceId();
            RoutingStepResourceVO routingStepResourceVO = routingStepResourceVOMap.get(resourceKey);
            String newResourceCode = routingStepResourceVO.getPhysicalResourceCode();
            //供应月份变化
            boolean monthChange = !dto.getNewSupplyMonth().equals(dto.getSupplyMonth());
            //资源变化
            boolean resourceChange = !dto.getResourceCode().equals(newResourceCode);

            //供应月份发生变化
            handleMonthOrResourceChange(dto, sourceDataList, routingStepResourceVO, newResourceCode, monthChange, resourceChange);
            updateList.addAll(sourceDataList);
        }
        //更新数据
        commonDataUpdate(null, updateList, null);
        //刷新负荷数据
        capacityLoadService.saveCapacityLoadBasedOnVersionNew();

        capacitySupplyRelationshipReadOnlyService.doRefresh();
    }

    private void handleMonthOrResourceChange(CapacitySupplyRelationshipEditorDTO dto,
                                             List<CapacitySupplyRelationshipPO> sourceDataList,
                                             RoutingStepResourceVO routingStepResourceVO,
                                             String newResourceCode, boolean monthChange, boolean resourceChange) {
        if (monthChange){
            //把updateList里所有数据的供应月份月份改为新的供应月份
            for (CapacitySupplyRelationshipPO relationshipPO : sourceDataList) {
                //这里供应时间会被更新成新供应月份的1号
                relationshipPO.setSupplyTime(DateUtils.stringToDate(dto.getNewSupplyMonth(), DateUtils.YEAR_MONTH));
                relationshipPO.setSupplyMonth(dto.getNewSupplyMonth());
            }
        }
        if (resourceChange){
            for (CapacitySupplyRelationshipPO relationshipPO : sourceDataList) {
                relationshipPO.setResourceCode(newResourceCode);
                relationshipPO.setResourceName(routingStepResourceVO.getPhysicalResourceName());
                relationshipPO.setBeat(routingStepResourceVO.getUnitProductionTime().toString());
            }
        }
    }

    private void check(CapacitySupplyRelationshipEditorDTO dto){
        if (dto == null){
            throw new BusinessException("参数不能为空");
        }
        if (StringUtils.isEmpty(dto.getId())){
            throw new BusinessException("原数据主键不能为空");
        }
        if (StringUtils.isEmpty(dto.getProductCode())){
            throw new BusinessException("产品编码不能为空");
        }
        if (StringUtils.isEmpty(dto.getOperationCode())){
            throw new BusinessException("工序编码不能为空");
        }
        if (StringUtils.isEmpty(dto.getForecastMonth())){
            throw new BusinessException("需求月份不能为空");
        }
        if (StringUtils.isEmpty(dto.getSupplyMonth())){
            throw new BusinessException("供应月份不能为空");
        }
        if (StringUtils.isEmpty(dto.getNewSupplyMonth())){
            throw new BusinessException("新供应月份不能为空");
        }
        if (StringUtils.isEmpty(dto.getResourceId())){
            throw new BusinessException("新资源不能为空");
        }
        if (StringUtils.isEmpty(dto.getResourceCode())){
            throw new BusinessException("原资源编码不能为空");
        }
        if (dto.getSupplyQuantity() == null){
            throw new BusinessException("原供应数量不能为空");
        }
        if (dto.getNewSupplyQuantity() == null){
            throw new BusinessException("新供应数量不能为空");
        }
        if (StringUtils.isEmpty(dto.getSupplyModel())){
            throw new BusinessException("供应方式不能为空");
        }
        if (StringUtils.isEmpty(dto.getLockStatus())){
            throw new BusinessException("锁定状态不能为空");
        }
        if (LockStatusEnum.LOCKED.getCode().equals(dto.getLockStatus())){
            throw new BusinessException("数据已锁定，不允许修改");
        }
    }

    private void lockCheck(CapacitySupplyRelationshipVO vo){
        if (vo == null){
            throw new BusinessException("参数不能为空");
        }
        if (StringUtils.isEmpty(vo.getId())){
            throw new BusinessException("原数据主键不能为空");
        }
        if (StringUtils.isEmpty(vo.getProductCode())){
            throw new BusinessException("产品编码不能为空");
        }
        if (StringUtils.isEmpty(vo.getOperationCode())){
            throw new BusinessException("工序编码不能为空");
        }
        if (StringUtils.isEmpty(vo.getForecastMonth())){
            throw new BusinessException("需求月份不能为空");
        }
        if (StringUtils.isEmpty(vo.getSupplyMonth())){
            throw new BusinessException("供应月份不能为空");
        }
        if (StringUtils.isEmpty(vo.getResourceCode())){
            throw new BusinessException("原资源编码不能为空");
        }
        if (vo.getSupplyQuantity() == null){
            throw new BusinessException("原供应数量不能为空");
        }
        if (StringUtils.isEmpty(vo.getSupplyModel())){
            throw new BusinessException("供应方式不能为空");
        }
        if (StringUtils.isEmpty(vo.getLockStatus())){
            throw new BusinessException("锁定状态不能为空");
        }
    }

    /**
     * 特殊工艺校验
     * @param operationCode
     */
    private void checkSpecialOperation(String operationCode) {
        List<CollectionValueVO> specialTechnology = ipsFeign.getByCollectionCode("SPECIAL_TECHNOLOGY");
        List<String> specialTechnologyList = specialTechnology.stream().map(CollectionValueVO::getValueMeaning).collect(Collectors.toList());
        //是特殊工艺对应的供应关系，不允许编辑
        if (CollectionUtils.isNotEmpty(specialTechnologyList) && specialTechnologyList.contains(operationCode)){
            throw new BusinessException("特殊工艺不允许编辑");
        }
    }



    private void commonDataUpdate(List<CapacitySupplyRelationshipPO> insertList, List<CapacitySupplyRelationshipPO> updateList, List<String> deleteIdList) {
        if (CollectionUtils.isNotEmpty(insertList)){
            BasePOUtils.insertBatchFiller(insertList);
            capacitySupplyRelationshipDao.insertBatch(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            updateList.forEach(t->t.setRule(CapacityBalanceRule.manualModification.getCode()));
            BasePOUtils.updateBatchFiller(updateList);
            capacitySupplyRelationshipDao.updateBatch(updateList);
        }
        if (CollectionUtils.isNotEmpty(deleteIdList)){
            capacitySupplyRelationshipDao.deleteBatch(deleteIdList);
        }
    }

    private CapacitySupplyRelationshipEditorDataDO commonEdit(CapacitySupplyRelationshipEditorDTO dto,
                                                              CapacitySupplyRelationshipPO sourceData,
                                                              List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList,
                                                              Map<String, RoutingStepResourceVO> routingStepResourceVOMap){
        CapacitySupplyRelationshipEditorDataDO capacitySupplyRelationshipEditorDataDO = new CapacitySupplyRelationshipEditorDataDO();
        List<String> deleteList = new ArrayList<>();
        List<CapacitySupplyRelationshipPO> insertList = new ArrayList<>();
        List<CapacitySupplyRelationshipPO> updateList = new ArrayList<>();
        String pattern = "yyyyMM";

        String beatKey = sourceData.getRoutingStepId()+"-"+dto.getResourceId();
        String beat = routingStepResourceVOMap.get(beatKey) == null ? null : routingStepResourceVOMap.get(beatKey).getUnitProductionTime().toString();
        RoutingStepResourceVO routingStepResourceVO = routingStepResourceVOMap.get(beatKey);
        //改变的数量（可正可负）
        BigDecimal num = dto.getSupplyQuantity();
        //看资源或者供应时间是不是变了,变了的话，说明是整体移动了
        if (num.compareTo(BigDecimal.ZERO) == 0){
            capacitySupplyRelationshipEditorDataDO.setDeleteIdList(deleteList);
            capacitySupplyRelationshipEditorDataDO.setInsertList(insertList);
            capacitySupplyRelationshipEditorDataDO.setUpdateList(updateList);
            return capacitySupplyRelationshipEditorDataDO;
        }

        CapacitySupplyRelationshipPO po = new CapacitySupplyRelationshipPO();
        BeanUtils.copyProperties(sourceData, po);
        po.setId(null);
        po.setResourceCode(routingStepResourceVO.getPhysicalResourceCode());
        po.setResourceName(routingStepResourceVO.getPhysicalResourceName());
        po.setSupplyQuantity(dto.getSupplyQuantity());
        po.setSupplyModel(SupplyModelEnum.LOCAL.getCode());
        po.setSupplyTime(dto.getSupplyTime());
        //根据这些条件寻找现有的功能供应关系数据
        CapacitySupplyRelationshipPO supplyRelationshipPO = capacitySupplyRelationshipPOList.stream().sorted(Comparator.comparing(CapacitySupplyRelationshipPO::getSupplyTime)).filter(t -> {
            return po.getResourceCode().equals(t.getResourceCode()) && po.getSupplyModel().equals(t.getSupplyModel())
                    && DateUtils.dateToString(po.getSupplyTime(), pattern).equals(DateUtils.dateToString(t.getSupplyTime(), pattern));
        }).findFirst().orElse(null);
        if (supplyRelationshipPO != null){
            //如果找到的还是原数据，直接报错，在批量调整中supplyRelationshipPO可能是新生成的，id为null
            if (sourceData.getId().equals(supplyRelationshipPO.getId())){
                //找相同供应时间的委外数据
                sourceData.setSupplyQuantity(dto.getSupplyQuantity());
                updateList.add(sourceData);
            }else {//这种情况可能资源，供应时间，供应数量都变了，在已有的供应关系里能找到
                supplyRelationshipPO.setSupplyQuantity(supplyRelationshipPO.getSupplyQuantity().add(num));
                supplyRelationshipPO.setDemandQuantity(supplyRelationshipPO.getDemandQuantity().add(num));
                sourceData.setSupplyQuantity(sourceData.getSupplyQuantity().subtract(num));
                updateList.add(sourceData);
                updateList.add(supplyRelationshipPO);
            }
        }else {//这种情况就是原来的供应关系里没有该数据，需要新增一条数据，新增的数据就CapacitySupplyRelationshipPO
            sourceData.setSupplyQuantity(sourceData.getSupplyQuantity().subtract(num));
            po.setBeat(beat);
            capacitySupplyRelationshipPOList.add(po);
            insertList.add(po);
            updateList.add(sourceData);
        }
        //现在capacitySupplyRelationshipPOList就是新的供应关系,过滤掉供应数量为0的
/*        BigDecimal totalSupplyQuantity = capacitySupplyRelationshipPOList.stream()
                .filter(t->t.getSupplyQuantity().compareTo(BigDecimal.ZERO)>=0)
                .map(CapacitySupplyRelationshipPO::getSupplyQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //需求数量在每条数据上都是一样的
        if (totalSupplyQuantity.compareTo(sourceData.getDemandQuantity()) > 0){
            throw new BusinessException("总供应数量不能大于需求数量");
        }*/
        deleteList = capacitySupplyRelationshipPOList.stream().filter(t -> t.getSupplyQuantity().compareTo(BigDecimal.ZERO) <= 0).map(CapacitySupplyRelationshipPO::getId).collect(Collectors.toList());
        updateList = updateList.stream().filter(t -> t.getSupplyQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        insertList.forEach(t->t.setRule(CapacityBalanceRule.manualModification.getCode()));
        updateList.forEach(t->t.setRule(CapacityBalanceRule.manualModification.getCode()));
        capacitySupplyRelationshipEditorDataDO.setDeleteIdList(deleteList);
        capacitySupplyRelationshipEditorDataDO.setInsertList(insertList);
        capacitySupplyRelationshipEditorDataDO.setUpdateList(updateList);
        return capacitySupplyRelationshipEditorDataDO;

    }

    /**
     * 公共参数校验
     * @param dto
     */
    private void commonCheck(CapacitySupplyRelationshipEditorDTO dto) {
        if (StringUtils.isEmpty(dto.getResourceId())){
            throw new BusinessException("分配资源不能为空");
        }
        if (StringUtils.isEmpty(dto.getSupplyModel())){
            throw new BusinessException("供应方式不能为空");
        }
        if (dto.getSupplyTime() == null){
            throw new BusinessException("供应时间不能为空");
        }
    }

    private void paramsCheck(CapacitySupplyRelationshipEditorDTO dto) {
        if (dto == null){
            throw new BusinessException("参数不能为空");
        }
        if (StringUtils.isEmpty(dto.getId())){
            throw new BusinessException("原数据主键不能为空");
        }
        if (dto.getSupplyQuantity() == null){
            throw new BusinessException("供应数量不能为空");
        }
        if (StringUtils.isEmpty(dto.getOperationCode())){
            throw new BusinessException("工序编码不能为空");
        }
        commonCheck(dto);
        //改供应方式只能从委外改本厂
        /*if (SupplyModelEnum.OUTSOURCED.getCode().equals(dto.getSupplyModel())){
            throw new BusinessException("供应方式不能改成委外");
        }*/
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doOutsourcingAdjustment(OutsourcingAdjustmentDTO dto) {
        if (dto == null) {
            return;
        }
        if (StringUtils.isEmpty(dto.getProductCode())){
            throw new BusinessException("本厂编码不能为空");
        }
        if (StringUtils.isEmpty(dto.getChangeType())){
            throw new BusinessException("转产方式不能为空");
        }
        if (dto.getChangeStartDate() == null){
            throw new BusinessException("转产开始时间不能为空");
        }
        if (dto.getChangeEndDate() == null){
            throw new BusinessException("转产结束时间不能为空");
        }
        List<CollectionValueVO> specialTechnology = ipsFeign.getByCollectionCode("SPECIAL_TECHNOLOGY");
        List<String> specialTechnologyList = specialTechnology.stream().map(CollectionValueVO::getValueMeaning).collect(Collectors.toList());
        String pattern = "yyyy-MM";
        String start = DateUtils.dateToString(dto.getChangeStartDate(), pattern);
        String end = DateUtils.dateToString(dto.getChangeEndDate(), pattern);
        Date startTime = DateUtils.stringToDate(start, pattern);
        Date endTime = DateUtils.stringToDate(end, pattern);
        //新增一条委外转产需求
        outsourceTransferSummaryService.doCreate(OutsourceTransferSummaryConvertor.INSTANCE.outsourcingAdjustment2Dto(dto));
        //根据本厂编码，需求时间和工序编码查询出该时间段内该工序的供应关系数据
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOS = null;
        if (ChangeTypeEnum.DEMAND_OUTSOURCING.getCode().equals(dto.getChangeType())){
            capacitySupplyRelationshipPOS = capacitySupplyRelationshipDao.selectForecastTime(dto.getProductCode(), null, startTime, endTime);
        }else {
            if (StringUtils.isEmpty(dto.getOperationCode())){
                throw new BusinessException("工序编码不能为空");
            }
            if (specialTechnologyList.contains(dto.getOperationCode())){
                throw new BusinessException("特殊工艺不允许工序委外");
            }
            capacitySupplyRelationshipPOS = capacitySupplyRelationshipDao.selectForecastTime(dto.getProductCode(), dto.getOperationCode(), startTime, endTime);
        }
        List<CapacitySupplyRelationshipPO> importData = capacitySupplyRelationshipPOS.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getImportFlag())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(importData)){
            throw new BusinessException("导入数据不允许委外");
        }
        List<CapacitySupplyRelationshipPO> lockData = capacitySupplyRelationshipPOS.stream().filter(t -> LockStatusEnum.LOCKED.getCode().equals(t.getLockStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lockData)){
            throw new BusinessException("有锁定数据不允许委外");
        }
    }

    @Override
    public List<ProductCandidateResourceTimeVO> calculateThePriority(List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOList,
                                                                     String scenario) {
        if (CollectionUtils.isEmpty(capacitySupplyRelationshipVOList)){
            return new ArrayList<>();
        }
        String pattern = "yyyy-MM";
        //只计算计划期间开始月份，和之后的两个月的数据，其他的删掉
        PlanningHorizonVO planningHorizonVO = newMdsFeign.selectPlanningHorizon(scenario);
        String startMonth = DateUtils.dateToString(planningHorizonVO.getPlanStartTime(), pattern);
        Date firstMonth = DateUtils.stringToDate(startMonth, pattern);
        Date secondMonth = DateUtils.moveMonth(firstMonth, 1);
        String secondMonthStr = DateUtils.dateToString(secondMonth, pattern);

        //根据最新的产能供应关系重新计算设备生产关系
        capacitySupplyRelationshipVOList = capacitySupplyRelationshipVOList.stream()
                .filter(t-> SupplyModelEnum.LOCAL.getCode().equals(t.getSupplyModel()) && secondMonthStr.equals(t.getSupplyMonth()))
                .sorted(Comparator.comparing(CapacitySupplyRelationshipVO::getForecastTime)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(capacitySupplyRelationshipVOList)) {
            return new ArrayList<>();
        }

        List<String> productCodeList = capacitySupplyRelationshipVOList.stream().map(CapacitySupplyRelationshipVO::getProductCode).distinct().collect(Collectors.toList());
        //获取原来的优先级
        Map<String, Object> params = new HashMap<>();
        params.put("productCodeList", productCodeList);
        List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOList  = newMdsFeign.selectProductCandidateResourceTimeByParams(scenario, params);
        //根据产品编码+工序编码+生效时间分组
        Map<String, List<ProductCandidateResourceTimeVO>> productCandidateResourceTimeMap = productCandidateResourceTimeVOList.stream().collect(Collectors.groupingBy(t -> t.getProductCode() + "-" + t.getOperationCode()));

        //再按本厂编码分组
        Map<String, List<CapacitySupplyRelationshipVO>> productMap = capacitySupplyRelationshipVOList.stream().collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getProductCode));

        for (Map.Entry<String, List<CapacitySupplyRelationshipVO>> productEntry : productMap.entrySet()) {
            String productCode = productEntry.getKey();
            //每个产品再根据工序分组
            Map<String, List<CapacitySupplyRelationshipVO>> operationMap = productEntry.getValue().stream().collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getOperationCode));
            for (Map.Entry<String, List<CapacitySupplyRelationshipVO>> operationEntry : operationMap.entrySet()) {
                String operationCode = operationEntry.getKey();
                //获取原来的优先级
                String key = productCode + "-" + operationCode;
                List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOS = productCandidateResourceTimeMap.get(key);
                if (CollectionUtils.isEmpty(productCandidateResourceTimeVOS)){
                    continue;
                }
                List<String> resourceCodeList = productCandidateResourceTimeVOS.stream().map(ProductCandidateResourceTimeVO::getResourceCode).distinct().collect(Collectors.toList());
                //按资源分组求和，
                Map<String, BigDecimal> resourceSupplySum = operationEntry.getValue().stream()
                        .collect(Collectors.toMap(
                                CapacitySupplyRelationshipVO::getResourceCode,
                                CapacitySupplyRelationshipVO::getSupplyQuantity,
                                BigDecimal::add));
                //补充未分配的资源
                for (String resourceCode : resourceCodeList) {
                    if (!resourceSupplySum.containsKey(resourceCode)){
                        resourceSupplySum.put(resourceCode, BigDecimal.ZERO);
                    }
                }
                for (ProductCandidateResourceTimeVO resourceTimeVO : productCandidateResourceTimeVOS) {
                    resourceTimeVO.setNum(resourceSupplySum.get(resourceTimeVO.getResourceCode()));
                }
                mergeSort(productCandidateResourceTimeVOS, 0, productCandidateResourceTimeVOS.size()-1);
            }
        }

        return productCandidateResourceTimeVOList;
    }

    @Override
    public void saveEquipmentProductionRelation(String scenario) {
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOList = capacitySupplyRelationshipDao.selectLatestCode();
        if (CollectionUtils.isEmpty(capacitySupplyRelationshipVOList)){
            return;
        }
        //过滤掉特殊工艺对应的供应关系
        List<CollectionValueVO> specialTechnology = ipsFeign.getByCollectionCode("SPECIAL_TECHNOLOGY");
        List<String> specialTechnologyList = specialTechnology.stream().map(CollectionValueVO::getValueMeaning).collect(Collectors.toList());
        //导入数据可能存在本厂编码为null的情况，需要过滤掉
        capacitySupplyRelationshipVOList = capacitySupplyRelationshipVOList.stream().filter(t->!specialTechnologyList.contains(t.getOperationCode()) && t.getProductCode()!=null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(capacitySupplyRelationshipVOList)){
            return;
        }
        List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOS = calculateThePriority(capacitySupplyRelationshipVOList, scenario);
        //保存数据
        if (CollectionUtils.isNotEmpty(productCandidateResourceTimeVOS)) {
            newMdsFeign.saveProductCandidateResourceTimeVO(SystemHolder.getScenario(), productCandidateResourceTimeVOS);
        }
    }

    public List<LabelValue<String>> resourceDropDown(List<String> routingStepIds) {
        if (CollectionUtils.isEmpty(routingStepIds)){
            return null;
        }
        List<RoutingStepResourceVO> stepResourceVOS = newMdsFeign.selectRoutingStepResourceByRoutingStepIds(SystemHolder.getScenario(), routingStepIds);
        // 过滤掉禁用的
        stepResourceVOS = stepResourceVOS.stream().filter(t-> YesOrNoEnum.YES.getCode().equals(t.getEnabled())).collect(Collectors.toList());
        Map<String, String> reourceMap = stepResourceVOS.stream().collect(Collectors.toMap(RoutingStepResourceBasicVO::getPhysicalResourceId, RoutingStepResourceBasicVO::getPhysicalResourceCode, (v1, v2) -> v1));
        Map<String, List<RoutingStepResourceVO>> map = stepResourceVOS.stream().collect(Collectors.groupingBy(RoutingStepResourceVO::getRoutingStepId));
        List<LabelValue<String>> resourceList = new ArrayList<>();
        Set<String> resourceIds = new HashSet<>();
        boolean sameResource = true;
        //找到不同routingStepId中相同的资源
        for (Map.Entry<String, List<RoutingStepResourceVO>> entry : map.entrySet()) {
            resourceIds.addAll(entry.getValue().stream().map(RoutingStepResourceVO::getPhysicalResourceId).collect(Collectors.toList()));
            break;
        }
        //获取是否所有产品编码的相同候选资源
        for (String routingStepId : routingStepIds) {
            if (map.containsKey(routingStepId)) {
                List<String> codeList = map.get(routingStepId).stream().map(RoutingStepResourceVO::getPhysicalResourceId).distinct().collect(Collectors.toList());
                resourceIds.retainAll(codeList);
            } else {
                sameResource = false;
                break;
            }
        }
        if (sameResource) {
            for (String resourceId : resourceIds) {
                String resourceCode = reourceMap.get(resourceId);
                resourceList.add(new LabelValue<>(resourceCode, resourceId));
            }
        }
        return resourceList.stream().distinct().collect(Collectors.toList());
    }

    private void mergeSort(List<ProductCandidateResourceTimeVO> timeVOS, int left, int right) {
        if (left < right) {
            int mid = left + (right - left) / 2;

            mergeSort(timeVOS, left, mid);
            mergeSort(timeVOS, mid + 1, right);

            merge(timeVOS, left, mid, right);
        }
    }

    private void merge(List<ProductCandidateResourceTimeVO> timeVOS, int left, int mid, int right) {
        int n1 = mid - left + 1;
        int n2 = right - mid;

        List<ProductCandidateResourceTimeVO> L = new ArrayList<>(timeVOS.subList(left, mid + 1));
        List<ProductCandidateResourceTimeVO> R = new ArrayList<>(timeVOS.subList(mid + 1, right + 1));

        int i = 0, j = 0;
        int k = left;

        while (i < n1 && j < n2) {
            if (L.get(i).getNum().compareTo(R.get(j).getNum()) > 0 ) {
                timeVOS.set(k++, L.get(i++));
            } else {
                timeVOS.set(k++, R.get(j++));
            }
        }

        while (i < n1) {
            timeVOS.set(k++, L.get(i++));
        }

        while (j < n2) {
            timeVOS.set(k++, R.get(j++));
        }

        // 更新 priority 字段
        for (int e = left; e <= right; e++) {
            timeVOS.get(e).setPriority(e - left + 1);
        }
    }

	@Override
	public List<CapacitySupplyRelationshipVO> selectForSupplyCalculate(Map<String, Object> paramMap) {
		List<CapacitySupplyRelationshipPO> list = capacitySupplyRelationshipDao.selectForSupplyCalculate(paramMap);
		return CapacitySupplyRelationshipConvertor.INSTANCE.po2Vos(list);
	}

    public PageInfo<CapacitySupplyRelationshipVO> selectCollect(CapacitySupplyRelationshipEditorDTO dto){
        Map<String, Object> params = getParamsMap(dto);
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS = capacitySupplyRelationshipDao.selectCapacitySupplyRelationshipCollect(params);
        capacitySupplyRelationshipVOS = capacitySupplyRelationshipVOS.stream().sorted(Comparator.comparing(CapacitySupplyRelationshipVO::getForecastMonth))
                .collect(Collectors.toList());
        PageInfo pageInfo = PageUtils.getPageInfo(capacitySupplyRelationshipVOS, dto.getPageNum(),
                dto.getPageSize());
        return pageInfo;
    }


    public PageInfo<CapacitySupplyRelationshipVO> selectCollectByVersion(CapacitySupplyRelationshipEditorDTO dto){

        Map<String, Object> params = getParamsMap(dto);
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS = capacitySupplyRelationshipDao.selectCollectByParamsNew(params);
        capacitySupplyRelationshipVOS = capacitySupplyRelationshipVOS.stream().sorted(Comparator.comparing(CapacitySupplyRelationshipVO::getForecastMonth))
                .collect(Collectors.toList());
        PageInfo pageInfo = PageUtils.getPageInfo(capacitySupplyRelationshipVOS, dto.getPageNum(),
                dto.getPageSize());
        return pageInfo;
    }

    private Map<String, Object> getParamsMap(CapacitySupplyRelationshipEditorDTO dto) {
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", dto.getVersionId());
        params.put("productCode", dto.getProductCode());
        params.put("operationCode", dto.getOperationCode());
        params.put("operationName", dto.getOperationName());
        params.put("forecastTime", dto.getForecastTime());
        params.put("supplyTime", dto.getSupplyTime());
        params.put("forecastMonth", dto.getForecastMonth());
        params.put("supplyMonth", dto.getSupplyMonth());
        params.put("resourceCode", dto.getResourceCode());
        params.put("resourceName", dto.getResourceName());
        params.put("rule", dto.getRule());
        params.put("supplyModel", dto.getSupplyModel());
        return params;
    }

    private List<String> getResourceCodeList(){
        String userId = SystemHolder.getUserId();
        List<PhysicalResourceVO> physicalResourceVOS = newMdsFeign.selectAllPhysicalResource(SystemHolder.getScenario());
        List<String> filteredPhysicalResourceCode = physicalResourceVOS.stream()
                .filter(pr -> {
                    String productionPlanner = pr.getProductionPlanner();
                    if (productionPlanner != null) {
                        String[] plannerIds = productionPlanner.split(",");
                        return Arrays.asList(plannerIds).contains(userId);
                    }
                    return false;
                })
                .map(PhysicalResourceVO::getPhysicalResourceCode)
                .collect(Collectors.toList());

        return filteredPhysicalResourceCode;
    }

    @Override
    public void deleteByVersionIds(List<String> versionIds) {
        if (CollectionUtils.isEmpty(versionIds)){
            return;
        }
        //产能供应关系数据量太大，需要根据id分批删除
        List<String> deleteIds = capacitySupplyRelationshipDao.selectNeedDeleteIds(versionIds);
        if (CollectionUtils.isNotEmpty(deleteIds)){
            for (List<String> deleteId : com.yhl.platform.common.utils.CollectionUtils.splitList(deleteIds, 5000)) {
                capacitySupplyRelationshipDao.deleteBatch(deleteId);
            }
        }
    }

    @Override
    public List<CapacitySupplyRelationshipVO> dataConsistentCheckWeekly() {
        return capacitySupplyRelationshipDao.dataConsistentCheckWeekly();
    }

    @Override
    public List<CapacitySupplyRelationshipVO> dataConsistentCheckMonthly() {
        return capacitySupplyRelationshipDao.dataConsistentCheckMonthly();
    }

}
