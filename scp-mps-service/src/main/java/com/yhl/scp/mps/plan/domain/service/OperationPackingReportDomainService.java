package com.yhl.scp.mps.plan.domain.service;

import com.yhl.scp.mps.plan.domain.entity.OperationPackingReportDO;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationPackingReportDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>OperationPackingReportDomainService</code>
 * <p>
 * OperationPackingReportDomainService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:42:06
 */
@Service
public class OperationPackingReportDomainService {

    @Resource
    private OperationPackingReportDao operationPackingReportDao;

    /**
     * 数据校验
     *
     * @param operationPackingReportDO 领域对象
     */
    public void validation(OperationPackingReportDO operationPackingReportDO) {
        checkNotNull(operationPackingReportDO);
        checkUniqueCode(operationPackingReportDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param operationPackingReportDO 领域对象
     */
    private void checkNotNull(OperationPackingReportDO operationPackingReportDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param operationPackingReportDO 领域对象
     */
    private void checkUniqueCode(OperationPackingReportDO operationPackingReportDO) {
        // TODO
    }

}
