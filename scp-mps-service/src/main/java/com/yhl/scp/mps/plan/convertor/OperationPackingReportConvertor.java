package com.yhl.scp.mps.plan.convertor;

import com.yhl.scp.mps.plan.domain.entity.OperationPackingReportDO;
import com.yhl.scp.mps.plan.dto.OperationPackingReportDTO;
import com.yhl.scp.mps.plan.infrastructure.po.OperationPackingReportPO;
import com.yhl.scp.mps.plan.vo.OperationPackingReportVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>OperationPackingReportConvertor</code>
 * <p>
 * OperationPackingReportConvertor
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:42:06
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OperationPackingReportConvertor {

    OperationPackingReportConvertor INSTANCE = Mappers.getMapper(OperationPackingReportConvertor.class);

    OperationPackingReportDO dto2Do(OperationPackingReportDTO obj);

    List<OperationPackingReportDO> dto2Dos(List<OperationPackingReportDTO> list);

    OperationPackingReportDTO do2Dto(OperationPackingReportDO obj);

    List<OperationPackingReportDTO> do2Dtos(List<OperationPackingReportDO> list);

    OperationPackingReportDTO vo2Dto(OperationPackingReportVO obj);

    List<OperationPackingReportDTO> vo2Dtos(List<OperationPackingReportVO> list);

    OperationPackingReportVO po2Vo(OperationPackingReportPO obj);

    List<OperationPackingReportVO> po2Vos(List<OperationPackingReportPO> list);

    OperationPackingReportPO dto2Po(OperationPackingReportDTO obj);

    List<OperationPackingReportPO> dto2Pos(List<OperationPackingReportDTO> obj);

    OperationPackingReportVO do2Vo(OperationPackingReportDO obj);

    OperationPackingReportPO do2Po(OperationPackingReportDO obj);

    OperationPackingReportDO po2Do(OperationPackingReportPO obj);

}
