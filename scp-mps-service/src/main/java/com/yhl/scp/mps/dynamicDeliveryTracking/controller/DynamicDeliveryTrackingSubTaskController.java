package com.yhl.scp.mps.dynamicDeliveryTracking.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.dynamicDeliveryTracking.dto.DynamicDeliveryTrackingSubTaskDTO;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryAlertingService;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryTrackingSubTaskService;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.AlertMessageVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingTaskVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVXToVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>DynamicDeliveryTrackingSubTaskController</code>
 * <p>
 * 动态交付跟踪工序任务明细表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 13:47:44
 */
@Slf4j
@Api(tags = "动态交付跟踪工序任务明细表控制器")
@RestController
@RequestMapping("dynamicDeliveryTrackingSubTask")
public class DynamicDeliveryTrackingSubTaskController extends BaseController {

    @Resource
    private DynamicDeliveryTrackingSubTaskService dynamicDeliveryTrackingSubTaskService;

    @Resource
    private DynamicDeliveryAlertingService dynamicDeliveryAlertingService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<DynamicDeliveryTrackingSubTaskVO>> page() {
        List<DynamicDeliveryTrackingSubTaskVO> dynamicDeliveryTrackingSubTaskList = dynamicDeliveryTrackingSubTaskService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<DynamicDeliveryTrackingSubTaskVO> pageInfo = new PageInfo<>(dynamicDeliveryTrackingSubTaskList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody DynamicDeliveryTrackingSubTaskDTO dynamicDeliveryTrackingSubTaskDTO) {
        return dynamicDeliveryTrackingSubTaskService.doCreate(dynamicDeliveryTrackingSubTaskDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody DynamicDeliveryTrackingSubTaskDTO dynamicDeliveryTrackingSubTaskDTO) {
        return dynamicDeliveryTrackingSubTaskService.doUpdate(dynamicDeliveryTrackingSubTaskDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        dynamicDeliveryTrackingSubTaskService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<DynamicDeliveryTrackingSubTaskVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, dynamicDeliveryTrackingSubTaskService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "交付动态跟踪平台-明细查询")
    @GetMapping(value = "selectSubTaskByTaskId")
    public BaseResponse<List<DynamicDeliveryTrackingSubTaskVO>> selectSubTaskByTaskId(
            @RequestParam(value = "taskId", required = false) String taskId,
            @RequestParam(value = "limitFlag", required = false, defaultValue = "true") Boolean limitFlag) {
        List<DynamicDeliveryTrackingSubTaskVO> dynamicDeliveryTrackingSubTaskList =
                dynamicDeliveryTrackingSubTaskService.selectSubTaskByTaskId(taskId, limitFlag);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, dynamicDeliveryTrackingSubTaskList);
    }

    @ApiOperation(value = "交付动态跟踪平台-明细查询-报工")
    @GetMapping(value = "selectSubTaskByTaskIdAndReport")
    public BaseResponse<List<DynamicDeliveryTrackingSubTaskVO>> selectSubTaskByTaskIdAndReport(
            @RequestParam(value = "taskId", required = false) String taskId,
            @RequestParam(value = "limitFlag", required = false, defaultValue = "true") Boolean limitFlag) {
        List<DynamicDeliveryTrackingSubTaskVO> dynamicDeliveryTrackingSubTaskList =
                dynamicDeliveryTrackingSubTaskService.selectSubTaskByTaskIdAndReport(taskId, limitFlag);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, dynamicDeliveryTrackingSubTaskList);
    }

    @ApiOperation(value = "根据追踪工序任务构建工序任务明细")
    @PostMapping(value = "buildSubTaskByTask")
    public BaseResponse<List<DynamicDeliveryTrackingSubTaskVO>> buildSubTaskByTask(@RequestBody List<DynamicDeliveryTrackingTaskVO> taskList) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, dynamicDeliveryTrackingSubTaskService.buildSubTaskByTask(taskList));
    }

    @ApiOperation(value = "根据任务工序id找到关联的数据")
    @GetMapping(value = "search")
    public BaseResponse<DynamicDeliveryTrackingSubTaskVXToVO> search(@RequestParam(value = "id") String id) {
        return dynamicDeliveryTrackingSubTaskService.search(id);
    }

    @ApiOperation(value = "保存微信数据")
    @PostMapping(value = "saveVXData")
    public BaseResponse<Void> saveVXData(@RequestBody DynamicDeliveryTrackingSubTaskVXToVO vxToVO) {
        return dynamicDeliveryTrackingSubTaskService.saveVXData(vxToVO);
    }

    @ApiOperation(value = "告警查询")
    @PostMapping(value = "alertQuery")
    public BaseResponse<AlertMessageVO> alertQuery(@RequestParam(value = "scenario") String scenario,
                                                   @RequestParam(value = "physicalResourceId") String physicalResourceId,
                                                   @RequestParam(value = "taskId") String taskId) {
        return dynamicDeliveryAlertingService.checkWorkPeriod(scenario, physicalResourceId, taskId);
    }


    @ApiOperation(value = "测试服务端发送信息到客户端")
    @PostMapping(value = "testSendMessage")
    public BaseResponse<List<DynamicDeliveryTrackingSubTaskVO>> testSendMessage(@RequestParam("userId") String userId,
                                                                                @RequestParam("message")  String message) {
        String podId = System.getenv("HOSTNAME");
        dynamicDeliveryAlertingService.testSendMessage(userId, message);
        return BaseResponse.success(podId);
    }

    @ApiOperation(value = "测试服务端发送信息到客户端2")
    @PostMapping(value = "testSendMessage2")
    public BaseResponse<List<DynamicDeliveryTrackingSubTaskVO>> testSendMessage2(@RequestParam("userIds") List<String> userIds,
                                                                                 @RequestParam("message")  String message) {
        String podId = System.getenv("HOSTNAME");
        dynamicDeliveryAlertingService.testSendMessageToGroup(userIds, message);
        return BaseResponse.success(podId);
    }

}