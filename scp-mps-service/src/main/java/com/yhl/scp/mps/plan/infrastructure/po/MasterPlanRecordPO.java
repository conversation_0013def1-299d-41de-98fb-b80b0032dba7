package com.yhl.scp.mps.plan.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;

/**
 * <code>MasterPlanRecordPO</code>
 * <p>
 * PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 10:23:11
 */
public class MasterPlanRecordPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -82321397059518115L;

    /**
     * 场景
     */
    private String scenario;
    /**
     * 文件名
     */
    private String fileName;

    public String getScenario() {
        return scenario;
    }

    public void setScenario(String scenario) {
        this.scenario = scenario;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

}
