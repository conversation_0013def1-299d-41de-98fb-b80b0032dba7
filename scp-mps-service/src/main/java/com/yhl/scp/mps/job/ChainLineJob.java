package com.yhl.scp.mps.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mps.product.service.ChainLineInventoryLogService;
import com.yhl.scp.mps.reportingFeedback.service.MpsProReportingFeedbackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>ChaineLineJob</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-08 20:33:57
 */
@Component
@Slf4j
public class ChainLineJob {
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private ChainLineInventoryLogService chainLineInventoryLogService;

    @XxlJob("chainLineJob")
    private ReturnT<String> chainLineJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MPS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MPS模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的同步链式生产线job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            chainLineInventoryLogService.syncChainLine(scenario.getTenantId(),scenario.getDataBaseName());
            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步链式生产线job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }
}
