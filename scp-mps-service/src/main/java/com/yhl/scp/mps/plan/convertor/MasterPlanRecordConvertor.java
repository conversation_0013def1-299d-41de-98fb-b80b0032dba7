package com.yhl.scp.mps.plan.convertor;

import com.yhl.scp.mps.plan.domain.entity.MasterPlanRecordDO;
import com.yhl.scp.mps.plan.dto.MasterPlanRecordDTO;
import com.yhl.scp.mps.plan.infrastructure.po.MasterPlanRecordPO;
import com.yhl.scp.mps.plan.vo.MasterPlanRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MasterPlanRecordConvertor</code>
 * <p>
 * 转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 10:23:11
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MasterPlanRecordConvertor {

    MasterPlanRecordConvertor INSTANCE = Mappers.getMapper(MasterPlanRecordConvertor.class);

    MasterPlanRecordDO dto2Do(MasterPlanRecordDTO obj);

    MasterPlanRecordDTO do2Dto(MasterPlanRecordDO obj);

    List<MasterPlanRecordDO> dto2Dos(List<MasterPlanRecordDTO> list);

    List<MasterPlanRecordDTO> do2Dtos(List<MasterPlanRecordDO> list);

    MasterPlanRecordVO do2Vo(MasterPlanRecordDO obj);

    MasterPlanRecordVO po2Vo(MasterPlanRecordPO obj);

    List<MasterPlanRecordVO> po2Vos(List<MasterPlanRecordPO> list);

    MasterPlanRecordPO do2Po(MasterPlanRecordDO obj);

    MasterPlanRecordDO po2Do(MasterPlanRecordPO obj);

    MasterPlanRecordPO dto2Po(MasterPlanRecordDTO obj);

    List<MasterPlanRecordPO> dto2Pos(List<MasterPlanRecordDTO> obj);

}
