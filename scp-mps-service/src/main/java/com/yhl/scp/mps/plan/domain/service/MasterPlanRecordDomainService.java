package com.yhl.scp.mps.plan.domain.service;

import com.yhl.scp.mps.plan.domain.entity.MasterPlanRecordDO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanRecordDao;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MasterPlanRecordDomainService</code>
 * <p>
 * 领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 10:23:11
 */
@Service
public class MasterPlanRecordDomainService {

    @Resource
    private MasterPlanRecordDao masterPlanRecordDao;

    /**
     * 数据校验
     *
     * @param masterPlanRecordDO 领域对象
     */
    public void validation(MasterPlanRecordDO masterPlanRecordDO) {
        checkNotNull(masterPlanRecordDO);
        checkUniqueCode(masterPlanRecordDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param masterPlanRecordDO 领域对象
     */
    private void checkNotNull(MasterPlanRecordDO masterPlanRecordDO) {

    }

    /**
     * 唯一性校验
     *
     * @param masterPlanRecordDO 领域对象
     */
    private void checkUniqueCode(MasterPlanRecordDO masterPlanRecordDO) {

    }

    public List<String> getAllHeaderNames(List<String> headerNames) {
        List<String> result = Lists.newArrayList();
        result.add("资源");
        result.add("产品类型");
        result.add("产品编码");
        result.add("工具资源");
        result.add("模具数量限制");
        result.add("排产日期");
        result.add("排产结束日期");
        result.add("生产时间");
        result.add("综合成品率");
        result.add("制造订单号");
        result.add("ERP计划单号");
        result.add("MES工单号");
        result.add("节拍");
        result.add("换模时间");
        result.add("包装方式");
        result.add("订单类型");
        result.add("单箱片数");
        result.add("风栅类型");
        result.add("生产模式");
        result.add("预处理");
        result.add("印刷");
        result.add("成型");
        result.add("合片");
        result.add("包装");
        result.add("在产品合计");
        result.add("成品库存");
        result.add("半品库存");
        result.add("hud");
        result.add("夹丝");
        result.add("宽");
        result.add("长");
        result.add("颜色");
        result.add("加热线");
        result.add("排产数量");
        result.add("完工数量");
        result.add("任务交期");
        result.add("预处理排产时间");
        result.add("风险等级");
        result.add("工序");
        result.add("工序任务代码");
        result.add("是否延期");
        result.add("工单状态");
        result.add("齐套状态");
        result.add("计划状态");
        result.add("成套数量");
        result.add("成品编码");
        result.add("试制单号");
        result.add("备注");
        result.add("固化时间");
        result.add("当月预测剩余量");
        result.add("下月预测数量");
        result.addAll(headerNames);
        return result;
    }


    public MasterPlanReq getMasterPlanReq(String scenario) {
        MasterPlanReq masterPlanReq = new MasterPlanReq();
        masterPlanReq.setPageNum(1);
        masterPlanReq.setPageSize(99999);
        masterPlanReq.setUseCache(Boolean.FALSE);
        masterPlanReq.setShowAbnormalShift(Boolean.TRUE);
        masterPlanReq.setOrderBy("resourceName asc,planDate asc");
        masterPlanReq.setScenario(scenario);
        return masterPlanReq;
    }

}
