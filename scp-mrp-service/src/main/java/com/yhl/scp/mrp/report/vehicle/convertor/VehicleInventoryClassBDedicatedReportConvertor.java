package com.yhl.scp.mrp.report.vehicle.convertor;

import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassBDedicatedReportDO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBDedicatedReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBDedicatedReportPO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBDedicatedReportVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>VehicleInventoryClassBDedicatedReportConvertor</code>
 * <p>
 * 车型库存（B类专用）报表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:47:36
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleInventoryClassBDedicatedReportConvertor {

    VehicleInventoryClassBDedicatedReportConvertor INSTANCE = Mappers.getMapper(VehicleInventoryClassBDedicatedReportConvertor.class);

    VehicleInventoryClassBDedicatedReportDO dto2Do(VehicleInventoryClassBDedicatedReportDTO obj);

    VehicleInventoryClassBDedicatedReportDTO do2Dto(VehicleInventoryClassBDedicatedReportDO obj);

    List<VehicleInventoryClassBDedicatedReportDO> dto2Dos(List<VehicleInventoryClassBDedicatedReportDTO> list);

    List<VehicleInventoryClassBDedicatedReportDTO> do2Dtos(List<VehicleInventoryClassBDedicatedReportDO> list);

    VehicleInventoryClassBDedicatedReportVO do2Vo(VehicleInventoryClassBDedicatedReportDO obj);

    VehicleInventoryClassBDedicatedReportVO po2Vo(VehicleInventoryClassBDedicatedReportPO obj);

    List<VehicleInventoryClassBDedicatedReportVO> po2Vos(List<VehicleInventoryClassBDedicatedReportPO> list);

    VehicleInventoryClassBDedicatedReportPO do2Po(VehicleInventoryClassBDedicatedReportDO obj);

    VehicleInventoryClassBDedicatedReportDO po2Do(VehicleInventoryClassBDedicatedReportPO obj);

    VehicleInventoryClassBDedicatedReportPO dto2Po(VehicleInventoryClassBDedicatedReportDTO obj);

    List<VehicleInventoryClassBDedicatedReportPO> dto2Pos(List<VehicleInventoryClassBDedicatedReportDTO> obj);

}
