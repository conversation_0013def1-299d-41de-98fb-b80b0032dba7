package com.yhl.scp.mrp.report.vehicle.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>VehicleInventoryClassBFactoryReportPO</code>
 * <p>
 * 车型库存（B类本厂）报表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:48:40
 */
public class VehicleInventoryClassBFactoryReportPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 249713531716758616L;

    /**
     * 客户
     */
    private String customer;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    private String productFactoryCode;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 物料名称
     */
    private String productName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 采购周期
     */
    private BigDecimal purchaseLot;
    /**
     * 单耗
     */
    private BigDecimal inputFactor;
    /**
     * 单位
     */
    private String unit;
    /**
     * 产出率
     */
    private BigDecimal outputRate;
    /**
     * 材料预测占比
     */
    private String materialPredictedProportion;
    /**
     * 材料预测占比（车型）
     */
    private String materialPredictedProportionVehicle;
    /**
     * 是否专用（占比>=50%）
     */
    private String isDedicatedGe50pct;
    /**
     * 未来30天日均需求
     */
    private BigDecimal next30DaysDailyAverageDemand;
    /**
     * 辅料库存
     */
    private BigDecimal auxiliaryInventory;
    /**
     * 供应商库存
     */
    private BigDecimal supplierInventory;
    /**
     * 现有库存可生产玻璃零件数量
     */
    private BigDecimal currentStockPartProdQuantity;
    /**
     * 福耀库存可用天数
     */
    private Integer factoryInventoryUseDay;
    /**
     * 供应商库存可用天数
     */
    private Integer supplierInventoryUseDay;
    /**
     * 材料总天数
     */
    private Integer materialTotalDay;
    /**
     * 预测日期
     */
    private Date forecastDate;
    /**
     * 预测数量
     */
    private BigDecimal forecastQuantity;
    /**
     * 成本总金额（万元）
     */
    private BigDecimal totalCostAmountThousand;
    /**
     * 本厂库存金额（万元）
     */
    private BigDecimal factoryInventoryAmountThousand;
    /**
     * 供应商库存金额（万元）
     */
    private BigDecimal supplierInventoryAmountThousand;
    /**
     * 版本号
     */
    private Integer versionValue;

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getVehicleModelCode() {
        return vehicleModelCode;
    }

    public void setVehicleModelCode(String vehicleModelCode) {
        this.vehicleModelCode = vehicleModelCode;
    }

    public String getProductFactoryCode() {
        return productFactoryCode;
    }

    public void setProductFactoryCode(String productFactoryCode) {
        this.productFactoryCode = productFactoryCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public BigDecimal getPurchaseLot() {
        return purchaseLot;
    }

    public void setPurchaseLot(BigDecimal purchaseLot) {
        this.purchaseLot = purchaseLot;
    }

    public BigDecimal getInputFactor() {
        return inputFactor;
    }

    public void setInputFactor(BigDecimal inputFactor) {
        this.inputFactor = inputFactor;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getOutputRate() {
        return outputRate;
    }

    public void setOutputRate(BigDecimal outputRate) {
        this.outputRate = outputRate;
    }

    public String getMaterialPredictedProportion() {
        return materialPredictedProportion;
    }

    public void setMaterialPredictedProportion(String materialPredictedProportion) {
        this.materialPredictedProportion = materialPredictedProportion;
    }

    public String getMaterialPredictedProportionVehicle() {
        return materialPredictedProportionVehicle;
    }

    public void setMaterialPredictedProportionVehicle(String materialPredictedProportionVehicle) {
        this.materialPredictedProportionVehicle = materialPredictedProportionVehicle;
    }

    public String getIsDedicatedGe50pct() {
        return isDedicatedGe50pct;
    }

    public void setIsDedicatedGe50pct(String isDedicatedGe50pct) {
        this.isDedicatedGe50pct = isDedicatedGe50pct;
    }

    public BigDecimal getNext30DaysDailyAverageDemand() {
        return next30DaysDailyAverageDemand;
    }

    public void setNext30DaysDailyAverageDemand(BigDecimal next30DaysDailyAverageDemand) {
        this.next30DaysDailyAverageDemand = next30DaysDailyAverageDemand;
    }

    public BigDecimal getAuxiliaryInventory() {
        return auxiliaryInventory;
    }

    public void setAuxiliaryInventory(BigDecimal auxiliaryInventory) {
        this.auxiliaryInventory = auxiliaryInventory;
    }

    public BigDecimal getSupplierInventory() {
        return supplierInventory;
    }

    public void setSupplierInventory(BigDecimal supplierInventory) {
        this.supplierInventory = supplierInventory;
    }

    public BigDecimal getCurrentStockPartProdQuantity() {
        return currentStockPartProdQuantity;
    }

    public void setCurrentStockPartProdQuantity(BigDecimal currentStockPartProdQuantity) {
        this.currentStockPartProdQuantity = currentStockPartProdQuantity;
    }

    public Integer getFactoryInventoryUseDay() {
        return factoryInventoryUseDay;
    }

    public void setFactoryInventoryUseDay(Integer factoryInventoryUseDay) {
        this.factoryInventoryUseDay = factoryInventoryUseDay;
    }

    public Integer getSupplierInventoryUseDay() {
        return supplierInventoryUseDay;
    }

    public void setSupplierInventoryUseDay(Integer supplierInventoryUseDay) {
        this.supplierInventoryUseDay = supplierInventoryUseDay;
    }

    public Integer getMaterialTotalDay() {
        return materialTotalDay;
    }

    public void setMaterialTotalDay(Integer materialTotalDay) {
        this.materialTotalDay = materialTotalDay;
    }

    public Date getForecastDate() {
        return forecastDate;
    }

    public void setForecastDate(Date forecastDate) {
        this.forecastDate = forecastDate;
    }

    public BigDecimal getForecastQuantity() {
        return forecastQuantity;
    }

    public void setForecastQuantity(BigDecimal forecastQuantity) {
        this.forecastQuantity = forecastQuantity;
    }

    public BigDecimal getTotalCostAmountThousand() {
        return totalCostAmountThousand;
    }

    public void setTotalCostAmountThousand(BigDecimal totalCostAmountThousand) {
        this.totalCostAmountThousand = totalCostAmountThousand;
    }

    public BigDecimal getFactoryInventoryAmountThousand() {
        return factoryInventoryAmountThousand;
    }

    public void setFactoryInventoryAmountThousand(BigDecimal factoryInventoryAmountThousand) {
        this.factoryInventoryAmountThousand = factoryInventoryAmountThousand;
    }

    public BigDecimal getSupplierInventoryAmountThousand() {
        return supplierInventoryAmountThousand;
    }

    public void setSupplierInventoryAmountThousand(BigDecimal supplierInventoryAmountThousand) {
        this.supplierInventoryAmountThousand = supplierInventoryAmountThousand;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
