package com.yhl.scp.mrp.report.vehicle.convertor;

import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassBFactoryReportDO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBFactoryReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBFactoryReportPO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBFactoryReportVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>VehicleInventoryClassBFactoryReportConvertor</code>
 * <p>
 * 车型库存（B类本厂）报表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:48:40
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleInventoryClassBFactoryReportConvertor {

    VehicleInventoryClassBFactoryReportConvertor INSTANCE = Mappers.getMapper(VehicleInventoryClassBFactoryReportConvertor.class);

    VehicleInventoryClassBFactoryReportDO dto2Do(VehicleInventoryClassBFactoryReportDTO obj);

    VehicleInventoryClassBFactoryReportDTO do2Dto(VehicleInventoryClassBFactoryReportDO obj);

    List<VehicleInventoryClassBFactoryReportDO> dto2Dos(List<VehicleInventoryClassBFactoryReportDTO> list);

    List<VehicleInventoryClassBFactoryReportDTO> do2Dtos(List<VehicleInventoryClassBFactoryReportDO> list);

    VehicleInventoryClassBFactoryReportVO do2Vo(VehicleInventoryClassBFactoryReportDO obj);

    VehicleInventoryClassBFactoryReportVO po2Vo(VehicleInventoryClassBFactoryReportPO obj);

    List<VehicleInventoryClassBFactoryReportVO> po2Vos(List<VehicleInventoryClassBFactoryReportPO> list);

    VehicleInventoryClassBFactoryReportPO do2Po(VehicleInventoryClassBFactoryReportDO obj);

    VehicleInventoryClassBFactoryReportDO po2Do(VehicleInventoryClassBFactoryReportPO obj);

    VehicleInventoryClassBFactoryReportPO dto2Po(VehicleInventoryClassBFactoryReportDTO obj);

    List<VehicleInventoryClassBFactoryReportPO> dto2Pos(List<VehicleInventoryClassBFactoryReportDTO> obj);

}
