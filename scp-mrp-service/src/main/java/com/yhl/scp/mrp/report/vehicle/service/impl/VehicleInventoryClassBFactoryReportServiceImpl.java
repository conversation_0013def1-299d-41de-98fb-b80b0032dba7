package com.yhl.scp.mrp.report.vehicle.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.report.vehicle.convertor.VehicleInventoryClassBFactoryReportConvertor;
import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassBFactoryReportDO;
import com.yhl.scp.mrp.report.vehicle.domain.service.VehicleInventoryClassBFactoryReportDomainService;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBFactoryReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassBFactoryReportDao;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBFactoryReportPO;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassBFactoryReportService;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBFactoryReportVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>VehicleInventoryClassBFactoryReportServiceImpl</code>
 * <p>
 * 车型库存（B类本厂）报表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:48:34
 */
@Slf4j
@Service
public class VehicleInventoryClassBFactoryReportServiceImpl extends AbstractService implements VehicleInventoryClassBFactoryReportService {

    @Resource
    private VehicleInventoryClassBFactoryReportDao vehicleInventoryClassBFactoryReportDao;

    @Resource
    private VehicleInventoryClassBFactoryReportDomainService vehicleInventoryClassBFactoryReportDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(VehicleInventoryClassBFactoryReportDTO vehicleInventoryClassBFactoryReportDTO) {
        // 0.数据转换
        VehicleInventoryClassBFactoryReportDO vehicleInventoryClassBFactoryReportDO = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassBFactoryReportDTO);
        VehicleInventoryClassBFactoryReportPO vehicleInventoryClassBFactoryReportPO = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassBFactoryReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassBFactoryReportDomainService.validation(vehicleInventoryClassBFactoryReportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(vehicleInventoryClassBFactoryReportPO);
        vehicleInventoryClassBFactoryReportDao.insert(vehicleInventoryClassBFactoryReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(VehicleInventoryClassBFactoryReportDTO vehicleInventoryClassBFactoryReportDTO) {
        // 0.数据转换
        VehicleInventoryClassBFactoryReportDO vehicleInventoryClassBFactoryReportDO = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassBFactoryReportDTO);
        VehicleInventoryClassBFactoryReportPO vehicleInventoryClassBFactoryReportPO = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassBFactoryReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassBFactoryReportDomainService.validation(vehicleInventoryClassBFactoryReportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(vehicleInventoryClassBFactoryReportPO);
        vehicleInventoryClassBFactoryReportDao.update(vehicleInventoryClassBFactoryReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<VehicleInventoryClassBFactoryReportDTO> list) {
        List<VehicleInventoryClassBFactoryReportPO> newList = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        vehicleInventoryClassBFactoryReportDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<VehicleInventoryClassBFactoryReportDTO> list) {
        List<VehicleInventoryClassBFactoryReportPO> newList = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        vehicleInventoryClassBFactoryReportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return vehicleInventoryClassBFactoryReportDao.deleteBatch(idList);
        }
        return vehicleInventoryClassBFactoryReportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public VehicleInventoryClassBFactoryReportVO selectByPrimaryKey(String id) {
        VehicleInventoryClassBFactoryReportPO po = vehicleInventoryClassBFactoryReportDao.selectByPrimaryKey(id);
        return VehicleInventoryClassBFactoryReportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_BFACTORY_REPORT")
    public List<VehicleInventoryClassBFactoryReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_BFACTORY_REPORT")
    public List<VehicleInventoryClassBFactoryReportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<VehicleInventoryClassBFactoryReportVO> dataList = vehicleInventoryClassBFactoryReportDao.selectByCondition(sortParam, queryCriteriaParam);
        VehicleInventoryClassBFactoryReportServiceImpl target = SpringBeanUtils.getBean(VehicleInventoryClassBFactoryReportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<VehicleInventoryClassBFactoryReportVO> selectByParams(Map<String, Object> params) {
        List<VehicleInventoryClassBFactoryReportPO> list = vehicleInventoryClassBFactoryReportDao.selectByParams(params);
        return VehicleInventoryClassBFactoryReportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<VehicleInventoryClassBFactoryReportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> generateReport() {

        return null;
    }

    @Override
    public String getObjectType() {
        // return ObjectTypeEnum.VEHICLE_INVENTORY_CLASS_BFACTORY_REPORT.getCode();
        return null;
    }

    @Override
    public List<VehicleInventoryClassBFactoryReportVO> invocation(List<VehicleInventoryClassBFactoryReportVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
