package com.yhl.scp.mrp.report.vehicle.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassBFactoryReportDO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassBFactoryReportDao;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBFactoryReportPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>VehicleInventoryClassBFactoryReportDomainService</code>
 * <p>
 * 车型库存（B类本厂）报表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:48:40
 */
@Service
public class VehicleInventoryClassBFactoryReportDomainService {

    @Resource
    private VehicleInventoryClassBFactoryReportDao vehicleInventoryClassBFactoryReportDao;

    /**
     * 数据校验
     *
     * @param vehicleInventoryClassBFactoryReportDO 领域对象
     */
    public void validation(VehicleInventoryClassBFactoryReportDO vehicleInventoryClassBFactoryReportDO) {
        checkNotNull(vehicleInventoryClassBFactoryReportDO);
        checkUniqueCode(vehicleInventoryClassBFactoryReportDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param vehicleInventoryClassBFactoryReportDO 领域对象
     */
    private void checkNotNull(VehicleInventoryClassBFactoryReportDO vehicleInventoryClassBFactoryReportDO) {}

    /**
     * 唯一性校验
     *
     * @param vehicleInventoryClassBFactoryReportDO 领域对象
     */
    private void checkUniqueCode(VehicleInventoryClassBFactoryReportDO vehicleInventoryClassBFactoryReportDO) {}

}
