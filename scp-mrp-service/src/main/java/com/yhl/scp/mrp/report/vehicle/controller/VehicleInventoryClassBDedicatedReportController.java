package com.yhl.scp.mrp.report.vehicle.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBDedicatedReportDTO;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassBDedicatedReportService;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBDedicatedReportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>VehicleInventoryClassBDedicatedReportController</code>
 * <p>
 * 车型库存（B类专用）报表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:47:30
 */
@Slf4j
@Api(tags = "车型库存（B类专用）报表控制器")
@RestController
@RequestMapping("vehicleInventoryClassBDedicatedReport")
public class VehicleInventoryClassBDedicatedReportController extends BaseController {

    @Resource
    private VehicleInventoryClassBDedicatedReportService vehicleInventoryClassBDedicatedReportService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<VehicleInventoryClassBDedicatedReportVO>> page() {
        List<VehicleInventoryClassBDedicatedReportVO> vehicleInventoryClassBDedicatedReportList = vehicleInventoryClassBDedicatedReportService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<VehicleInventoryClassBDedicatedReportVO> pageInfo = new PageInfo<>(vehicleInventoryClassBDedicatedReportList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody VehicleInventoryClassBDedicatedReportDTO vehicleInventoryClassBDedicatedReportDTO) {
        return vehicleInventoryClassBDedicatedReportService.doCreate(vehicleInventoryClassBDedicatedReportDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody VehicleInventoryClassBDedicatedReportDTO vehicleInventoryClassBDedicatedReportDTO) {
        return vehicleInventoryClassBDedicatedReportService.doUpdate(vehicleInventoryClassBDedicatedReportDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        vehicleInventoryClassBDedicatedReportService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<VehicleInventoryClassBDedicatedReportVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, vehicleInventoryClassBDedicatedReportService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "生成报表数据")
    @GetMapping(value = "generateReport")
    public BaseResponse<Void> generateReport() {
        String redisKey = String.join("#", RedisKeyManageEnum.VEHICLE_INVENTORY_CLASS_B_DEDICATED_REPORT_GENERATE.getKey());
        try {
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))) {
                return BaseResponse.error("当前已有报表正在生成，请等待生成完成");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId(), 60 * 5);
            // 生成报表
            return vehicleInventoryClassBDedicatedReportService.generateReport();
        } catch (Exception e) {
            log.error("生成车型库存（B类专用）报表失败", e);
            throw new BusinessException("生成车型库存（B类专用）报表失败,{0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
        }
    }
}
