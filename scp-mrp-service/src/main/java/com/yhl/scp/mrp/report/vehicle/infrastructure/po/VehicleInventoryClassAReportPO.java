package com.yhl.scp.mrp.report.vehicle.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>VehicleInventoryClassAReportPO</code>
 * <p>
 * 车型库存管控-A类车型维度报表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-25 17:18:41
 */
public class VehicleInventoryClassAReportPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 581409752720377363L;

    /**
     * 客户
     */
    private String customer;
    /**
     * 车型
     */
    private String vehicleModelCode;
    /**
     * 材料代码
     */
    private String materialCode;
    /**
     * 材料名称
     */
    private String materialName;
    /**
     * 材料类型
     */
    private String materialType;
    /**
     * 供应商代码
     */
    private String supplierCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 采购周期
     */
    private BigDecimal purchaseLot;
    /**
     * 未来30天日均需求
     */
    private BigDecimal futureThirtyDayDemand;
    /**
     * 福耀库存
     */
    private BigDecimal fuyaoInventoryQuantity;
    /**
     * 供应商库存
     */
    private BigDecimal supplierInventoryQuantity;
    /**
     * 未生产订单
     */
    private BigDecimal unprocessedOrder;
    /**
     * 现有库存可生产玻璃零件数量
     */
    private BigDecimal producedGlassQuantity;
    /**
     * 福耀库存可用天数
     */
    private BigDecimal fuyaoInventoryAvailableDay;
    /**
     * 供应商库存可用天数
     */
    private BigDecimal supplierInventoryAvailableDay;
    /**
     * 成本总金额
     */
    private BigDecimal costAmountTotal;
    /**
     * 福耀在库金额(万元)
     */
    private BigDecimal fuyaoInStockAmount;
    /**
     * 供应商库存金额(万元)
     */
    private BigDecimal supplierInStockAmount;
    /**
     * 日均需求金额
     */
    private BigDecimal dayDemandAmount;
    /**
     * 材料总天数
     */
    private BigDecimal materialTotalDay;
    /**
     * 管控标准
     */
    private BigDecimal controlStandards;
    /**
     * 版本
     */
    private Integer versionValue;

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getVehicleModelCode() {
        return vehicleModelCode;
    }

    public void setVehicleModelCode(String vehicleModelCode) {
        this.vehicleModelCode = vehicleModelCode;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public BigDecimal getPurchaseLot() {
        return purchaseLot;
    }

    public void setPurchaseLot(BigDecimal purchaseLot) {
        this.purchaseLot = purchaseLot;
    }

    public BigDecimal getFutureThirtyDayDemand() {
        return futureThirtyDayDemand;
    }

    public void setFutureThirtyDayDemand(BigDecimal futureThirtyDayDemand) {
        this.futureThirtyDayDemand = futureThirtyDayDemand;
    }

    public BigDecimal getFuyaoInventoryQuantity() {
        return fuyaoInventoryQuantity;
    }

    public void setFuyaoInventoryQuantity(BigDecimal fuyaoInventoryQuantity) {
        this.fuyaoInventoryQuantity = fuyaoInventoryQuantity;
    }

    public BigDecimal getSupplierInventoryQuantity() {
        return supplierInventoryQuantity;
    }

    public void setSupplierInventoryQuantity(BigDecimal supplierInventoryQuantity) {
        this.supplierInventoryQuantity = supplierInventoryQuantity;
    }

    public BigDecimal getUnprocessedOrder() {
        return unprocessedOrder;
    }

    public void setUnprocessedOrder(BigDecimal unprocessedOrder) {
        this.unprocessedOrder = unprocessedOrder;
    }

    public BigDecimal getProducedGlassQuantity() {
        return producedGlassQuantity;
    }

    public void setProducedGlassQuantity(BigDecimal producedGlassQuantity) {
        this.producedGlassQuantity = producedGlassQuantity;
    }

    public BigDecimal getFuyaoInventoryAvailableDay() {
        return fuyaoInventoryAvailableDay;
    }

    public void setFuyaoInventoryAvailableDay(BigDecimal fuyaoInventoryAvailableDay) {
        this.fuyaoInventoryAvailableDay = fuyaoInventoryAvailableDay;
    }

    public BigDecimal getSupplierInventoryAvailableDay() {
        return supplierInventoryAvailableDay;
    }

    public void setSupplierInventoryAvailableDay(BigDecimal supplierInventoryAvailableDay) {
        this.supplierInventoryAvailableDay = supplierInventoryAvailableDay;
    }

    public BigDecimal getCostAmountTotal() {
        return costAmountTotal;
    }

    public void setCostAmountTotal(BigDecimal costAmountTotal) {
        this.costAmountTotal = costAmountTotal;
    }

    public BigDecimal getFuyaoInStockAmount() {
        return fuyaoInStockAmount;
    }

    public void setFuyaoInStockAmount(BigDecimal fuyaoInStockAmount) {
        this.fuyaoInStockAmount = fuyaoInStockAmount;
    }

    public BigDecimal getSupplierInStockAmount() {
        return supplierInStockAmount;
    }

    public void setSupplierInStockAmount(BigDecimal supplierInStockAmount) {
        this.supplierInStockAmount = supplierInStockAmount;
    }

    public BigDecimal getDayDemandAmount() {
        return dayDemandAmount;
    }

    public void setDayDemandAmount(BigDecimal dayDemandAmount) {
        this.dayDemandAmount = dayDemandAmount;
    }

    public BigDecimal getMaterialTotalDay() {
        return materialTotalDay;
    }

    public void setMaterialTotalDay(BigDecimal materialTotalDay) {
        this.materialTotalDay = materialTotalDay;
    }

    public BigDecimal getControlStandards() {
        return controlStandards;
    }

    public void setControlStandards(BigDecimal controlStandards) {
        this.controlStandards = controlStandards;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
