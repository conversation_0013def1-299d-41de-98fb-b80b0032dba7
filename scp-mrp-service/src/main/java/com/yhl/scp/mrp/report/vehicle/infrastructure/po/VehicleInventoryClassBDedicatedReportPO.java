package com.yhl.scp.mrp.report.vehicle.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>VehicleInventoryClassBDedicatedReportPO</code>
 * <p>
 * 车型库存（B类专用）报表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:47:35
 */
public class VehicleInventoryClassBDedicatedReportPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 744096781222605240L;

    /**
     * 客户
     */
    private String customer;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    private String productFactoryCode;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 物料名称
     */
    private String productName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 采购周期
     */
    private BigDecimal purchaseLot;
    /**
     * 未来30天日均需求
     */
    private BigDecimal next30DaysAvgDailyDemand;
    /**
     * 本厂库存
     */
    private BigDecimal factoryInventory;
    /**
     * 供应商库存
     */
    private BigDecimal supplierInventory;
    /**
     * 现有库存可生产玻璃零件数量
     */
    private BigDecimal currentStockPartProdQuantity;
    /**
     * 福耀库存可用天数
     */
    private Integer factoryInventoryUseDay;
    /**
     * 供应商库存可用天数
     */
    private Integer supplierInventoryUseDay;
    /**
     * 材料总天数
     */
    private Integer materialTotalDay;
    /**
     * 成本总金额（万元）
     */
    private BigDecimal totalCostAmountThousand;
    /**
     * 本厂库存金额（万元）
     */
    private BigDecimal factoryInventoryAmountThousand;
    /**
     * 供应商库存金额（万元）
     */
    private BigDecimal supplierInventoryAmountThousand;
    /**
     * 日均需求金额
     */
    private BigDecimal dailyAverageDemandAmount;
    /**
     * 管控标准
     */
    private BigDecimal controlStandard;
    /**
     * 版本号
     */
    private Integer versionValue;

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getVehicleModelCode() {
        return vehicleModelCode;
    }

    public void setVehicleModelCode(String vehicleModelCode) {
        this.vehicleModelCode = vehicleModelCode;
    }

    public String getProductFactoryCode() {
        return productFactoryCode;
    }

    public void setProductFactoryCode(String productFactoryCode) {
        this.productFactoryCode = productFactoryCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public BigDecimal getPurchaseLot() {
        return purchaseLot;
    }

    public void setPurchaseLot(BigDecimal purchaseLot) {
        this.purchaseLot = purchaseLot;
    }

    public BigDecimal getNext30DaysAvgDailyDemand() {
        return next30DaysAvgDailyDemand;
    }

    public void setNext30DaysAvgDailyDemand(BigDecimal next30DaysAvgDailyDemand) {
        this.next30DaysAvgDailyDemand = next30DaysAvgDailyDemand;
    }

    public BigDecimal getFactoryInventory() {
        return factoryInventory;
    }

    public void setFactoryInventory(BigDecimal factoryInventory) {
        this.factoryInventory = factoryInventory;
    }

    public BigDecimal getSupplierInventory() {
        return supplierInventory;
    }

    public void setSupplierInventory(BigDecimal supplierInventory) {
        this.supplierInventory = supplierInventory;
    }

    public BigDecimal getCurrentStockPartProdQuantity() {
        return currentStockPartProdQuantity;
    }

    public void setCurrentStockPartProdQuantity(BigDecimal currentStockPartProdQuantity) {
        this.currentStockPartProdQuantity = currentStockPartProdQuantity;
    }

    public Integer getFactoryInventoryUseDay() {
        return factoryInventoryUseDay;
    }

    public void setFactoryInventoryUseDay(Integer factoryInventoryUseDay) {
        this.factoryInventoryUseDay = factoryInventoryUseDay;
    }

    public Integer getSupplierInventoryUseDay() {
        return supplierInventoryUseDay;
    }

    public void setSupplierInventoryUseDay(Integer supplierInventoryUseDay) {
        this.supplierInventoryUseDay = supplierInventoryUseDay;
    }

    public Integer getMaterialTotalDay() {
        return materialTotalDay;
    }

    public void setMaterialTotalDay(Integer materialTotalDay) {
        this.materialTotalDay = materialTotalDay;
    }

    public BigDecimal getTotalCostAmountThousand() {
        return totalCostAmountThousand;
    }

    public void setTotalCostAmountThousand(BigDecimal totalCostAmountThousand) {
        this.totalCostAmountThousand = totalCostAmountThousand;
    }

    public BigDecimal getFactoryInventoryAmountThousand() {
        return factoryInventoryAmountThousand;
    }

    public void setFactoryInventoryAmountThousand(BigDecimal factoryInventoryAmountThousand) {
        this.factoryInventoryAmountThousand = factoryInventoryAmountThousand;
    }

    public BigDecimal getSupplierInventoryAmountThousand() {
        return supplierInventoryAmountThousand;
    }

    public void setSupplierInventoryAmountThousand(BigDecimal supplierInventoryAmountThousand) {
        this.supplierInventoryAmountThousand = supplierInventoryAmountThousand;
    }

    public BigDecimal getDailyAverageDemandAmount() {
        return dailyAverageDemandAmount;
    }

    public void setDailyAverageDemandAmount(BigDecimal dailyAverageDemandAmount) {
        this.dailyAverageDemandAmount = dailyAverageDemandAmount;
    }

    public BigDecimal getControlStandard() {
        return controlStandard;
    }

    public void setControlStandard(BigDecimal controlStandard) {
        this.controlStandard = controlStandard;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
