package com.yhl.scp.mrp.report.vehicle.domain.factory;

import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassBFactoryReportDO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBFactoryReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassBFactoryReportDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>VehicleInventoryClassBFactoryReportFactory</code>
 * <p>
 * 车型库存（B类本厂）报表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:48:40
 */
@Component
public class VehicleInventoryClassBFactoryReportFactory {

    @Resource
    private VehicleInventoryClassBFactoryReportDao vehicleInventoryClassBFactoryReportDao;

    VehicleInventoryClassBFactoryReportDO create(VehicleInventoryClassBFactoryReportDTO dto) {
        // TODO
        return null;
    }

}
