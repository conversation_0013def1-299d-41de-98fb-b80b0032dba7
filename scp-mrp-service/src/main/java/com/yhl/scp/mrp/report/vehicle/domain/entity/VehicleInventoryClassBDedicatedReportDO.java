package com.yhl.scp.mrp.report.vehicle.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>VehicleInventoryClassBDedicatedReportDO</code>
 * <p>
 * 车型库存（B类专用）报表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:47:35
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleInventoryClassBDedicatedReportDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 827769229600001047L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 客户
     */
    private String customer;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    private String productFactoryCode;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 物料名称
     */
    private String productName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 采购周期
     */
    private BigDecimal purchaseLot;
    /**
     * 未来30天日均需求
     */
    private BigDecimal next30DaysAvgDailyDemand;
    /**
     * 本厂库存
     */
    private BigDecimal factoryInventory;
    /**
     * 供应商库存
     */
    private BigDecimal supplierInventory;
    /**
     * 现有库存可生产玻璃零件数量
     */
    private BigDecimal currentStockPartProdQuantity;
    /**
     * 福耀库存可用天数
     */
    private Integer factoryInventoryUseDay;
    /**
     * 供应商库存可用天数
     */
    private Integer supplierInventoryUseDay;
    /**
     * 材料总天数
     */
    private Integer materialTotalDay;
    /**
     * 成本总金额（万元）
     */
    private BigDecimal totalCostAmountThousand;
    /**
     * 本厂库存金额（万元）
     */
    private BigDecimal factoryInventoryAmountThousand;
    /**
     * 供应商库存金额（万元）
     */
    private BigDecimal supplierInventoryAmountThousand;
    /**
     * 日均需求金额
     */
    private BigDecimal dailyAverageDemandAmount;
    /**
     * 管控标准
     */
    private BigDecimal controlStandard;
    /**
     * 版本号
     */
    private Integer versionValue;

}
