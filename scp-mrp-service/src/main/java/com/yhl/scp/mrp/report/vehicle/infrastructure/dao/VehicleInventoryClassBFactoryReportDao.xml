<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassBFactoryReportDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBFactoryReportPO">
        <!--@Table mrp_vehicle_inventory_class_b_factory_report-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="customer" jdbcType="VARCHAR" property="customer"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="product_factory_code" jdbcType="VARCHAR" property="productFactoryCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="purchase_lot" jdbcType="VARCHAR" property="purchaseLot"/>
        <result column="input_factor" jdbcType="VARCHAR" property="inputFactor"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="output_rate" jdbcType="VARCHAR" property="outputRate"/>
        <result column="material_predicted_proportion" jdbcType="VARCHAR" property="materialPredictedProportion"/>
        <result column="material_predicted_proportion_vehicle" jdbcType="VARCHAR"
                property="materialPredictedProportionVehicle"/>
        <result column="is_dedicated_ge50pct" jdbcType="VARCHAR" property="isDedicatedGe50pct"/>
        <result column="next_30_days_daily_average_demand" jdbcType="VARCHAR" property="next30DaysDailyAverageDemand"/>
        <result column="auxiliary_inventory" jdbcType="VARCHAR" property="auxiliaryInventory"/>
        <result column="supplier_inventory" jdbcType="VARCHAR" property="supplierInventory"/>
        <result column="current_stock_part_prod_quantity" jdbcType="VARCHAR" property="currentStockPartProdQuantity"/>
        <result column="factory_inventory_use_day" jdbcType="INTEGER" property="factoryInventoryUseDay"/>
        <result column="supplier_inventory_use_day" jdbcType="INTEGER" property="supplierInventoryUseDay"/>
        <result column="material_total_day" jdbcType="INTEGER" property="materialTotalDay"/>
        <result column="forecast_date" jdbcType="TIMESTAMP" property="forecastDate"/>
        <result column="forecast_quantity" jdbcType="VARCHAR" property="forecastQuantity"/>
        <result column="total_cost_amount_thousand" jdbcType="VARCHAR" property="totalCostAmountThousand"/>
        <result column="factory_inventory_amount_thousand" jdbcType="VARCHAR"
                property="factoryInventoryAmountThousand"/>
        <result column="supplier_inventory_amount_thousand" jdbcType="VARCHAR"
                property="supplierInventoryAmountThousand"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBFactoryReportVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,customer,vehicle_model_code,product_factory_code,product_code,product_name,supplier_code,purchase_lot,input_factor,unit,output_rate,material_predicted_proportion,material_predicted_proportion_vehicle,is_dedicated_ge50pct,next_30_days_daily_average_demand,auxiliary_inventory,supplier_inventory,current_stock_part_prod_quantity,factory_inventory_use_day,supplier_inventory_use_day,material_total_day,forecast_date,forecast_quantity,total_cost_amount_thousand,factory_inventory_amount_thousand,supplier_inventory_amount_thousand,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.customer != null and params.customer != ''">
                and customer = #{params.customer,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productFactoryCode != null and params.productFactoryCode != ''">
                and product_factory_code = #{params.productFactoryCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierCode != null and params.supplierCode != ''">
                and supplier_code = #{params.supplierCode,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseLot != null">
                and purchase_lot = #{params.purchaseLot,jdbcType=VARCHAR}
            </if>
            <if test="params.inputFactor != null">
                and input_factor = #{params.inputFactor,jdbcType=VARCHAR}
            </if>
            <if test="params.unit != null and params.unit != ''">
                and unit = #{params.unit,jdbcType=VARCHAR}
            </if>
            <if test="params.outputRate != null">
                and output_rate = #{params.outputRate,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPredictedProportion != null and params.materialPredictedProportion != ''">
                and material_predicted_proportion = #{params.materialPredictedProportion,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPredictedProportionVehicle != null and params.materialPredictedProportionVehicle != ''">
                and material_predicted_proportion_vehicle =
                #{params.materialPredictedProportionVehicle,jdbcType=VARCHAR}
            </if>
            <if test="params.isDedicatedGe50pct != null and params.isDedicatedGe50pct != ''">
                and is_dedicated_ge50pct = #{params.isDedicatedGe50pct,jdbcType=VARCHAR}
            </if>
            <if test="params.next30DaysDailyAverageDemand != null">
                and next_30_days_daily_average_demand = #{params.next30DaysDailyAverageDemand,jdbcType=VARCHAR}
            </if>
            <if test="params.auxiliaryInventory != null">
                and auxiliary_inventory = #{params.auxiliaryInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierInventory != null">
                and supplier_inventory = #{params.supplierInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.currentStockPartProdQuantity != null">
                and current_stock_part_prod_quantity = #{params.currentStockPartProdQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.factoryInventoryUseDay != null">
                and factory_inventory_use_day = #{params.factoryInventoryUseDay,jdbcType=INTEGER}
            </if>
            <if test="params.supplierInventoryUseDay != null">
                and supplier_inventory_use_day = #{params.supplierInventoryUseDay,jdbcType=INTEGER}
            </if>
            <if test="params.materialTotalDay != null">
                and material_total_day = #{params.materialTotalDay,jdbcType=INTEGER}
            </if>
            <if test="params.forecastDate != null">
                and forecast_date = #{params.forecastDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.forecastQuantity != null">
                and forecast_quantity = #{params.forecastQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.totalCostAmountThousand != null">
                and total_cost_amount_thousand = #{params.totalCostAmountThousand,jdbcType=VARCHAR}
            </if>
            <if test="params.factoryInventoryAmountThousand != null">
                and factory_inventory_amount_thousand = #{params.factoryInventoryAmountThousand,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierInventoryAmountThousand != null">
                and supplier_inventory_amount_thousand = #{params.supplierInventoryAmountThousand,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_vehicle_inventory_class_b_factory_report
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_vehicle_inventory_class_b_factory_report
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_vehicle_inventory_class_b_factory_report
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_vehicle_inventory_class_b_factory_report
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBFactoryReportPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_vehicle_inventory_class_b_factory_report(
        id,
        customer,
        vehicle_model_code,
        product_factory_code,
        product_code,
        product_name,
        supplier_code,
        purchase_lot,
        input_factor,
        unit,
        output_rate,
        material_predicted_proportion,
        material_predicted_proportion_vehicle,
        is_dedicated_ge50pct,
        next_30_days_daily_average_demand,
        auxiliary_inventory,
        supplier_inventory,
        current_stock_part_prod_quantity,
        factory_inventory_use_day,
        supplier_inventory_use_day,
        material_total_day,
        forecast_date,
        forecast_quantity,
        total_cost_amount_thousand,
        factory_inventory_amount_thousand,
        supplier_inventory_amount_thousand,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{customer,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{productFactoryCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{purchaseLot,jdbcType=VARCHAR},
        #{inputFactor,jdbcType=VARCHAR},
        #{unit,jdbcType=VARCHAR},
        #{outputRate,jdbcType=VARCHAR},
        #{materialPredictedProportion,jdbcType=VARCHAR},
        #{materialPredictedProportionVehicle,jdbcType=VARCHAR},
        #{isDedicatedGe50pct,jdbcType=VARCHAR},
        #{next30DaysDailyAverageDemand,jdbcType=VARCHAR},
        #{auxiliaryInventory,jdbcType=VARCHAR},
        #{supplierInventory,jdbcType=VARCHAR},
        #{currentStockPartProdQuantity,jdbcType=VARCHAR},
        #{factoryInventoryUseDay,jdbcType=INTEGER},
        #{supplierInventoryUseDay,jdbcType=INTEGER},
        #{materialTotalDay,jdbcType=INTEGER},
        #{forecastDate,jdbcType=TIMESTAMP},
        #{forecastQuantity,jdbcType=VARCHAR},
        #{totalCostAmountThousand,jdbcType=VARCHAR},
        #{factoryInventoryAmountThousand,jdbcType=VARCHAR},
        #{supplierInventoryAmountThousand,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBFactoryReportPO">
        insert into mrp_vehicle_inventory_class_b_factory_report(id,
                                                                 customer,
                                                                 vehicle_model_code,
                                                                 product_factory_code,
                                                                 product_code,
                                                                 product_name,
                                                                 supplier_code,
                                                                 purchase_lot,
                                                                 input_factor,
                                                                 unit,
                                                                 output_rate,
                                                                 material_predicted_proportion,
                                                                 material_predicted_proportion_vehicle,
                                                                 is_dedicated_ge50pct,
                                                                 next_30_days_daily_average_demand,
                                                                 auxiliary_inventory,
                                                                 supplier_inventory,
                                                                 current_stock_part_prod_quantity,
                                                                 factory_inventory_use_day,
                                                                 supplier_inventory_use_day,
                                                                 material_total_day,
                                                                 forecast_date,
                                                                 forecast_quantity,
                                                                 total_cost_amount_thousand,
                                                                 factory_inventory_amount_thousand,
                                                                 supplier_inventory_amount_thousand,
                                                                 remark,
                                                                 enabled,
                                                                 creator,
                                                                 create_time,
                                                                 modifier,
                                                                 modify_time,
                                                                 version_value)
        values (#{id,jdbcType=VARCHAR},
                #{customer,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{productFactoryCode,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productName,jdbcType=VARCHAR},
                #{supplierCode,jdbcType=VARCHAR},
                #{purchaseLot,jdbcType=VARCHAR},
                #{inputFactor,jdbcType=VARCHAR},
                #{unit,jdbcType=VARCHAR},
                #{outputRate,jdbcType=VARCHAR},
                #{materialPredictedProportion,jdbcType=VARCHAR},
                #{materialPredictedProportionVehicle,jdbcType=VARCHAR},
                #{isDedicatedGe50pct,jdbcType=VARCHAR},
                #{next30DaysDailyAverageDemand,jdbcType=VARCHAR},
                #{auxiliaryInventory,jdbcType=VARCHAR},
                #{supplierInventory,jdbcType=VARCHAR},
                #{currentStockPartProdQuantity,jdbcType=VARCHAR},
                #{factoryInventoryUseDay,jdbcType=INTEGER},
                #{supplierInventoryUseDay,jdbcType=INTEGER},
                #{materialTotalDay,jdbcType=INTEGER},
                #{forecastDate,jdbcType=TIMESTAMP},
                #{forecastQuantity,jdbcType=VARCHAR},
                #{totalCostAmountThousand,jdbcType=VARCHAR},
                #{factoryInventoryAmountThousand,jdbcType=VARCHAR},
                #{supplierInventoryAmountThousand,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_vehicle_inventory_class_b_factory_report(
        id,
        customer,
        vehicle_model_code,
        product_factory_code,
        product_code,
        product_name,
        supplier_code,
        purchase_lot,
        input_factor,
        unit,
        output_rate,
        material_predicted_proportion,
        material_predicted_proportion_vehicle,
        is_dedicated_ge50pct,
        next_30_days_daily_average_demand,
        auxiliary_inventory,
        supplier_inventory,
        current_stock_part_prod_quantity,
        factory_inventory_use_day,
        supplier_inventory_use_day,
        material_total_day,
        forecast_date,
        forecast_quantity,
        total_cost_amount_thousand,
        factory_inventory_amount_thousand,
        supplier_inventory_amount_thousand,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.customer,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.productFactoryCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.purchaseLot,jdbcType=VARCHAR},
            #{entity.inputFactor,jdbcType=VARCHAR},
            #{entity.unit,jdbcType=VARCHAR},
            #{entity.outputRate,jdbcType=VARCHAR},
            #{entity.materialPredictedProportion,jdbcType=VARCHAR},
            #{entity.materialPredictedProportionVehicle,jdbcType=VARCHAR},
            #{entity.isDedicatedGe50pct,jdbcType=VARCHAR},
            #{entity.next30DaysDailyAverageDemand,jdbcType=VARCHAR},
            #{entity.auxiliaryInventory,jdbcType=VARCHAR},
            #{entity.supplierInventory,jdbcType=VARCHAR},
            #{entity.currentStockPartProdQuantity,jdbcType=VARCHAR},
            #{entity.factoryInventoryUseDay,jdbcType=INTEGER},
            #{entity.supplierInventoryUseDay,jdbcType=INTEGER},
            #{entity.materialTotalDay,jdbcType=INTEGER},
            #{entity.forecastDate,jdbcType=TIMESTAMP},
            #{entity.forecastQuantity,jdbcType=VARCHAR},
            #{entity.totalCostAmountThousand,jdbcType=VARCHAR},
            #{entity.factoryInventoryAmountThousand,jdbcType=VARCHAR},
            #{entity.supplierInventoryAmountThousand,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_vehicle_inventory_class_b_factory_report(
        id,
        customer,
        vehicle_model_code,
        product_factory_code,
        product_code,
        product_name,
        supplier_code,
        purchase_lot,
        input_factor,
        unit,
        output_rate,
        material_predicted_proportion,
        material_predicted_proportion_vehicle,
        is_dedicated_ge50pct,
        next_30_days_daily_average_demand,
        auxiliary_inventory,
        supplier_inventory,
        current_stock_part_prod_quantity,
        factory_inventory_use_day,
        supplier_inventory_use_day,
        material_total_day,
        forecast_date,
        forecast_quantity,
        total_cost_amount_thousand,
        factory_inventory_amount_thousand,
        supplier_inventory_amount_thousand,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.customer,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.productFactoryCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.purchaseLot,jdbcType=VARCHAR},
            #{entity.inputFactor,jdbcType=VARCHAR},
            #{entity.unit,jdbcType=VARCHAR},
            #{entity.outputRate,jdbcType=VARCHAR},
            #{entity.materialPredictedProportion,jdbcType=VARCHAR},
            #{entity.materialPredictedProportionVehicle,jdbcType=VARCHAR},
            #{entity.isDedicatedGe50pct,jdbcType=VARCHAR},
            #{entity.next30DaysDailyAverageDemand,jdbcType=VARCHAR},
            #{entity.auxiliaryInventory,jdbcType=VARCHAR},
            #{entity.supplierInventory,jdbcType=VARCHAR},
            #{entity.currentStockPartProdQuantity,jdbcType=VARCHAR},
            #{entity.factoryInventoryUseDay,jdbcType=INTEGER},
            #{entity.supplierInventoryUseDay,jdbcType=INTEGER},
            #{entity.materialTotalDay,jdbcType=INTEGER},
            #{entity.forecastDate,jdbcType=TIMESTAMP},
            #{entity.forecastQuantity,jdbcType=VARCHAR},
            #{entity.totalCostAmountThousand,jdbcType=VARCHAR},
            #{entity.factoryInventoryAmountThousand,jdbcType=VARCHAR},
            #{entity.supplierInventoryAmountThousand,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBFactoryReportPO">
        update mrp_vehicle_inventory_class_b_factory_report
        set customer                              = #{customer,jdbcType=VARCHAR},
            vehicle_model_code                    = #{vehicleModelCode,jdbcType=VARCHAR},
            product_factory_code                  = #{productFactoryCode,jdbcType=VARCHAR},
            product_code                          = #{productCode,jdbcType=VARCHAR},
            product_name                          = #{productName,jdbcType=VARCHAR},
            supplier_code                         = #{supplierCode,jdbcType=VARCHAR},
            purchase_lot                          = #{purchaseLot,jdbcType=VARCHAR},
            input_factor                          = #{inputFactor,jdbcType=VARCHAR},
            unit                                  = #{unit,jdbcType=VARCHAR},
            output_rate                           = #{outputRate,jdbcType=VARCHAR},
            material_predicted_proportion         = #{materialPredictedProportion,jdbcType=VARCHAR},
            material_predicted_proportion_vehicle = #{materialPredictedProportionVehicle,jdbcType=VARCHAR},
            is_dedicated_ge50pct                  = #{isDedicatedGe50pct,jdbcType=VARCHAR},
            next_30_days_daily_average_demand     = #{next30DaysDailyAverageDemand,jdbcType=VARCHAR},
            auxiliary_inventory                   = #{auxiliaryInventory,jdbcType=VARCHAR},
            supplier_inventory                    = #{supplierInventory,jdbcType=VARCHAR},
            current_stock_part_prod_quantity      = #{currentStockPartProdQuantity,jdbcType=VARCHAR},
            factory_inventory_use_day             = #{factoryInventoryUseDay,jdbcType=INTEGER},
            supplier_inventory_use_day            = #{supplierInventoryUseDay,jdbcType=INTEGER},
            material_total_day                    = #{materialTotalDay,jdbcType=INTEGER},
            forecast_date                         = #{forecastDate,jdbcType=TIMESTAMP},
            forecast_quantity                     = #{forecastQuantity,jdbcType=VARCHAR},
            total_cost_amount_thousand            = #{totalCostAmountThousand,jdbcType=VARCHAR},
            factory_inventory_amount_thousand     = #{factoryInventoryAmountThousand,jdbcType=VARCHAR},
            supplier_inventory_amount_thousand    = #{supplierInventoryAmountThousand,jdbcType=VARCHAR},
            remark                                = #{remark,jdbcType=VARCHAR},
            enabled                               = #{enabled,jdbcType=VARCHAR},
            modifier                              = #{modifier,jdbcType=VARCHAR},
            modify_time                           = #{modifyTime,jdbcType=TIMESTAMP},
            version_value                         = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBFactoryReportPO">
        update mrp_vehicle_inventory_class_b_factory_report
        <set>
            <if test="item.customer != null and item.customer != ''">
                customer = #{item.customer,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productFactoryCode != null and item.productFactoryCode != ''">
                product_factory_code = #{item.productFactoryCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null and item.supplierCode != ''">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseLot != null">
                purchase_lot = #{item.purchaseLot,jdbcType=VARCHAR},
            </if>
            <if test="item.inputFactor != null">
                input_factor = #{item.inputFactor,jdbcType=VARCHAR},
            </if>
            <if test="item.unit != null and item.unit != ''">
                unit = #{item.unit,jdbcType=VARCHAR},
            </if>
            <if test="item.outputRate != null">
                output_rate = #{item.outputRate,jdbcType=VARCHAR},
            </if>
            <if test="item.materialPredictedProportion != null and item.materialPredictedProportion != ''">
                material_predicted_proportion = #{item.materialPredictedProportion,jdbcType=VARCHAR},
            </if>
            <if test="item.materialPredictedProportionVehicle != null and item.materialPredictedProportionVehicle != ''">
                material_predicted_proportion_vehicle = #{item.materialPredictedProportionVehicle,jdbcType=VARCHAR},
            </if>
            <if test="item.isDedicatedGe50pct != null and item.isDedicatedGe50pct != ''">
                is_dedicated_ge50pct = #{item.isDedicatedGe50pct,jdbcType=VARCHAR},
            </if>
            <if test="item.next30DaysDailyAverageDemand != null">
                next_30_days_daily_average_demand = #{item.next30DaysDailyAverageDemand,jdbcType=VARCHAR},
            </if>
            <if test="item.auxiliaryInventory != null">
                auxiliary_inventory = #{item.auxiliaryInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierInventory != null">
                supplier_inventory = #{item.supplierInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.currentStockPartProdQuantity != null">
                current_stock_part_prod_quantity = #{item.currentStockPartProdQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.factoryInventoryUseDay != null">
                factory_inventory_use_day = #{item.factoryInventoryUseDay,jdbcType=INTEGER},
            </if>
            <if test="item.supplierInventoryUseDay != null">
                supplier_inventory_use_day = #{item.supplierInventoryUseDay,jdbcType=INTEGER},
            </if>
            <if test="item.materialTotalDay != null">
                material_total_day = #{item.materialTotalDay,jdbcType=INTEGER},
            </if>
            <if test="item.forecastDate != null">
                forecast_date = #{item.forecastDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.forecastQuantity != null">
                forecast_quantity = #{item.forecastQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.totalCostAmountThousand != null">
                total_cost_amount_thousand = #{item.totalCostAmountThousand,jdbcType=VARCHAR},
            </if>
            <if test="item.factoryInventoryAmountThousand != null">
                factory_inventory_amount_thousand = #{item.factoryInventoryAmountThousand,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierInventoryAmountThousand != null">
                supplier_inventory_amount_thousand = #{item.supplierInventoryAmountThousand,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_vehicle_inventory_class_b_factory_report
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="customer = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customer,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_factory_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productFactoryCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_lot = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseLot,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="input_factor = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inputFactor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="output_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.outputRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_predicted_proportion = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPredictedProportion,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_predicted_proportion_vehicle = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.materialPredictedProportionVehicle,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_dedicated_ge50pct = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.isDedicatedGe50pct,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="next_30_days_daily_average_demand = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.next30DaysDailyAverageDemand,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="auxiliary_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.auxiliaryInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="current_stock_part_prod_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currentStockPartProdQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="factory_inventory_use_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.factoryInventoryUseDay,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="supplier_inventory_use_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierInventoryUseDay,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="material_total_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialTotalDay,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="forecast_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="forecast_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="total_cost_amount_thousand = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.totalCostAmountThousand,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="factory_inventory_amount_thousand = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.factoryInventoryAmountThousand,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_inventory_amount_thousand = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierInventoryAmountThousand,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_vehicle_inventory_class_b_factory_report
            <set>
                <if test="item.customer != null and item.customer != ''">
                    customer = #{item.customer,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productFactoryCode != null and item.productFactoryCode != ''">
                    product_factory_code = #{item.productFactoryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productName != null and item.productName != ''">
                    product_name = #{item.productName,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierCode != null and item.supplierCode != ''">
                    supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseLot != null">
                    purchase_lot = #{item.purchaseLot,jdbcType=VARCHAR},
                </if>
                <if test="item.inputFactor != null">
                    input_factor = #{item.inputFactor,jdbcType=VARCHAR},
                </if>
                <if test="item.unit != null and item.unit != ''">
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.outputRate != null">
                    output_rate = #{item.outputRate,jdbcType=VARCHAR},
                </if>
                <if test="item.materialPredictedProportion != null and item.materialPredictedProportion != ''">
                    material_predicted_proportion = #{item.materialPredictedProportion,jdbcType=VARCHAR},
                </if>
                <if test="item.materialPredictedProportionVehicle != null and item.materialPredictedProportionVehicle != ''">
                    material_predicted_proportion_vehicle = #{item.materialPredictedProportionVehicle,jdbcType=VARCHAR},
                </if>
                <if test="item.isDedicatedGe50pct != null and item.isDedicatedGe50pct != ''">
                    is_dedicated_ge50pct = #{item.isDedicatedGe50pct,jdbcType=VARCHAR},
                </if>
                <if test="item.next30DaysDailyAverageDemand != null">
                    next_30_days_daily_average_demand = #{item.next30DaysDailyAverageDemand,jdbcType=VARCHAR},
                </if>
                <if test="item.auxiliaryInventory != null">
                    auxiliary_inventory = #{item.auxiliaryInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierInventory != null">
                    supplier_inventory = #{item.supplierInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.currentStockPartProdQuantity != null">
                    current_stock_part_prod_quantity = #{item.currentStockPartProdQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.factoryInventoryUseDay != null">
                    factory_inventory_use_day = #{item.factoryInventoryUseDay,jdbcType=INTEGER},
                </if>
                <if test="item.supplierInventoryUseDay != null">
                    supplier_inventory_use_day = #{item.supplierInventoryUseDay,jdbcType=INTEGER},
                </if>
                <if test="item.materialTotalDay != null">
                    material_total_day = #{item.materialTotalDay,jdbcType=INTEGER},
                </if>
                <if test="item.forecastDate != null">
                    forecast_date = #{item.forecastDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.forecastQuantity != null">
                    forecast_quantity = #{item.forecastQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.totalCostAmountThousand != null">
                    total_cost_amount_thousand = #{item.totalCostAmountThousand,jdbcType=VARCHAR},
                </if>
                <if test="item.factoryInventoryAmountThousand != null">
                    factory_inventory_amount_thousand = #{item.factoryInventoryAmountThousand,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierInventoryAmountThousand != null">
                    supplier_inventory_amount_thousand = #{item.supplierInventoryAmountThousand,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_vehicle_inventory_class_b_factory_report
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_vehicle_inventory_class_b_factory_report where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
