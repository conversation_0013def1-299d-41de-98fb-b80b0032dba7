package com.yhl.scp.mrp.report.vehicle.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>VehicleInventoryClassBFactoryReportDO</code>
 * <p>
 * 车型库存（B类本厂）报表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:48:39
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleInventoryClassBFactoryReportDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -47462145951817668L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 客户
     */
    private String customer;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    private String productFactoryCode;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 物料名称
     */
    private String productName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 采购周期
     */
    private BigDecimal purchaseLot;
    /**
     * 单耗
     */
    private BigDecimal inputFactor;
    /**
     * 单位
     */
    private String unit;
    /**
     * 产出率
     */
    private BigDecimal outputRate;
    /**
     * 材料预测占比
     */
    private String materialPredictedProportion;
    /**
     * 材料预测占比（车型）
     */
    private String materialPredictedProportionVehicle;
    /**
     * 是否专用（占比>=50%）
     */
    private String isDedicatedGe50pct;
    /**
     * 未来30天日均需求
     */
    private BigDecimal next30DaysDailyAverageDemand;
    /**
     * 辅料库存
     */
    private BigDecimal auxiliaryInventory;
    /**
     * 供应商库存
     */
    private BigDecimal supplierInventory;
    /**
     * 现有库存可生产玻璃零件数量
     */
    private BigDecimal currentStockPartProdQuantity;
    /**
     * 福耀库存可用天数
     */
    private Integer factoryInventoryUseDay;
    /**
     * 供应商库存可用天数
     */
    private Integer supplierInventoryUseDay;
    /**
     * 材料总天数
     */
    private Integer materialTotalDay;
    /**
     * 预测日期
     */
    private Date forecastDate;
    /**
     * 预测数量
     */
    private BigDecimal forecastQuantity;
    /**
     * 成本总金额（万元）
     */
    private BigDecimal totalCostAmountThousand;
    /**
     * 本厂库存金额（万元）
     */
    private BigDecimal factoryInventoryAmountThousand;
    /**
     * 供应商库存金额（万元）
     */
    private BigDecimal supplierInventoryAmountThousand;
    /**
     * 版本号
     */
    private Integer versionValue;

}
