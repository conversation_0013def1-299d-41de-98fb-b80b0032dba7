package com.yhl.scp.mrp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName MaterialGrossDemandJob
 * @Description TODO
 * @Date 2025-04-15 19:22:11
 * <AUTHOR>
 * @Copyright rzz
 * @Version 1.0
 */
@Component
@Slf4j
public class MaterialGrossDemandJob {

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @XxlJob("materialGrossDemandJobHandler")
    public ReturnT<String> materialGrossDemandJobHandler() {
        XxlJobHelper.log("毛需求计算Job开始执行");

        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MRP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MRP模块信息");
            return ReturnT.SUCCESS;
        }
        MaterialGrossDemandJob job = SpringBeanUtils.getBean("materialGrossDemandJob");
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("接收調度中心参数...[{}]", jobParam);
        for (Scenario scenario : scenarios) {
            BaseResponse<Void> baseResponse = materialGrossDemandService.checkCapacityBalancePublishDate(scenario.getDataBaseName());
            if (!baseResponse.getSuccess()){
                XxlJobHelper.log(baseResponse.getMsg());
                return ReturnT.FAIL;
            }

            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            job.doJob(scenario.getDataBaseName());
            DynamicDataSourceContextHolder.clearDataSource();
        }
        XxlJobHelper.log("毛需求计算Job执行结束");
        return ReturnT.SUCCESS;
    }

    @BusinessMonitorLog(businessCode = "毛需求自动计算", moduleCode = "MRP", businessFrequency = "DAY")
    public void doJob(String scenario) {
        materialGrossDemandService.doComputeDemand(true, scenario, "DELIVERY_PLAN_DEMAND");
    }
}
