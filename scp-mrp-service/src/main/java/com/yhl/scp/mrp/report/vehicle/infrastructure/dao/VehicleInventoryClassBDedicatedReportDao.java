package com.yhl.scp.mrp.report.vehicle.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBDedicatedReportPO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBDedicatedReportVO;

/**
 * <code>VehicleInventoryClassBDedicatedReportDao</code>
 * <p>
 * 车型库存（B类专用）报表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:47:31
 */
public interface VehicleInventoryClassBDedicatedReportDao extends BaseDao<VehicleInventoryClassBDedicatedReportPO, VehicleInventoryClassBDedicatedReportVO> {

}
