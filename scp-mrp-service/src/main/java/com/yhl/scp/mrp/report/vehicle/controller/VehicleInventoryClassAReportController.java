package com.yhl.scp.mrp.report.vehicle.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassAReportDTO;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassAReportService;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassAReportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>VehicleInventoryClassAReportController</code>
 * <p>
 * 车型库存管控-A类车型维度报表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-25 17:18:35
 */
@Slf4j
@Api(tags = "车型库存管控-A类车型维度报表控制器")
@RestController
@RequestMapping("vehicleInventoryClassAReport")
public class VehicleInventoryClassAReportController extends BaseController {

    @Resource
    private VehicleInventoryClassAReportService vehicleInventoryClassAReportService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<VehicleInventoryClassAReportVO>> page() {
        List<VehicleInventoryClassAReportVO> vehicleInventoryClassAReportList = vehicleInventoryClassAReportService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<VehicleInventoryClassAReportVO> pageInfo = new PageInfo<>(vehicleInventoryClassAReportList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody VehicleInventoryClassAReportDTO vehicleInventoryClassAReportDTO) {
        return vehicleInventoryClassAReportService.doCreate(vehicleInventoryClassAReportDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody VehicleInventoryClassAReportDTO vehicleInventoryClassAReportDTO) {
        return vehicleInventoryClassAReportService.doUpdate(vehicleInventoryClassAReportDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        vehicleInventoryClassAReportService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<VehicleInventoryClassAReportVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, vehicleInventoryClassAReportService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "生成报表数据")
    @GetMapping(value = "generateReport")
    @SuppressWarnings("unchecked")
    public void generateReport() {
       vehicleInventoryClassAReportService.doGenerateReport();
    }

}
