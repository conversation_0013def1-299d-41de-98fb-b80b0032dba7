<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassAReportDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassAReportPO">
        <!--@Table mrp_vehicle_inventory_class_a_report-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="customer" jdbcType="VARCHAR" property="customer"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
        <result column="material_name" jdbcType="VARCHAR" property="materialName"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="purchase_lot" jdbcType="VARCHAR" property="purchaseLot"/>
        <result column="future_thirty_day_demand" jdbcType="VARCHAR" property="futureThirtyDayDemand"/>
        <result column="fuyao_inventory_quantity" jdbcType="VARCHAR" property="fuyaoInventoryQuantity"/>
        <result column="supplier_inventory_quantity" jdbcType="VARCHAR" property="supplierInventoryQuantity"/>
        <result column="unprocessed_order" jdbcType="VARCHAR" property="unprocessedOrder"/>
        <result column="produced_glass_quantity" jdbcType="VARCHAR" property="producedGlassQuantity"/>
        <result column="fuyao_inventory_available_day" jdbcType="VARCHAR" property="fuyaoInventoryAvailableDay"/>
        <result column="supplier_inventory_available_day" jdbcType="VARCHAR" property="supplierInventoryAvailableDay"/>
        <result column="cost_amount_total" jdbcType="VARCHAR" property="costAmountTotal"/>
        <result column="fuyao_in_stock_amount" jdbcType="VARCHAR" property="fuyaoInStockAmount"/>
        <result column="supplier_in_stock_amount" jdbcType="VARCHAR" property="supplierInStockAmount"/>
        <result column="day_demand_amount" jdbcType="VARCHAR" property="dayDemandAmount"/>
        <result column="material_total_day" jdbcType="VARCHAR" property="materialTotalDay"/>
        <result column="control_standards" jdbcType="VARCHAR" property="controlStandards"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassAReportVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,customer,vehicle_model_code,material_code,material_name,material_type,supplier_code,supplier_name,purchase_lot,future_thirty_day_demand,fuyao_inventory_quantity,supplier_inventory_quantity,unprocessed_order,produced_glass_quantity,fuyao_inventory_available_day,supplier_inventory_available_day,cost_amount_total,fuyao_in_stock_amount,supplier_in_stock_amount,day_demand_amount,material_total_day,control_standards,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.customer != null and params.customer != ''">
                and customer = #{params.customer,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.materialCode != null and params.materialCode != ''">
                and material_code = #{params.materialCode,jdbcType=VARCHAR}
            </if>
            <if test="params.materialName != null and params.materialName != ''">
                and material_name = #{params.materialName,jdbcType=VARCHAR}
            </if>
            <if test="params.materialType != null and params.materialType != ''">
                and material_type = #{params.materialType,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierCode != null and params.supplierCode != ''">
                and supplier_code = #{params.supplierCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierName != null and params.supplierName != ''">
                and supplier_name = #{params.supplierName,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseLot != null">
                and purchase_lot = #{params.purchaseLot,jdbcType=VARCHAR}
            </if>
            <if test="params.futureThirtyDayDemand != null">
                and future_thirty_day_demand = #{params.futureThirtyDayDemand,jdbcType=VARCHAR}
            </if>
            <if test="params.fuyaoInventoryQuantity != null">
                and fuyao_inventory_quantity = #{params.fuyaoInventoryQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierInventoryQuantity != null">
                and supplier_inventory_quantity = #{params.supplierInventoryQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.unprocessedOrder != null">
                and unprocessed_order = #{params.unprocessedOrder,jdbcType=VARCHAR}
            </if>
            <if test="params.producedGlassQuantity != null">
                and produced_glass_quantity = #{params.producedGlassQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.fuyaoInventoryAvailableDay != null">
                and fuyao_inventory_available_day = #{params.fuyaoInventoryAvailableDay,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierInventoryAvailableDay != null">
                and supplier_inventory_available_day = #{params.supplierInventoryAvailableDay,jdbcType=VARCHAR}
            </if>
            <if test="params.costAmountTotal != null">
                and cost_amount_total = #{params.costAmountTotal,jdbcType=VARCHAR}
            </if>
            <if test="params.fuyaoInStockAmount != null">
                and fuyao_in_stock_amount = #{params.fuyaoInStockAmount,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierInStockAmount != null">
                and supplier_in_stock_amount = #{params.supplierInStockAmount,jdbcType=VARCHAR}
            </if>
            <if test="params.dayDemandAmount != null">
                and day_demand_amount = #{params.dayDemandAmount,jdbcType=VARCHAR}
            </if>
            <if test="params.materialTotalDay != null">
                and material_total_day = #{params.materialTotalDay,jdbcType=VARCHAR}
            </if>
            <if test="params.controlStandards != null">
                and control_standards = #{params.controlStandards,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_vehicle_inventory_class_a_report
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_vehicle_inventory_class_a_report
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_vehicle_inventory_class_a_report
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_vehicle_inventory_class_a_report
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassAReportPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_vehicle_inventory_class_a_report(
        id,
        customer,
        vehicle_model_code,
        material_code,
        material_name,
        material_type,
        supplier_code,
        supplier_name,
        purchase_lot,
        future_thirty_day_demand,
        fuyao_inventory_quantity,
        supplier_inventory_quantity,
        unprocessed_order,
        produced_glass_quantity,
        fuyao_inventory_available_day,
        supplier_inventory_available_day,
        cost_amount_total,
        fuyao_in_stock_amount,
        supplier_in_stock_amount,
        day_demand_amount,
        material_total_day,
        control_standards,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{customer,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{materialCode,jdbcType=VARCHAR},
        #{materialName,jdbcType=VARCHAR},
        #{materialType,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{supplierName,jdbcType=VARCHAR},
        #{purchaseLot,jdbcType=VARCHAR},
        #{futureThirtyDayDemand,jdbcType=VARCHAR},
        #{fuyaoInventoryQuantity,jdbcType=VARCHAR},
        #{supplierInventoryQuantity,jdbcType=VARCHAR},
        #{unprocessedOrder,jdbcType=VARCHAR},
        #{producedGlassQuantity,jdbcType=VARCHAR},
        #{fuyaoInventoryAvailableDay,jdbcType=VARCHAR},
        #{supplierInventoryAvailableDay,jdbcType=VARCHAR},
        #{costAmountTotal,jdbcType=VARCHAR},
        #{fuyaoInStockAmount,jdbcType=VARCHAR},
        #{supplierInStockAmount,jdbcType=VARCHAR},
        #{dayDemandAmount,jdbcType=VARCHAR},
        #{materialTotalDay,jdbcType=VARCHAR},
        #{controlStandards,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassAReportPO">
        insert into mrp_vehicle_inventory_class_a_report(id,
                                                         customer,
                                                         vehicle_model_code,
                                                         material_code,
                                                         material_name,
                                                         material_type,
                                                         supplier_code,
                                                         supplier_name,
                                                         purchase_lot,
                                                         future_thirty_day_demand,
                                                         fuyao_inventory_quantity,
                                                         supplier_inventory_quantity,
                                                         unprocessed_order,
                                                         produced_glass_quantity,
                                                         fuyao_inventory_available_day,
                                                         supplier_inventory_available_day,
                                                         cost_amount_total,
                                                         fuyao_in_stock_amount,
                                                         supplier_in_stock_amount,
                                                         day_demand_amount,
                                                         material_total_day,
                                                         control_standards,
                                                         remark,
                                                         enabled,
                                                         creator,
                                                         create_time,
                                                         modifier,
                                                         modify_time,
                                                         version_value)
        values (#{id,jdbcType=VARCHAR},
                #{customer,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{materialCode,jdbcType=VARCHAR},
                #{materialName,jdbcType=VARCHAR},
                #{materialType,jdbcType=VARCHAR},
                #{supplierCode,jdbcType=VARCHAR},
                #{supplierName,jdbcType=VARCHAR},
                #{purchaseLot,jdbcType=VARCHAR},
                #{futureThirtyDayDemand,jdbcType=VARCHAR},
                #{fuyaoInventoryQuantity,jdbcType=VARCHAR},
                #{supplierInventoryQuantity,jdbcType=VARCHAR},
                #{unprocessedOrder,jdbcType=VARCHAR},
                #{producedGlassQuantity,jdbcType=VARCHAR},
                #{fuyaoInventoryAvailableDay,jdbcType=VARCHAR},
                #{supplierInventoryAvailableDay,jdbcType=VARCHAR},
                #{costAmountTotal,jdbcType=VARCHAR},
                #{fuyaoInStockAmount,jdbcType=VARCHAR},
                #{supplierInStockAmount,jdbcType=VARCHAR},
                #{dayDemandAmount,jdbcType=VARCHAR},
                #{materialTotalDay,jdbcType=VARCHAR},
                #{controlStandards,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_vehicle_inventory_class_a_report(
        id,
        customer,
        vehicle_model_code,
        material_code,
        material_name,
        material_type,
        supplier_code,
        supplier_name,
        purchase_lot,
        future_thirty_day_demand,
        fuyao_inventory_quantity,
        supplier_inventory_quantity,
        unprocessed_order,
        produced_glass_quantity,
        fuyao_inventory_available_day,
        supplier_inventory_available_day,
        cost_amount_total,
        fuyao_in_stock_amount,
        supplier_in_stock_amount,
        day_demand_amount,
        material_total_day,
        control_standards,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.customer,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.materialCode,jdbcType=VARCHAR},
            #{entity.materialName,jdbcType=VARCHAR},
            #{entity.materialType,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.purchaseLot,jdbcType=VARCHAR},
            #{entity.futureThirtyDayDemand,jdbcType=VARCHAR},
            #{entity.fuyaoInventoryQuantity,jdbcType=VARCHAR},
            #{entity.supplierInventoryQuantity,jdbcType=VARCHAR},
            #{entity.unprocessedOrder,jdbcType=VARCHAR},
            #{entity.producedGlassQuantity,jdbcType=VARCHAR},
            #{entity.fuyaoInventoryAvailableDay,jdbcType=VARCHAR},
            #{entity.supplierInventoryAvailableDay,jdbcType=VARCHAR},
            #{entity.costAmountTotal,jdbcType=VARCHAR},
            #{entity.fuyaoInStockAmount,jdbcType=VARCHAR},
            #{entity.supplierInStockAmount,jdbcType=VARCHAR},
            #{entity.dayDemandAmount,jdbcType=VARCHAR},
            #{entity.materialTotalDay,jdbcType=VARCHAR},
            #{entity.controlStandards,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_vehicle_inventory_class_a_report(
        id,
        customer,
        vehicle_model_code,
        material_code,
        material_name,
        material_type,
        supplier_code,
        supplier_name,
        purchase_lot,
        future_thirty_day_demand,
        fuyao_inventory_quantity,
        supplier_inventory_quantity,
        unprocessed_order,
        produced_glass_quantity,
        fuyao_inventory_available_day,
        supplier_inventory_available_day,
        cost_amount_total,
        fuyao_in_stock_amount,
        supplier_in_stock_amount,
        day_demand_amount,
        material_total_day,
        control_standards,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.customer,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.materialCode,jdbcType=VARCHAR},
            #{entity.materialName,jdbcType=VARCHAR},
            #{entity.materialType,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.purchaseLot,jdbcType=VARCHAR},
            #{entity.futureThirtyDayDemand,jdbcType=VARCHAR},
            #{entity.fuyaoInventoryQuantity,jdbcType=VARCHAR},
            #{entity.supplierInventoryQuantity,jdbcType=VARCHAR},
            #{entity.unprocessedOrder,jdbcType=VARCHAR},
            #{entity.producedGlassQuantity,jdbcType=VARCHAR},
            #{entity.fuyaoInventoryAvailableDay,jdbcType=VARCHAR},
            #{entity.supplierInventoryAvailableDay,jdbcType=VARCHAR},
            #{entity.costAmountTotal,jdbcType=VARCHAR},
            #{entity.fuyaoInStockAmount,jdbcType=VARCHAR},
            #{entity.supplierInStockAmount,jdbcType=VARCHAR},
            #{entity.dayDemandAmount,jdbcType=VARCHAR},
            #{entity.materialTotalDay,jdbcType=VARCHAR},
            #{entity.controlStandards,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassAReportPO">
        update mrp_vehicle_inventory_class_a_report
        set customer                         = #{customer,jdbcType=VARCHAR},
            vehicle_model_code               = #{vehicleModelCode,jdbcType=VARCHAR},
            material_code                    = #{materialCode,jdbcType=VARCHAR},
            material_name                    = #{materialName,jdbcType=VARCHAR},
            material_type                    = #{materialType,jdbcType=VARCHAR},
            supplier_code                    = #{supplierCode,jdbcType=VARCHAR},
            supplier_name                    = #{supplierName,jdbcType=VARCHAR},
            purchase_lot                     = #{purchaseLot,jdbcType=VARCHAR},
            future_thirty_day_demand         = #{futureThirtyDayDemand,jdbcType=VARCHAR},
            fuyao_inventory_quantity         = #{fuyaoInventoryQuantity,jdbcType=VARCHAR},
            supplier_inventory_quantity      = #{supplierInventoryQuantity,jdbcType=VARCHAR},
            unprocessed_order                = #{unprocessedOrder,jdbcType=VARCHAR},
            produced_glass_quantity          = #{producedGlassQuantity,jdbcType=VARCHAR},
            fuyao_inventory_available_day    = #{fuyaoInventoryAvailableDay,jdbcType=VARCHAR},
            supplier_inventory_available_day = #{supplierInventoryAvailableDay,jdbcType=VARCHAR},
            cost_amount_total                = #{costAmountTotal,jdbcType=VARCHAR},
            fuyao_in_stock_amount            = #{fuyaoInStockAmount,jdbcType=VARCHAR},
            supplier_in_stock_amount         = #{supplierInStockAmount,jdbcType=VARCHAR},
            day_demand_amount                = #{dayDemandAmount,jdbcType=VARCHAR},
            material_total_day               = #{materialTotalDay,jdbcType=VARCHAR},
            control_standards                = #{controlStandards,jdbcType=VARCHAR},
            remark                           = #{remark,jdbcType=VARCHAR},
            enabled                          = #{enabled,jdbcType=VARCHAR},
            modifier                         = #{modifier,jdbcType=VARCHAR},
            modify_time                      = #{modifyTime,jdbcType=TIMESTAMP},
            version_value                    = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassAReportPO">
        update mrp_vehicle_inventory_class_a_report
        <set>
            <if test="item.customer != null and item.customer != ''">
                customer = #{item.customer,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialCode != null and item.materialCode != ''">
                material_code = #{item.materialCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialName != null and item.materialName != ''">
                material_name = #{item.materialName,jdbcType=VARCHAR},
            </if>
            <if test="item.materialType != null and item.materialType != ''">
                material_type = #{item.materialType,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null and item.supplierCode != ''">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierName != null and item.supplierName != ''">
                supplier_name = #{item.supplierName,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseLot != null">
                purchase_lot = #{item.purchaseLot,jdbcType=VARCHAR},
            </if>
            <if test="item.futureThirtyDayDemand != null">
                future_thirty_day_demand = #{item.futureThirtyDayDemand,jdbcType=VARCHAR},
            </if>
            <if test="item.fuyaoInventoryQuantity != null">
                fuyao_inventory_quantity = #{item.fuyaoInventoryQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierInventoryQuantity != null">
                supplier_inventory_quantity = #{item.supplierInventoryQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.unprocessedOrder != null">
                unprocessed_order = #{item.unprocessedOrder,jdbcType=VARCHAR},
            </if>
            <if test="item.producedGlassQuantity != null">
                produced_glass_quantity = #{item.producedGlassQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.fuyaoInventoryAvailableDay != null">
                fuyao_inventory_available_day = #{item.fuyaoInventoryAvailableDay,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierInventoryAvailableDay != null">
                supplier_inventory_available_day = #{item.supplierInventoryAvailableDay,jdbcType=VARCHAR},
            </if>
            <if test="item.costAmountTotal != null">
                cost_amount_total = #{item.costAmountTotal,jdbcType=VARCHAR},
            </if>
            <if test="item.fuyaoInStockAmount != null">
                fuyao_in_stock_amount = #{item.fuyaoInStockAmount,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierInStockAmount != null">
                supplier_in_stock_amount = #{item.supplierInStockAmount,jdbcType=VARCHAR},
            </if>
            <if test="item.dayDemandAmount != null">
                day_demand_amount = #{item.dayDemandAmount,jdbcType=VARCHAR},
            </if>
            <if test="item.materialTotalDay != null">
                material_total_day = #{item.materialTotalDay,jdbcType=VARCHAR},
            </if>
            <if test="item.controlStandards != null">
                control_standards = #{item.controlStandards,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_vehicle_inventory_class_a_report
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="customer = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customer,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_lot = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseLot,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="future_thirty_day_demand = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.futureThirtyDayDemand,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fuyao_inventory_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fuyaoInventoryQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_inventory_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierInventoryQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unprocessed_order = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unprocessedOrder,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="produced_glass_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.producedGlassQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fuyao_inventory_available_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fuyaoInventoryAvailableDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_inventory_available_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierInventoryAvailableDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cost_amount_total = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.costAmountTotal,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fuyao_in_stock_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fuyaoInStockAmount,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_in_stock_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierInStockAmount,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="day_demand_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dayDemandAmount,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_total_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialTotalDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="control_standards = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.controlStandards,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_vehicle_inventory_class_a_report
            <set>
                <if test="item.customer != null and item.customer != ''">
                    customer = #{item.customer,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialCode != null and item.materialCode != ''">
                    material_code = #{item.materialCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialName != null and item.materialName != ''">
                    material_name = #{item.materialName,jdbcType=VARCHAR},
                </if>
                <if test="item.materialType != null and item.materialType != ''">
                    material_type = #{item.materialType,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierCode != null and item.supplierCode != ''">
                    supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierName != null and item.supplierName != ''">
                    supplier_name = #{item.supplierName,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseLot != null">
                    purchase_lot = #{item.purchaseLot,jdbcType=VARCHAR},
                </if>
                <if test="item.futureThirtyDayDemand != null">
                    future_thirty_day_demand = #{item.futureThirtyDayDemand,jdbcType=VARCHAR},
                </if>
                <if test="item.fuyaoInventoryQuantity != null">
                    fuyao_inventory_quantity = #{item.fuyaoInventoryQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierInventoryQuantity != null">
                    supplier_inventory_quantity = #{item.supplierInventoryQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.unprocessedOrder != null">
                    unprocessed_order = #{item.unprocessedOrder,jdbcType=VARCHAR},
                </if>
                <if test="item.producedGlassQuantity != null">
                    produced_glass_quantity = #{item.producedGlassQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.fuyaoInventoryAvailableDay != null">
                    fuyao_inventory_available_day = #{item.fuyaoInventoryAvailableDay,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierInventoryAvailableDay != null">
                    supplier_inventory_available_day = #{item.supplierInventoryAvailableDay,jdbcType=VARCHAR},
                </if>
                <if test="item.costAmountTotal != null">
                    cost_amount_total = #{item.costAmountTotal,jdbcType=VARCHAR},
                </if>
                <if test="item.fuyaoInStockAmount != null">
                    fuyao_in_stock_amount = #{item.fuyaoInStockAmount,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierInStockAmount != null">
                    supplier_in_stock_amount = #{item.supplierInStockAmount,jdbcType=VARCHAR},
                </if>
                <if test="item.dayDemandAmount != null">
                    day_demand_amount = #{item.dayDemandAmount,jdbcType=VARCHAR},
                </if>
                <if test="item.materialTotalDay != null">
                    material_total_day = #{item.materialTotalDay,jdbcType=VARCHAR},
                </if>
                <if test="item.controlStandards != null">
                    control_standards = #{item.controlStandards,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_vehicle_inventory_class_a_report
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_vehicle_inventory_class_a_report where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
