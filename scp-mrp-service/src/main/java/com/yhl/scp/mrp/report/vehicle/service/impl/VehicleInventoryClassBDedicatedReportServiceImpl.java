package com.yhl.scp.mrp.report.vehicle.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.report.vehicle.convertor.VehicleInventoryClassBDedicatedReportConvertor;
import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassBDedicatedReportDO;
import com.yhl.scp.mrp.report.vehicle.domain.service.VehicleInventoryClassBDedicatedReportDomainService;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBDedicatedReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassBDedicatedReportDao;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBDedicatedReportPO;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassBDedicatedReportService;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBDedicatedReportVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>VehicleInventoryClassBDedicatedReportServiceImpl</code>
 * <p>
 * 车型库存（B类专用）报表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:47:32
 */
@Slf4j
@Service
public class VehicleInventoryClassBDedicatedReportServiceImpl extends AbstractService implements VehicleInventoryClassBDedicatedReportService {

    @Resource
    private VehicleInventoryClassBDedicatedReportDao vehicleInventoryClassBDedicatedReportDao;

    @Resource
    private VehicleInventoryClassBDedicatedReportDomainService vehicleInventoryClassBDedicatedReportDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(VehicleInventoryClassBDedicatedReportDTO vehicleInventoryClassBDedicatedReportDTO) {
        // 0.数据转换
        VehicleInventoryClassBDedicatedReportDO vehicleInventoryClassBDedicatedReportDO = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassBDedicatedReportDTO);
        VehicleInventoryClassBDedicatedReportPO vehicleInventoryClassBDedicatedReportPO = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassBDedicatedReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassBDedicatedReportDomainService.validation(vehicleInventoryClassBDedicatedReportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(vehicleInventoryClassBDedicatedReportPO);
        vehicleInventoryClassBDedicatedReportDao.insert(vehicleInventoryClassBDedicatedReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(VehicleInventoryClassBDedicatedReportDTO vehicleInventoryClassBDedicatedReportDTO) {
        // 0.数据转换
        VehicleInventoryClassBDedicatedReportDO vehicleInventoryClassBDedicatedReportDO = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassBDedicatedReportDTO);
        VehicleInventoryClassBDedicatedReportPO vehicleInventoryClassBDedicatedReportPO = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassBDedicatedReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassBDedicatedReportDomainService.validation(vehicleInventoryClassBDedicatedReportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(vehicleInventoryClassBDedicatedReportPO);
        vehicleInventoryClassBDedicatedReportDao.update(vehicleInventoryClassBDedicatedReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<VehicleInventoryClassBDedicatedReportDTO> list) {
        List<VehicleInventoryClassBDedicatedReportPO> newList = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        vehicleInventoryClassBDedicatedReportDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<VehicleInventoryClassBDedicatedReportDTO> list) {
        List<VehicleInventoryClassBDedicatedReportPO> newList = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        vehicleInventoryClassBDedicatedReportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return vehicleInventoryClassBDedicatedReportDao.deleteBatch(idList);
        }
        return vehicleInventoryClassBDedicatedReportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public VehicleInventoryClassBDedicatedReportVO selectByPrimaryKey(String id) {
        VehicleInventoryClassBDedicatedReportPO po = vehicleInventoryClassBDedicatedReportDao.selectByPrimaryKey(id);
        return VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_BDEDICATED_REPORT")
    public List<VehicleInventoryClassBDedicatedReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_BDEDICATED_REPORT")
    public List<VehicleInventoryClassBDedicatedReportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<VehicleInventoryClassBDedicatedReportVO> dataList = vehicleInventoryClassBDedicatedReportDao.selectByCondition(sortParam, queryCriteriaParam);
        VehicleInventoryClassBDedicatedReportServiceImpl target = SpringBeanUtils.getBean(VehicleInventoryClassBDedicatedReportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<VehicleInventoryClassBDedicatedReportVO> selectByParams(Map<String, Object> params) {
        List<VehicleInventoryClassBDedicatedReportPO> list = vehicleInventoryClassBDedicatedReportDao.selectByParams(params);
        return VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<VehicleInventoryClassBDedicatedReportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> generateReport() {
        return null;
    }

    @Override
    public String getObjectType() {
        // return ObjectTypeEnum.VEHICLE_INVENTORY_CLASS_BDEDICATED_REPORT.getCode();
        return null;
    }

    @Override
    public List<VehicleInventoryClassBDedicatedReportVO> invocation(List<VehicleInventoryClassBDedicatedReportVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
