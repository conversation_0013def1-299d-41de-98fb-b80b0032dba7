package com.yhl.scp.mrp.report.vehicle.domain.factory;

import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassBDedicatedReportDO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBDedicatedReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassBDedicatedReportDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>VehicleInventoryClassBDedicatedReportFactory</code>
 * <p>
 * 车型库存（B类专用）报表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:47:36
 */
@Component
public class VehicleInventoryClassBDedicatedReportFactory {

    @Resource
    private VehicleInventoryClassBDedicatedReportDao vehicleInventoryClassBDedicatedReportDao;

    VehicleInventoryClassBDedicatedReportDO create(VehicleInventoryClassBDedicatedReportDTO dto) {
        // TODO
        return null;
    }

}
