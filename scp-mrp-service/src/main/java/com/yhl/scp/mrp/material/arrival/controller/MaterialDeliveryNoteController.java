package com.yhl.scp.mrp.material.arrival.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.material.arrival.dto.MaterialDeliveryNoteDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialDeliveryNoteService;
import com.yhl.scp.mrp.material.arrival.vo.MaterialDeliveryNoteVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialDeliveryNoteController</code>
 * <p>
 * 材料送货单数据控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 16:09:03
 */
@Slf4j
@Api(tags = "材料送货单数据控制器")
@RestController
@RequestMapping("materialDeliveryNote")
public class MaterialDeliveryNoteController extends BaseController {

    @Resource
    private MaterialDeliveryNoteService materialDeliveryNoteService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MaterialDeliveryNoteVO>> page() {
        List<MaterialDeliveryNoteVO> materialDeliveryNoteList = materialDeliveryNoteService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialDeliveryNoteVO> pageInfo = new PageInfo<>(materialDeliveryNoteList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialDeliveryNoteDTO materialDeliveryNoteDTO) {
        return materialDeliveryNoteService.doCreate(materialDeliveryNoteDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialDeliveryNoteDTO materialDeliveryNoteDTO) {
        return materialDeliveryNoteService.doUpdate(materialDeliveryNoteDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialDeliveryNoteService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MaterialDeliveryNoteVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialDeliveryNoteService.selectByPrimaryKey(id));
    }


    @ApiOperation(value = "同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncMaterialDeliveryNote() {
        return materialDeliveryNoteService.syncMaterialDeliveryNote(SystemHolder.getTenantCode(),SystemHolder.getScenario());
    }
}
