package com.yhl.scp.mrp.report.vehicle.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassAReportPO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassAReportVO;

/**
 * <code>VehicleInventoryClassAReportDao</code>
 * <p>
 * 车型库存管控-A类车型维度报表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-25 17:18:35
 */
public interface VehicleInventoryClassAReportDao extends BaseDao<VehicleInventoryClassAReportPO, VehicleInventoryClassAReportVO> {

}
