package com.yhl.scp.mrp.report.vehicle.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>VehicleInventoryClassAReportDO</code>
 * <p>
 * 车型库存管控-A类车型维度报表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-25 17:18:41
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleInventoryClassAReportDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 421904505629552380L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 客户
     */
    private String customer;
    /**
     * 车型
     */
    private String vehicleModelCode;
    /**
     * 材料代码
     */
    private String materialCode;
    /**
     * 材料名称
     */
    private String materialName;
    /**
     * 材料类型
     */
    private String materialType;
    /**
     * 供应商代码
     */
    private String supplierCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 采购周期
     */
    private BigDecimal purchaseLot;
    /**
     * 未来30天日均需求
     */
    private BigDecimal futureThirtyDayDemand;
    /**
     * 福耀库存
     */
    private BigDecimal fuyaoInventoryQuantity;
    /**
     * 供应商库存
     */
    private BigDecimal supplierInventoryQuantity;
    /**
     * 未生产订单
     */
    private BigDecimal unprocessedOrder;
    /**
     * 现有库存可生产玻璃零件数量
     */
    private BigDecimal producedGlassQuantity;
    /**
     * 福耀库存可用天数
     */
    private BigDecimal fuyaoInventoryAvailableDay;
    /**
     * 供应商库存可用天数
     */
    private BigDecimal supplierInventoryAvailableDay;
    /**
     * 成本总金额
     */
    private BigDecimal costAmountTotal;
    /**
     * 福耀在库金额(万元)
     */
    private BigDecimal fuyaoInStockAmount;
    /**
     * 供应商库存金额(万元)
     */
    private BigDecimal supplierInStockAmount;
    /**
     * 日均需求金额
     */
    private BigDecimal dayDemandAmount;
    /**
     * 材料总天数
     */
    private BigDecimal materialTotalDay;
    /**
     * 管控标准
     */
    private BigDecimal controlStandards;
    /**
     * 版本
     */
    private Integer versionValue;

}
