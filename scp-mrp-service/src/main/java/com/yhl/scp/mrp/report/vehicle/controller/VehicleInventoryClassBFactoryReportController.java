package com.yhl.scp.mrp.report.vehicle.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBFactoryReportDTO;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassBFactoryReportService;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassAReportVO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBFactoryReportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>VehicleInventoryClassBFactoryReportController</code>
 * <p>
 * 车型库存（B类本厂）报表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:48:33
 */
@Slf4j
@Api(tags = "车型库存（B类本厂）报表控制器")
@RestController
@RequestMapping("vehicleInventoryClassBFactoryReport")
public class VehicleInventoryClassBFactoryReportController extends BaseController {

    @Resource
    private VehicleInventoryClassBFactoryReportService vehicleInventoryClassBFactoryReportService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<VehicleInventoryClassBFactoryReportVO>> page() {
        List<VehicleInventoryClassBFactoryReportVO> vehicleInventoryClassBFactoryReportList = vehicleInventoryClassBFactoryReportService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<VehicleInventoryClassBFactoryReportVO> pageInfo = new PageInfo<>(vehicleInventoryClassBFactoryReportList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody VehicleInventoryClassBFactoryReportDTO vehicleInventoryClassBFactoryReportDTO) {
        return vehicleInventoryClassBFactoryReportService.doCreate(vehicleInventoryClassBFactoryReportDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody VehicleInventoryClassBFactoryReportDTO vehicleInventoryClassBFactoryReportDTO) {
        return vehicleInventoryClassBFactoryReportService.doUpdate(vehicleInventoryClassBFactoryReportDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        vehicleInventoryClassBFactoryReportService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<VehicleInventoryClassBFactoryReportVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, vehicleInventoryClassBFactoryReportService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "生成报表数据")
    @GetMapping(value = "generateReport")
    public BaseResponse<Void> generateReport() {
        String redisKey = String.join("#", RedisKeyManageEnum.VEHICLE_INVENTORY_CLASS_B_FACTORY_REPORT_GENERATE.getKey());
        try {
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))) {
                return BaseResponse.error("当前已有报表正在生成，请等待生成完成");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId(), 60 * 5);
            // 生成报表
            return vehicleInventoryClassBFactoryReportService.generateReport();
        } catch (Exception e) {
            log.error("生成车型库存（B类本厂）报表失败", e);
            throw new BusinessException("生成车型库存（B类本厂）报表失败,{0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
        }
    }

}
