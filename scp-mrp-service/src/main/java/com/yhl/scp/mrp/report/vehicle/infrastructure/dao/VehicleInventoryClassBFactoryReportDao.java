package com.yhl.scp.mrp.report.vehicle.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBFactoryReportPO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBFactoryReportVO;

/**
 * <code>VehicleInventoryClassBFactoryReportDao</code>
 * <p>
 * 车型库存（B类本厂）报表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:48:33
 */
public interface VehicleInventoryClassBFactoryReportDao extends BaseDao<VehicleInventoryClassBFactoryReportPO, VehicleInventoryClassBFactoryReportVO> {

}
