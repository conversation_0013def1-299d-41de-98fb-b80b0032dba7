package com.yhl.scp.mrp.report.vehicle.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassBDedicatedReportDO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassBDedicatedReportDao;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBDedicatedReportPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>VehicleInventoryClassBDedicatedReportDomainService</code>
 * <p>
 * 车型库存（B类专用）报表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:47:36
 */
@Service
public class VehicleInventoryClassBDedicatedReportDomainService {

    @Resource
    private VehicleInventoryClassBDedicatedReportDao vehicleInventoryClassBDedicatedReportDao;

    /**
     * 数据校验
     *
     * @param vehicleInventoryClassBDedicatedReportDO 领域对象
     */
    public void validation(VehicleInventoryClassBDedicatedReportDO vehicleInventoryClassBDedicatedReportDO) {
        checkNotNull(vehicleInventoryClassBDedicatedReportDO);
        checkUniqueCode(vehicleInventoryClassBDedicatedReportDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param vehicleInventoryClassBDedicatedReportDO 领域对象
     */
    private void checkNotNull(VehicleInventoryClassBDedicatedReportDO vehicleInventoryClassBDedicatedReportDO) {}

    /**
     * 唯一性校验
     *
     * @param vehicleInventoryClassBDedicatedReportDO 领域对象
     */
    private void checkUniqueCode(VehicleInventoryClassBDedicatedReportDO vehicleInventoryClassBDedicatedReportDO) {}

}
