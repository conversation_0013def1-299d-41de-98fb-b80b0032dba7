package com.yhl.scp.mrp.report.vehicle.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryReportVO;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mrp.report.vehicle.convertor.VehicleInventoryClassAReportConvertor;
import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassAReportDO;
import com.yhl.scp.mrp.report.vehicle.domain.service.VehicleInventoryClassAReportDomainService;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassAReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassAReportDao;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassAReportPO;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassAReportService;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassAReportVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>VehicleInventoryClassAReportServiceImpl</code>
 * <p>
 * 车型库存管控-A类车型维度报表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-25 17:18:36
 */
@Slf4j
@Service
public class VehicleInventoryClassAReportServiceImpl extends AbstractService implements VehicleInventoryClassAReportService {

    @Resource
    private VehicleInventoryClassAReportDao vehicleInventoryClassAReportDao;

    @Resource
    private VehicleInventoryClassAReportDomainService vehicleInventoryClassAReportDomainService;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(VehicleInventoryClassAReportDTO vehicleInventoryClassAReportDTO) {
        // 0.数据转换
        VehicleInventoryClassAReportDO vehicleInventoryClassAReportDO = VehicleInventoryClassAReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassAReportDTO);
        VehicleInventoryClassAReportPO vehicleInventoryClassAReportPO = VehicleInventoryClassAReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassAReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassAReportDomainService.validation(vehicleInventoryClassAReportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(vehicleInventoryClassAReportPO);
        vehicleInventoryClassAReportDao.insert(vehicleInventoryClassAReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(VehicleInventoryClassAReportDTO vehicleInventoryClassAReportDTO) {
        // 0.数据转换
        VehicleInventoryClassAReportDO vehicleInventoryClassAReportDO = VehicleInventoryClassAReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassAReportDTO);
        VehicleInventoryClassAReportPO vehicleInventoryClassAReportPO = VehicleInventoryClassAReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassAReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassAReportDomainService.validation(vehicleInventoryClassAReportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(vehicleInventoryClassAReportPO);
        vehicleInventoryClassAReportDao.update(vehicleInventoryClassAReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<VehicleInventoryClassAReportDTO> list) {
        List<VehicleInventoryClassAReportPO> newList = VehicleInventoryClassAReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        vehicleInventoryClassAReportDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<VehicleInventoryClassAReportDTO> list) {
        List<VehicleInventoryClassAReportPO> newList = VehicleInventoryClassAReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        vehicleInventoryClassAReportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return vehicleInventoryClassAReportDao.deleteBatch(idList);
        }
        return vehicleInventoryClassAReportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public VehicleInventoryClassAReportVO selectByPrimaryKey(String id) {
        VehicleInventoryClassAReportPO po = vehicleInventoryClassAReportDao.selectByPrimaryKey(id);
        return VehicleInventoryClassAReportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_AREPORT")
    public List<VehicleInventoryClassAReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_AREPORT")
    public List<VehicleInventoryClassAReportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<VehicleInventoryClassAReportVO> dataList = vehicleInventoryClassAReportDao.selectByCondition(sortParam, queryCriteriaParam);
        VehicleInventoryClassAReportServiceImpl target = SpringBeanUtils.getBean(VehicleInventoryClassAReportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<VehicleInventoryClassAReportVO> selectByParams(Map<String, Object> params) {
        List<VehicleInventoryClassAReportPO> list = vehicleInventoryClassAReportDao.selectByParams(params);
        return VehicleInventoryClassAReportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<VehicleInventoryClassAReportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<VehicleInventoryClassAReportVO> invocation(List<VehicleInventoryClassAReportVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void doGenerateReport() {
        String scenario = SystemHolder.getScenario();
        // 查询需要计算的本厂编码
        DemandVersionVO demandVersionVO = dfpFeign.selectLastVersionByVersionTypeAndPlanPeriod(scenario, VersionTypeEnum.CLEAN_DEMAND.getCode(), null);
        String demandVersionId = demandVersionVO.getId();
        List<CleanDemandProductInventoryReportVO> dataList = dfpFeign.selectProductInventoryReport(scenario, demandVersionId);
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        List<String> productCodeList = dataList.stream().map(CleanDemandProductInventoryReportVO::getProductCode)
                .distinct().collect(Collectors.toList());

        // 查询所有的BOM
        List<ProductBomVersionVO> productBomVersionVOList = newMdsFeign.selectProductBomVersionVOByParams(scenario, new HashMap<>());
        List<ProductBomVO> productBomVOList = newMdsFeign.selectProductBomVOByParams(scenario, new HashMap<>());

        Map<String, ProductBomVersionVO> productBomVersionVOMapOfProductCode = productBomVersionVOList.stream()
                .collect(Collectors.toMap(ProductBomVersionVO::getProductCode, Function.identity(), (k1, k2) -> k2));
        Map<String, List<ProductBomVO>> productBomGroupOfBomVersionId = productBomVOList.stream()
                .collect(Collectors.groupingBy(ProductBomVO::getBomVersionId));

        // 解析成品所有的辅料信息
        Map<String, List<ProductBomVO>> productBomGroupOfProductCode = new HashMap<>();
        this.getProductChildBomAll(productCodeList, productBomVersionVOMapOfProductCode, productBomGroupOfBomVersionId, productBomGroupOfProductCode);

    }

    @Override
    public void getProductChildBomAll(List<String> productCodeList,
                                       Map<String, ProductBomVersionVO> productBomVersionVOMapOfProductCode,
                                       Map<String, List<ProductBomVO>> productBomGroupOfBomVersionId,
                                       Map<String, List<ProductBomVO>> productBomGroupOfProductCode) {
        for (String productCode : productCodeList) {
            getChildBomAll(productCode, productCodeList, productBomVersionVOMapOfProductCode, productBomGroupOfBomVersionId, productBomGroupOfProductCode);
        }
    }


    private void getChildBomAll(String productCode,
                                List<String> productCodeList,
                                Map<String, ProductBomVersionVO> productBomVersionVOMapOfProductCode,
                                Map<String, List<ProductBomVO>> productBomGroupOfBomVersionId,
                                Map<String, List<ProductBomVO>> productBomGroupOfProductCode) {
        for (String ioProductCode : productCodeList) {
            // 获取对应的bomVersion
            ProductBomVersionVO productBomVersionVO = productBomVersionVOMapOfProductCode.get(ioProductCode);
            if (null == productBomVersionVO) {
                continue;
            }
            // 获取对应的bom
            List<ProductBomVO> productBomVOS = productBomGroupOfBomVersionId.get(productBomVersionVO.getId());
            if (CollectionUtils.isEmpty(productBomVOS)) {
                continue;
            }

            // 过滤出类型为采购的
            List<ProductBomVO> materialProductBomVOS = productBomVOS.stream()
                    .filter(item -> StringUtils.equals(ProductTypeEnum.P.getCode(), item.getProductType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(materialProductBomVOS)) {
                if (productBomGroupOfProductCode.containsKey(productCode)) {
                    List<ProductBomVO> productBomVOList = productBomGroupOfProductCode.get(productCode);
                    productBomVOList.addAll(materialProductBomVOS);
                    productBomGroupOfProductCode.put(productCode, productBomVOList);
                } else {
                    productBomGroupOfProductCode.put(productCode, materialProductBomVOS);
                }
            }

            // 过滤出类型非采购的
            List<ProductBomVO> productBomVOList = productBomVOS.stream()
                    .filter(item -> !StringUtils.equals(ProductTypeEnum.P.getCode(), item.getProductType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productBomVOList)) {
                continue;
            }

            // 递归获取
            List<String> ioProductCodeList = productBomVOList.stream().map(ProductBomVO::getIoProductCode).collect(Collectors.toList());
            getChildBomAll(productCode, ioProductCodeList, productBomVersionVOMapOfProductCode, productBomGroupOfBomVersionId, productBomGroupOfProductCode);
        }
    }

}