package com.yhl.scp.mrp.report.vehicle.convertor;

import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassAReportDO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassAReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassAReportPO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassAReportVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>VehicleInventoryClassAReportConvertor</code>
 * <p>
 * 车型库存管控-A类车型维度报表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-25 17:18:41
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleInventoryClassAReportConvertor {

    VehicleInventoryClassAReportConvertor INSTANCE = Mappers.getMapper(VehicleInventoryClassAReportConvertor.class);

    VehicleInventoryClassAReportDO dto2Do(VehicleInventoryClassAReportDTO obj);

    VehicleInventoryClassAReportDTO do2Dto(VehicleInventoryClassAReportDO obj);

    List<VehicleInventoryClassAReportDO> dto2Dos(List<VehicleInventoryClassAReportDTO> list);

    List<VehicleInventoryClassAReportDTO> do2Dtos(List<VehicleInventoryClassAReportDO> list);

    VehicleInventoryClassAReportVO do2Vo(VehicleInventoryClassAReportDO obj);

    VehicleInventoryClassAReportVO po2Vo(VehicleInventoryClassAReportPO obj);

    List<VehicleInventoryClassAReportVO> po2Vos(List<VehicleInventoryClassAReportPO> list);

    VehicleInventoryClassAReportPO do2Po(VehicleInventoryClassAReportDO obj);

    VehicleInventoryClassAReportDO po2Do(VehicleInventoryClassAReportPO obj);

    VehicleInventoryClassAReportPO dto2Po(VehicleInventoryClassAReportDTO obj);

    List<VehicleInventoryClassAReportPO> dto2Pos(List<VehicleInventoryClassAReportDTO> obj);

}
