<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="30 seconds">
    <!-- 引入Spring Boot默认日志配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <property name="APP_NAME" value="app"/>
    <!-- 可被子项目覆盖的基础配置 -->
    <springProperty scope="context" name="LOG_HOME" source="logging.file.path" defaultValue="./logs"/>
    <springProperty scope="context" name="MAX_HISTORY" source="logging.max-history" defaultValue="30"/>
    <springProperty scope="context" name="MAX_FILE_SIZE" source="logging.max-file-size" defaultValue="100MB"/>
    <springProperty scope="context" name="TOTAL_SIZE_CAP" source="logging.total-size-cap" defaultValue="20GB"/>

    <!-- 日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr([%t: ${PID:- }]){faint} %L - %clr(%logger){cyan}%clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <property name="FILE_LOG_PATTERN"
              value="%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} [%t: ${PID:- }] %L - %logger: %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <charset>UTF-8</charset>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 文件输出（滚动策略 + 压缩，适配低版本Logback） -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_NAME}.log</file>
        <immediateFlush>true</immediateFlush>
        <prudent>false</prudent>

        <!-- 触发滚动的条件（按文件大小） -->
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
        </triggeringPolicy>

        <!-- 滚动策略（按时间+大小，支持压缩） -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 归档文件名格式（带.gz后缀表示压缩） -->
            <fileNamePattern>${LOG_HOME}/${APP_NAME}-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 保留历史日志天数 -->
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <!-- 总大小限制 -->
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
            <!-- 配合SizeBasedTriggeringPolicy使用，指定同一时间段内的文件索引 -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>

        <encoder>
            <charset>UTF-8</charset>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 异步日志 -->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <neverBlock>false</neverBlock>
        <includeCallerData>true</includeCallerData>
        <maxFlushTime>1000</maxFlushTime>
        <appender-ref ref="FILE"/>
    </appender>

    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_FILE"/>
    </root>

    <!-- 特定包日志配置 -->
    <logger name="org.springframework" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_FILE"/>
    </logger>
    <logger name="com.fasterxml.jackson" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_FILE"/>
    </logger>
</configuration>