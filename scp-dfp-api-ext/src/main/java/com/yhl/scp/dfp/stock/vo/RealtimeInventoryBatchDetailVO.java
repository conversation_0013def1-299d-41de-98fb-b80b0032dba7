package com.yhl.scp.dfp.stock.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <code>RealtimeInventoryBatchDetailVO</code>
 * <p>
 * 实时库存批次明细VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:56:10
 */
@ApiModel(value = "实时库存批次明细VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RealtimeInventoryBatchDetailVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -96046051267493131L;

    /**
     * 现有量
     */
    @ApiModelProperty(value = "现有量")
    @FieldInterpretation(value = "现有量")
    private String currentQuantity;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    @FieldInterpretation(value = "货位")
    private String freightSpace;
    /**
     * 货位描述
     */
    @ApiModelProperty(value = "货位描述")
    @FieldInterpretation(value = "货位描述")
    private String freightSpaceDescription;
    /**
     * 工序
     */
    @ApiModelProperty(value = "工序")
    @FieldInterpretation(value = "工序")
    private String operationCode;
    /**
     * 原始报文组织ID
     */
    @ApiModelProperty(value = "原始报文组织ID")
    @FieldInterpretation(value = "原始报文组织ID")
    private String originalOrgId;
    /**
     * 原始物料编码
     */
    @ApiModelProperty(value = "原始物料编码")
    @FieldInterpretation(value = "原始物料编码")
    private String originalProductCode;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @FieldInterpretation(value = "物料编码")
    private String productCode;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String stockPointCode;
    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    @FieldInterpretation(value = "子库存")
    private String subinventory;
    /**
     * 子库存描述
     */
    @ApiModelProperty(value = "子库存描述")
    @FieldInterpretation(value = "子库存描述")
    private String subinventoryDescription;
    /**
     * 同步人
     */
    @ApiModelProperty(value = "同步人")
    @FieldInterpretation(value = "同步人")
    private String syncUserId;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @FieldInterpretation(value = "版本号")
    private Integer versionValue;

    @Override
    public void clean() {

    }

}
