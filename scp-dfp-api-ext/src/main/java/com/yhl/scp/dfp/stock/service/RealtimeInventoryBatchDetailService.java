package com.yhl.scp.dfp.stock.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesRealTimeInventory;
import com.yhl.scp.dfp.stock.dto.RealtimeInventoryBatchDetailDTO;
import com.yhl.scp.dfp.stock.vo.RealtimeInventoryBatchDetailVO;

import java.util.List;

/**
 * <code>RealtimeInventoryBatchDetailService</code>
 * <p>
 * 实时库存批次明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:56:11
 */
public interface RealtimeInventoryBatchDetailService extends BaseService<RealtimeInventoryBatchDetailDTO, RealtimeInventoryBatchDetailVO> {

    /**
     * 查询所有
     *
     * @return list {@link RealtimeInventoryBatchDetailVO}
     */
    List<RealtimeInventoryBatchDetailVO> selectAll();

    /**
     * 同步MES实时库存数据
     *
     * @param scenario 场景
     * @param mesRealTimeInventories MES实时库存数据列表
     * @param orgIds 组织ID列表
     * @return 同步结果
     */
    BaseResponse<Void> sync(String scenario, List<MesRealTimeInventory> mesRealTimeInventories, List<String> orgIds,String syncUser);

}
