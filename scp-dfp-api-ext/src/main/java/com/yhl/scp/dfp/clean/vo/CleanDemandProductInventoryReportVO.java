package com.yhl.scp.dfp.clean.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.annotation.FieldInterpretation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <code>CleanDemandProductInventoryReportVO</code>
 * <p>
 * 风险车型管控-产品库存报表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-21 10:28:21
 */
@ApiModel(value = "风险车型管控-产品库存报表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CleanDemandProductInventoryReportVO implements Serializable {

    private static final long serialVersionUID = -85130264218462187L;
    
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @FieldInterpretation(value = "ID")
    private String id;

    /**
     * 版本ID
     */
    @ApiModelProperty(value = "版本ID")
    @FieldInterpretation(value = "版本ID")
    private String versionId;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    @FieldInterpretation(value = "需求类型")
    private String demandCategory;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @FieldInterpretation(value = "版本号")
    private String versionCode;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    
    /**
     * 零件编码（零件号）
     */
    @ApiModelProperty(value = "零件编码（零件号）")
    @FieldInterpretation(value = "零件编码（零件号）")
    private String partName;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    @FieldInterpretation(value = "需求类型")
    private String demandType;
    
    /**
     * 计划员
     */
    @ApiModelProperty(value = "计划员")
    @FieldInterpretation(value = "计划员")
    private String orderPlanner;
    
    /**
     * 计划员
     */
    @ApiModelProperty(value = "计划员")
    @FieldInterpretation(value = "计划员")
    private String customerAbbreviation;
    
    /**
     * 成品库库存/成品库
     */
    @ApiModelProperty(value = "成品库库存")
    @FieldInterpretation(value = "成品库库存")
    private BigDecimal finishedInventory;
    
    /**
     * 期初库存(厂外)/中转库
     */
    @ApiModelProperty(value = "期初库存(厂外)")
    @FieldInterpretation(value = "期初库存(厂外)")
    private Integer openingInventory;
    
    /**
     * 主机厂期初库存/主机厂
     */
    @ApiModelProperty(value = "主机厂期初库存")
    @FieldInterpretation(value = "主机厂期初库存")
    private Integer oemOpeningInventory;
    
    /**
     * 在产品
     */
    @ApiModelProperty(value = "在产品")
    @FieldInterpretation(value = "在产品")
    private BigDecimal inProductInventory;
    
    /**
     * 合计
     */
    @ApiModelProperty(value = "合计")
    @FieldInterpretation(value = "合计")
    private BigDecimal totalInventory;
    
    /**
     * 库存天数
     */
    @ApiModelProperty(value = "库存天数")
    @FieldInterpretation(value = "库存天数")
    private BigDecimal inventoryDays;
    
    /**
     * 日均需求
     */
    @ApiModelProperty(value = "日均需求")
    @FieldInterpretation(value = "日均需求")
    private BigDecimal averageDemandQuantity;
    
    /**
     * 当月实际出货
     */
    @ApiModelProperty(value = "当月实际出货")
    @FieldInterpretation(value = "当月实际出货")
    private String cuurMonthDeliveryQty;
    
    /**
     * 下月预测
     */
    @ApiModelProperty(value = "下月预测")
    @FieldInterpretation(value = "下月预测")
    private BigDecimal nextMonthForecastQuantity;
    
    /**
     * 下下月预测
     */
    @ApiModelProperty(value = "下下月预测")
    @FieldInterpretation(value = "下下月预测")
    private BigDecimal afterNextMonthForecastQuantity;
    
    /**
     * 成本金额
     */
    @ApiModelProperty(value = "成本金额")
    @FieldInterpretation(value = "成本金额")
    private BigDecimal costAmount;
    
    /**
     * 数据时间
     */
    @ApiModelProperty(value = "数据时间")
    @FieldInterpretation(value = "数据时间")
    private Date dataTime;
    
    /**
     * 日均需求金额
     */
    @ApiModelProperty(value = "日均需求金额")
    @FieldInterpretation(value = "日均需求金额")
    private BigDecimal averageDemandAmount;
    
    /**
     * 主机厂交易类型：出口/国内
     */
    @ApiModelProperty(value = "主机厂交易类型：出口/国内")
    @FieldInterpretation(value = "主机厂交易类型：出口/国内")
    private String marketType;
    

}
