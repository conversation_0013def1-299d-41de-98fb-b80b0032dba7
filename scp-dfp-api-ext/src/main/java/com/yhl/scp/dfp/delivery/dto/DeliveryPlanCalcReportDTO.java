package com.yhl.scp.dfp.delivery.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.util.Date;

/**
 * <code>DeliveryPlanCalcReportDTO</code>
 * <p>
 * 发货计划计算数据汇总报表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:00:52
 */
@ApiModel(value = "发货计划计算数据汇总报表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeliveryPlanCalcReportDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 338064719953373437L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productCode;
    /**
     * 期初库存
     */
    @ApiModelProperty(value = "期初库存")
    private Integer openingInventory;
    /**
     * 主机厂期初库存
     */
    @ApiModelProperty(value = "主机厂期初库存")
    private Integer oemOpeningInventory;
    /**
     * 在途数量
     */
    @ApiModelProperty(value = "在途数量")
    private Integer receive;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;

}
