package com.yhl.scp.dfp.stock.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>RealtimeInventoryBatchDetailDTO</code>
 * <p>
 * 实时库存批次明细DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:56:10
 */
@ApiModel(value = "实时库存批次明细DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RealtimeInventoryBatchDetailDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -16651405084594013L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 现有量
     */
    @ApiModelProperty(value = "现有量")
    private String currentQuantity;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    private String freightSpace;
    /**
     * 货位描述
     */
    @ApiModelProperty(value = "货位描述")
    private String freightSpaceDescription;
    /**
     * 工序
     */
    @ApiModelProperty(value = "工序")
    private String operationCode;
    /**
     * 原始报文组织ID
     */
    @ApiModelProperty(value = "原始报文组织ID")
    private String originalOrgId;
    /**
     * 原始物料编码
     */
    @ApiModelProperty(value = "原始物料编码")
    private String originalProductCode;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String productCode;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    private String stockPointCode;
    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    private String subinventory;
    /**
     * 子库存描述
     */
    @ApiModelProperty(value = "子库存描述")
    private String subinventoryDescription;
    /**
     * 同步人
     */
    @ApiModelProperty(value = "同步人")
    private String syncUserId;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer versionValue;

}
