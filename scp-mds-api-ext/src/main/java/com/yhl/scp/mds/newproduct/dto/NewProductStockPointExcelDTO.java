package com.yhl.scp.mds.newproduct.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@ApiModel(value = "新-物品（Excel）DTO")
@Data
@SuperBuilder
@AllArgsConstructor
public class NewProductStockPointExcelDTO implements Serializable {

    private static final long serialVersionUID = -5207583120894854357L;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @ExcelProperty(value = "最后更新时间")
    private Date modifyTime;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    @ExcelProperty(value = "库存点编码")
    private String stockPointCode;
    /**
     * 物品代码
     */
    @ApiModelProperty(value = "物品代码")
    @ExcelProperty(value = "产品编码")
    private String productCode;
    /**
     * 物品名称
     */
    @ApiModelProperty(value = "物品名称")
    @ExcelProperty(value = "产品名称")
    private String productName;
    /**
     * 物品类型
     */
    @ApiModelProperty(value = "物品类型")
    @ExcelProperty(value = "物料类型")
    private String productType;
    /**
     * 物品分类
     */
    @ApiModelProperty(value = "物品分类")
    @ExcelProperty(value = "通用类别")
    private String productClassify;
    /**
     * 分类说明
     */
    @ApiModelProperty(value = "分类说明")
    @ExcelProperty(value = "类别说明")
    private String classifyDesc;
    /**
     * edi标志
     */
    @ApiModelProperty(value = "edi标志")
    @ExcelProperty(value = "是否EDI直连")
    private String ediFlag;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    @ExcelProperty(value = "物料是否有效")
    private String enabled;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @ExcelProperty(value = "内部车型代码")
    private String vehicleModelCode;
    /**
     * 供应类型
     */
    @ApiModelProperty(value = "供应类型")
    @ExcelProperty(value = "供应类型")
    private String supplyType;
    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    @ExcelProperty(value = "单位")
    private String measurementUnit;
    /**
     * 销售类型
     */
    @ApiModelProperty(value = "销售类型")
    @ExcelProperty(value = "销售类型")
    private String saleType;
    /**
     * 装车位置
     */
    @ApiModelProperty(value = "装车位置")
    @ExcelProperty(value = "装车位置")
    private String loadingPosition;
    /**
     * 装车位置小类
     */
    @ApiModelProperty(value = "装车位置小类")
    @ExcelProperty(value = "装车位置小类")
    private String loadingPositionSub;
    /**
     * 车型类型
     */
    @ApiModelProperty(value = "车型类型")
    @ExcelProperty(value = "车型类型")
    private String vehicleModelType;
    /**
     * 业务特性
     */
    @ApiModelProperty(value = "业务特性")
    @ExcelProperty(value = "业务特性")
    private String businessSpecial;
    /**
     * 核心工序
     */
    @ApiModelProperty(value = "核心工序")
    @ExcelProperty(value = "核心工序")
    private String coreProcess;
    /**
     * 产品特性
     */
    @ApiModelProperty(value = "产品特性")
    @ExcelProperty(value = "产品特性")
    private String productSpecial;
    /**
     * 采购计划类别
     */
    @ApiModelProperty(value = "采购计划类别")
    @ExcelProperty(value = "采购类别")
    private String poCategory;
    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    @ExcelProperty(value = "长")
    private BigDecimal productLength;
    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    @ExcelProperty(value = "宽")
    private BigDecimal productWidth;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    @ExcelProperty(value = "厚度")
    private BigDecimal productThickness;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @ExcelProperty(value = "颜色")
    private String productColor;
    /**
     * 面积M2
     */
    @ApiModelProperty(value = "面积M2")
    @ExcelProperty(value = "面积M2")
    private BigDecimal productArea;
    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    @ExcelProperty(value = "重量")
    private BigDecimal productWeight;
    /**
     * 重量单位
     */
    @ApiModelProperty(value = "重量单位")
    @ExcelProperty(value = "重量单位")
    private String weightUnit;
    /**
     * 理货单模式，MES，GRP
     */
    @ApiModelProperty(value = "理货单模式，MES，GRP")
    @ExcelProperty(value = "理货单模式")
    private String tallyOrderMode;
    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    @ExcelProperty(value = "保质期")
    private Integer expireDate;
    /**
     * 特性说明
     */
    @ApiModelProperty(value = "特性说明")
    @ExcelProperty(value = "特性说明")
    private String specialDesc;
    /**
     * 运输周期
     */
    @ApiModelProperty(value = "运输周期")
    @ExcelProperty(value = "运输周期")
    private BigDecimal transportCycle;
    /**
     * 订单计划员
     */
    @ApiModelProperty(value = "订单计划员")
    @ExcelProperty(value = "订单计划员")
    private String orderPlanner;
    /**
     * 生产计划员
     */
    @ApiModelProperty(value = "生产计划员")
    @ExcelProperty(value = "生产计划员")
    private String productionPlanner;
    /**
     * 材料计划员
     */
    @ApiModelProperty(value = "材料计划员")
    @ExcelProperty(value = "材料计划员")
    private String materialPlanner;
    /**
     * 采购计划员
     */
    @ApiModelProperty(value = "采购计划员")
    @ExcelProperty(value = "采购计划员")
    private String purchasePlanner;
    /**
     * SOP
     */
    @ApiModelProperty(value = "SOP")
    @ExcelProperty(value = "SOP")
    private Date productSop;
    /**
     * EOP
     */
    @ApiModelProperty(value = "EOP")
    @ExcelProperty(value = "EOP")
    private Date productEop;
    /**
     * 物料大类
     */
    @ApiModelProperty(value = "物料大类")
    @ExcelProperty(value = "物料类型")
    private String productCategory;
    /**
     * 物料状态
     * Active
     * Inactive
     * PR-STOP
     * WIP-STOP
     */
    @ApiModelProperty(value = "物料状态")
    @ExcelProperty(value = "物料状态")
    private String inventoryItemStatusCode;
    /**
     * 自提类型：自提，非自提
     */
    @ApiModelProperty(value = "自提类型：自提，非自提")
    @ExcelProperty(value = "是否自提")
    private String pickUpType;
    /**
     * 是否整箱
     */
    @ApiModelProperty(value = "是否整箱")
    @ExcelProperty(value = "是否整箱")
    private String fullBoxFlag;
}
