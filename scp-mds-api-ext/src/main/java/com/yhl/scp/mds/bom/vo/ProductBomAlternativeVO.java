package com.yhl.scp.mds.bom.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>ProductBomAlternativeVO</code>
 * <p>
 * 物品BOM替代VO (仅用于查找BOM替代料)
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 10:25:47
 */
@ApiModel(value = "物品BOM替代VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductBomAlternativeVO implements Serializable {

    private static final long serialVersionUID = 485264072507892355L;

    @ApiModelProperty(value = "替代料编码")
    @FieldInterpretation(value = "替代料编码")
    private String alternativeProductCode;

    @ApiModelProperty(value = "替代用量")
    @FieldInterpretation(value = "替代用量")
    private BigDecimal alternativeQuantity;

    @ApiModelProperty(value = "替代方式")
    @FieldInterpretation(value = "替代方式")
    private String alternativeMethod;

    @ApiModelProperty(value = "优先级")
    @FieldInterpretation(value = "优先级")
    private Integer priority;
}
