package com.yhl.scp.mds.deleteGroup.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpDeleteGroup;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.deleteGroup.dto.MdsDeleteGroupsDTO;
import com.yhl.scp.mds.deleteGroup.vo.MdsDeleteGroupsVO;

import java.util.List;

/**
 * <code>MdsDeleteGroupsService</code>
 * <p>
 * ERP删除组接口中间表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14 20:24:36
 */
public interface MdsDeleteGroupsService extends BaseService<MdsDeleteGroupsDTO, MdsDeleteGroupsVO> {

    /**
     * 查询所有
     *
     * @return list {@link MdsDeleteGroupsVO}
     */
    List<MdsDeleteGroupsVO> selectAll();
    void doCreateBatchWithPartition(List<MdsDeleteGroupsDTO> list);
    void doUpdateBatchWithPartition(List<MdsDeleteGroupsDTO> list);
    List<MdsDeleteGroupsVO> selectByDeleteGroupNames(List<String> deleteGroupNames);

    BaseResponse<Void> sync(List<ErpDeleteGroup> o);

    BaseResponse<Void> syncDeleteGroups(Scenario scenario);
}
