pipeline {
    agent any
    // 定义参数
    parameters {
        string(name: 'image_name', defaultValue: 'bpim-ui-service', description: '镜像name')
        string(name: 'image_tag', defaultValue: '1.0.0', description: '镜像tag')
        choice(name: 'module_code', choices: ['ips', 'dfp', 'mds','mrp','mps'], description: '模块编码')
        string(name: 'harbor_prefix', defaultValue: 'harbor.fuyaogroup.com/bpim', description: 'harbor地址')
        choice(name: 'git_branch', choices: ['release'], description: 'git分支')
        string(name: 'profile', defaultValue: 'uat', description: '当前环境')
    }
    environment{
        PATH='PATH+EXTRA=/u01/apache-maven-3.6.3/bin:usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin'
        CURRENT_TIME_STR=new Date().format('yyyyMMddHHmmss')
    }
    stages {
        stage('pull code') {
            steps {
                checkout([$class: 'GitSCM', branches: [[name: '*/${git_branch}']], doGenerateSubmoduleConfigurations: false, extensions: [], submoduleCfg: [], userRemoteConfigs: [[credentialsId: 'app_deploy', url: 'http://gitlab.fuyaogroup.com/bpim/bpim-ui.git']]])
            }
        }
        stage("build project"){
            steps{
                sh '''
                   cd ./scp-${module_code}-front
                   npm i
                   npm run build:uat
                '''
            }
        }
        stage("build image"){
            steps {
                sh '''
                    rm -rf ./docker/${profile}/app-${module_code}-front
                    mv ./scp-${module_code}-front/app-${module_code}-front ./docker/${profile}
                    cd ./docker/${profile}
                    docker build -t ${harbor_prefix}/${image_name}-${profile}:${image_tag}_${CURRENT_TIME_STR} .
                '''
            }
        }
        stage("push image"){
            steps {
                sh 'docker push ${harbor_prefix}/${image_name}-${profile}:${image_tag}_${CURRENT_TIME_STR}'
            }
        }
        stage("k8s apply"){
            steps {
                sh '''
                cd ./docker/${profile}
                sed -i "s#bpim-demo-image#${harbor_prefix}/${image_name}-${profile}:${image_tag}_${CURRENT_TIME_STR}#g" k8s-front.yaml
                kubectl apply -f k8s-front.yaml
                '''
            }
        }
    }
}