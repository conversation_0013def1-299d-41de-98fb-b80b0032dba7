pipeline {
    agent any
    // 定义参数
    parameters {
        string(name: 'java_options', defaultValue: '-Xms2048m -Xmx2048m', description: 'java运行参数')
        string(name: 'image_name', defaultValue: 'bpim-dfp-service', description: '镜像name')
        string(name: 'image_tag', defaultValue: '1.0.0', description: '镜像tag')
        string(name: 'module_code', defaultValue: 'dfp', description: '模块编码')
        string(name: 'harbor_prefix', defaultValue: 'harbor.fuyaogroup.com/bpim', description: 'harbor地址')
        string(name: 'profile', defaultValue: 'uat', description: '当前环境')
        string(name: 'k8s_replicas', defaultValue: '1', description: '副本数量')
        string(name: 'k8s_port', defaultValue: '8764', description: '服务端口')
        string(name: 'k8s_namespace', defaultValue: 'bpim-application', description: '命名空间')
        string(name: 'k8s_pvc', defaultValue: 'bpim-data-pvc', description: 'PVC')
    }
    environment{
        PATH='PATH+EXTRA=/u01/apache-maven-3.6.3/bin:usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin'
        CURRENT_TIME_STR=new Date().format('yyyyMMddHHmmss')
    }
    stages {
        stage('pull code') {
            steps {
                checkout([$class: 'GitSCM', branches: [[name: '*/release']], doGenerateSubmoduleConfigurations: false, extensions: [], submoduleCfg: [], userRemoteConfigs: [[credentialsId: 'app_deploy', url: 'http://gitlab.fuyaogroup.com/bpim/bpim.git']]])
            }
        }
        stage('build project') {
            steps {
                sh '/u01/apache-maven-3.6.3/bin/mvn clean package -pl scp-${module_code}-service -am -Dmaven.test.skip=true -U'
            }
        }
        stage('build image') {
            steps {
                sh '''
                cp ./scp-${module_code}-service/target/scp-${module_code}-service.jar ./docker/backend
                cd ./docker/backend
                docker build --build-arg JAVA_OPTS="${java_options}" --build-arg SPRING_PROFILE=${profile} -t ${harbor_prefix}/${image_name}-${profile}:${image_tag}_${CURRENT_TIME_STR} .
                '''
            }
        }
        stage('push image'){
            steps {
                sh 'docker push ${harbor_prefix}/${image_name}-${profile}:${image_tag}_${CURRENT_TIME_STR}'
            }
        }
        stage('rmi image'){
            steps {
                sh '''
                rm -rf ./scp-${module_code}-service/target/*
                rm -rf ./docker/backend/scp-${module_code}-service.jar
                docker system prune
                docker rmi ${harbor_prefix}/${image_name}-${profile}:${image_tag}_${CURRENT_TIME_STR}
                '''
            }
        }
        stage('k8s apply'){
            steps {
               sh '''
               cd ./docker/backend
               sed -i "s#bpim-xxx-pvc#${k8s_pvc}#g" k8s.yaml
               sed -i "s#bpim-xxx-namespace#${k8s_namespace}#g" k8s.yaml
               sed -i "s#bpim-xxx-service#bpim-${module_code}-service#g" k8s.yaml
               sed -i "s#bpim-xxx-image#${harbor_prefix}/${image_name}-${profile}:${image_tag}_${CURRENT_TIME_STR}#g" k8s.yaml
               sed -i "s|\\( *replicas: \\)[0-9]*|\\1${k8s_replicas}|" k8s.yaml
               sed -i "s|\\( *port: \\)8760|\\1${k8s_port}|g;s|\\( *containerPort: \\)8760|\\1${k8s_port}|g;s|\\( *targetPort: \\)8760|\\1${k8s_port}|g;s|\\( *servicePort: \\)8760|\\1${k8s_port}|g" k8s.yaml
               kubectl apply -f k8s.yaml --record
               '''
            }
        }
    }
}