package com.yhl.scp.dcp.enmus;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>MesApiReqCodeEnum</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-15 12:55:28
 */
public enum MesApiReqCodeEnum implements CommonEnum {
    FY_ASL_FOR_BPIM("FY_ASL_FOR_BPIM", "MES物料合格供应商接口"),
    FY_PKN_AND_SGL_FOR_BPIM("FY_PKN_AND_SGL_FOR_BPIM", "MES库存批次明细接口"),
    FY_FINE_OP_YIELD_FOR_BPIM("FY_FINE_OP_YIELD_FOR_BPIM", "MES工序成品率接口"),
    FYE_FORECAST_FOR_ERP("FYE_FORECAST_FOR_ERP", "FYE-MES预测需求接口"),
    FYE_SHIP_REQ_FOR_ERP("FYE_SHIP_REQ_FOR_ERP", "FYE-MES发运需求接口"),
    FY_ITEM_GH_DATA_FOR_BPIM("FY_ITEM_GH_DATA_FOR_BPIM", "MES系统产品固化数据接口"),
    FY_DLY_ATTR_FOR_BPIM("FY_DLY_ATTR_FOR_BPIM", "系统成品发送属性数据"),
    FY_ENTITY_REPORT_FOR_BPIM("FY_ENTITY_REPORT_FOR_BPIM", "MES报工数据"),
    FY_INV_ONHAND_FOR_BPIM("FY_INV_ONHAND_FOR_BPIM", "MES实时库存数据"),
    ;

    MesApiReqCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    private String code;

    private String desc;
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
