package com.yhl.scp.dcp.apiConfig.externalApi.resp.mes;

import lombok.Data;

/**
 * <code>MesInventoryBatchDetail</code>
 * <p>
 * MES库存批次明细返回体
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 11:27:15
 */
@Data
public class MesRealTimeInventory {

    /**
     * 现有量
     */
    private Integer loctOnhand;

    /**
     * 客户号
     */
    private String supplierId;

    /**
     * 条码号
     */
    private String uomCode;

    /**
     * 货位表描述
     */
    private String colsIn;

    /**
     * 拆分工序号
     */
    private String splitedSequence;

    /**
     * 批次
     */
    private String whDesc;

    /**
     * 工厂ID
     */
    private String plantId;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 本厂编号id
     * 与物料表的inventory_item_id字段关联
     */
    private String itemId;
    /***
     * 本厂编号
     */
    private String itemCode;

    /**
     * 货位编码
     */
    private String locatorCode;

    /**
     * 是否B类物料
     */
    private String isb;

    /**
     * 工厂编码
     */
    private String plantCode;

}
