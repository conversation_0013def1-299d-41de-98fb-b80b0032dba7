package com.yhl.scp.dcp.apiConfig.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * 接口分类
 */
public enum ApiCategoryEnum implements CommonEnum {

    AUTH("AUTH", "鉴权"),

    SALE_ORGANIZE("SALE_ORGANIZE", "销售组织"),

    CUSTOMER("CUSTOMER", "客户信息"),

    PRODUCT("PRODUCT", "物料信息"),

    ORDER("ORDER", "订单信息"),

    STOCK_POINT("STOCK_POINT", "库存点"),

    HIGH_VALUE_PRODUCT("HIGH_VALUE_PRODUCT", "高价值物料"),

    LOCATOR_DESCRIPTION("LOCATOR_DESCRIPTION", "子库存货位信息"),

    PRODUCT_CANDIDATE_RESOURCE("PRODUCT_CANDIDATE_RESOURCE", "产品资源生产关系"),

    MES_INVENTORY_BATCH_DETAIL("MES_INVENTORY_BATCH_DETAIL", "MES库存批次明细"),

    PART_MAPPING("PART_MAPPING", "GRP零件映射"),

    MES_PART_MAPPING("MES_PART_MAPPING", "MES零件映射"),

    GRP_TOKEN("GRP_TOKEN", "获取GRP的token"),

    OA_TOKEN("OA_TOKEN", "获取OA的token"),

    NEW_PRODUCT_TRAIL("NEW_PRODUCT_TRAIL", "获取OA的新品试制卡"),

    MDM_PART_MAPPING("MDM_PART_MAPPING", "mdm获取零件映射"),

    BOX_INFO("BOX_INFO", "箱体信息"),

    WAREHOUSE_RELEASE("WAREHOUSE_RELEASE", "仓库发货记录"),

    PRODUCT_BOX_RELATION("PRODUCT_BOX_RELATION", "产品与成品箱关系"),

    LOADING_DEMAND("LOADING_DEMAND", "EDI装车需求"),

    LOADING_FORECAST("LOADING_FORECAST", "EDI装车预测"),

    MES_SHIP_DEMAND("MES_SHIP_DEMAND", "MES发运需求"),

    MES_FORECAST_DEMAND("MES_FORECAST_DEMAND", "MES预测需求"),

    REPORT_FEEDBACK("REPORT_FEEDBACK", "获取生产报工反馈"),

    ALL_ORGANIZATION("ALL_ORGANIZATION", "IAM同步组织"),

    ALL_USER("ALL_USER", "IAM同步用户"),

    USER_CALLBACK("USER_CALLBACK", "IAM同步用户"),

    TRANSPORT_ROUTING("TRANSPORT_ROUTING", "运输路径"),

    ROUTING_STEP_INPUT("ROUTING_STEP_INPUT", "获取生产路径步骤输入物品"),

    PRODUCT_SUBSTITUTION("PRODUCT_SUBSTITUTION", "获取BOM替代料关系"),

    OEM_ADDRESS("OEM_ADDRESS", "主机厂地址"),

    MOLD_CHANGE_TIME("MOLD_CHANGE_TIME", "换模换型时间"),

    PRODUCT_STOCK_POINT_BASE("PRODUCT_STOCK_POINT_BASE", "产品工艺基础数据"),

    SUPPLIER("SUPPLIER", "供应商基础数据"),

    SUPPLIER_PURCHASE("SUPPLIER_PURCHASE", "材料与供应商关系"),

    INVENT_ORY_OCEAN_FREIGHT("INVENT_ORY_OCEAN_FREIGHT", "浮法海运接口"),

    SHIPPING_RECORD("SHIPPING_RECORD", "FYSL发货记录"),
    PRODUCT_QUALIFIED_SUPPLIER("PRODUCT_QUALIFIED_SUPPLIER", "物料合格供应商"),

    ORIGINAL_FILM_FF_INVENTORY("ORIGINAL_FILM_FF_INVENTORY", "原片浮法库存"),
    ORIGINAL_FILM_FF_ONWAY("ORIGINAL_FILM_FF_ONWAY", "原片浮法在途库存"),
    PURCHASE_ORDER("PURCHASE_ORDER", "采购PO"),
    ERP_INVENTORY_BATCH_DETAIL("ERP_INVENTORY_BATCH_DETAIL", "ERP库存批次明细"),
    PRODUCT_ROUTING("PRODUCT_ROUTING", "ERP产品工艺路径"),
    OP_YIELD("OP_YIELD", "MES工序成品率"),
    DELETE_GROUP("DELETE_GROUP", "ERP删除组"),

    MATERIAL_DELIVERY_NOTE("MATERIAL_DELIVERY_NOTE", "材料在途数据"),

    CHAIN_LINE("CHAIN_LINE", "链式生产线"),

    PR_QUERY("PR_QUERY", "PR查询"),

    PR_CREATE("PR_CREATE", "PR创建"),

    EN_ROUTE("EN_ROUTE", "FYDS在途数据"),

    MATERIAL_PURCHASE_STORAGE("MATERIAL_PURCHASE_STORAGE", "采购入库记录"),

    CONSIGNMENT_PRODUCT("CONSIGNMENT_PRODUCT", "ERP委托产品关系"),

    STANDARD_RESOURCE("STANDARD_RESOURCE", "资源组"),

    PHYSICAL_RESOURCE("PHYSICAL_RESOURCE", "主资源"),

    NAKED_GLASS("NAKED_GLASS", "裸玻成品映射"),

    MOLD_TOOLING("MOLD_TOOLING", "系统模具工装族与工装编号关系"),

    MOLD_TOOLING_GROUP("MOLD_TOOLING_GROUP", "模具工装族表数据"),

    MOLD_TOOLING_GROUP_DIR("MOLD_TOOLING_GROUP_DIR", "系统模具工装族与目录号关系数据"),

    DELIVERY_DOCKING_GRP("DELIVERY_DOCKING_GRP", "同步理货单数据给GRP"),

    DELIVERY_DOCKING_MES("DELIVERY_DOCKING_MES", "同步理货单数据给MES"),

    CARRIER("CARRIER", "承运商"),

    DELIVERY_PLAN_MES("DELIVERY_PLAN_MES", "发货计划给MES"),

    MATERIAL_PRODUCT_NEED_DAY("MATERIAL_PRODUCT_NEED_DAY", "要货计划-下发要货计划（天）"),

    MATERIAL_PRODUCT_NEED_MONTH("MATERIAL_PRODUCT_NEED_MONTH", "要货计划-下发要货计划（月）"),

    MATERIAL_PRODUCT_NEED_PLANNER_CONFIRM("MATERIAL_PRODUCT_NEED_PLANNER_CONFIRM", "要货计划-计划员确认"),

    MATERIAL_PRODUCT_NEED_ARRIVED("MATERIAL_PRODUCT_NEED_ARRIVED", "要货计划-下发到货"),

    MATERIAL_ARRIVAL_TRACKING_CLOSED("MATERIAL_ARRIVAL_TRACKING_CLOSED", "到货跟踪-关单"),

    MATERIAL_PRODUCT_NEED_CANCEL_ISSUE("MATERIAL_PRODUCT_NEED_CANCEL_ISSUE","要货计划-取消下发"),

    MATERIAL_ARRIVAL_TRACKING_UPDATE_NEED("MATERIAL_ARRIVAL_TRACKING_UPDATE_NEED", "到货跟踪-更新单（要货计划模式）"),

    MATERIAL_ARRIVAL_TRACKING_UPDATE_PR("MATERIAL_ARRIVAL_TRACKING_UPDATE_PR", "到货跟踪-更新单（PR模式）"),

    MATERIAL_ARRIVAL_TRACKING_ISSUE("MATERIAL_ARRIVAL_TRACKING_ISSUE", "到货跟踪-下发）"),

    CUSTOMER_DATA("CUSTOMER_DATA", "客户信息表"),
    NEW_PRODUCT_CANDIDATE_RESOURCE("NEW_PRODUCT_CANDIDATE_RESOURCE", "产品资源生产关系和生产节拍"),
    PLAN_ORDER_CREATE("PLAN_ORDER_CREATE", "ERP计划单创建"),
    PLAN_ORDER_QUERY("PLAN_ORDER_QUERY", "ERP计划单查询"),
    PLAN_ORDER_UPDATE("PLAN_ORDER_UPDATE", "ERP计划单更新"),

    MASTER_PLAN_RELATION("MASTER_PLAN_RELATION", "ERP获取工单号回传"),

    EMAIL_MESSAGE("EMAIL_MESSAGE", "通过FONE发送邮件"),

    MESSAGE_NOTIFY("MESSAGE_NOTIFY", "消息通知"),

    PRODUCT_SUBSTITUTION_FEEDBACK("PRODUCT_SUBSTITUTION_FEEDBACK", "BOM替代料回传优先级"),

    PO_CREATE("PO_CREATE", "PO创建"),

    RETURNED_PURCHASE("RETURNED_PURCHASE", "采购退货"),
    CURING_TIME("CURING_TIME", "MES固化时间"),
    FINISHED_PRODUCT_DELIVERY("FINISHED_PRODUCT_DELIVERY", "系统成品发送属性"),

    PR_CANCEL("PR_CANCEL", "PR取消"),

    PO_CLOSED("PO_CLOSED", "PO关闭"),
    MES_FEED_BACK("MES_FEED_BACK", "MES生产反馈"),

    CREATE_PRODUCTION_HANDOVER("CREATE_PRODUCTION_HANDOVER", "量产移交"),

    PRODUCTION_HANDOVER_STATUS("PRODUCTION_HANDOVER_STATUS", "量产移交状态"),

    REAL_ALL_USER("REAL_ALL_USER", "IAM全量同步用户"),

    MATERIAL_TRANSACTIONS("MATERIAL_TRANSACTIONS", "物料事务处理查询"),

    LLM_GEMINI_DEMAND_FILE_PARSE("GEMINI_DEMAND_FILE_PARSE", "GOOGLE Gemini大模型客户需求文档解析"),

    PRODUCT_TIME("PRODUCT_TIME", "产品时间"),

    FYE_WAREHOUSE_RELEASE("FYE_WAREHOUSE_RELEASE","FYE仓库发货记录"),
    FYE_MES_INVENTORY_BATCH_DETAIL("FYE_MES_INVENTORY_BATCH_DETAIL", "FYE MES库存批次明细"),
    MES_REALTIME_INVENTORY("MES_REALTIME_INVENTORY", "MES实时库存批次明细"),

    ;

    private String code;

    private String desc;

    ApiCategoryEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
