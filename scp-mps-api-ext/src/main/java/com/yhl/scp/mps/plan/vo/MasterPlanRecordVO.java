package com.yhl.scp.mps.plan.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <code>MasterPlanRecordVO</code>
 * <p>
 * VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 10:22:51
 */
@ApiModel(value = "VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MasterPlanRecordVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -65867232012103085L;

    /**
     * 场景
     */
    @ApiModelProperty(value = "场景")
    @FieldInterpretation(value = "场景")
    private String scenario;
    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    @FieldInterpretation(value = "文件名")
    private String fileName;

    @Override
    public void clean() {

    }

}
