package com.yhl.scp.mps.nakedGlass.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.nakedGlass.dto.NakedGlassDTO;
import com.yhl.scp.mps.nakedGlass.vo.NakedGlassVO;

import java.util.List;

/**
 * <code>NakedGlassService</code>
 * <p>
 * 国内裸玻映射应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-27 11:06:37
 */
public interface NakedGlassService extends BaseService<NakedGlassDTO, NakedGlassVO> {

    /**
     * 查询所有
     *
     * @return list {@link NakedGlassVO}
     */
    List<NakedGlassVO> selectAll();

    BaseResponse syncData(String tenantId, String dataBaseName);

    BaseResponse<Void> syncNakedGlassData(List<NakedGlassDTO> list);

    void doCreateNewBatch(List<NakedGlassDTO> list);
    List<NakedGlassVO> selectNakedGlassByFinishedProducts(List<String> productCodes);
}
