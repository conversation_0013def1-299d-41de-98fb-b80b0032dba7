package com.yhl.scp.mps.plan.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OperationPackingReportDTO</code>
 * <p>
 * OperationPackingReportDTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:41:16
 */
@ApiModel(value = "包装工序完工表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OperationPackingReportDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -48268657870657944L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 制造订单ID
     */
    @ApiModelProperty(value = "制造订单ID")
    private String orderId;
    /**
     * 工序id
     */
    @ApiModelProperty(value = "工序id")
    private String operationId;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 完工数量
     */
    @ApiModelProperty(value = "完工数量")
    private BigDecimal finishedQuantity;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
     * 流水线时间
     */
    @ApiModelProperty(value = "流水线时间")
    private Integer lineTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}
