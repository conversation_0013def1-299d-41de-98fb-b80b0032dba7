package com.yhl.scp.mps.plan.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>MasterPlanRecordDTO</code>
 * <p>
 * DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-19 10:22:51
 */
@ApiModel(value = "DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MasterPlanRecordDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -96124578970431390L;

    @ApiModelProperty(value = "${column.comment}")
    private String id;
    /**
     * 场景
     */
    @ApiModelProperty(value = "场景")
    private String scenario;
    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;

}
