package com.yhl.scp.mps.plan.vo;

import com.yhl.scp.sds.extension.order.vo.OperationVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * <code>PlanNodeTrackingVO</code>
 * <p>
 * PlanNodeTrackingVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-14 19:04:26
 */
@ApiModel(value = "VO")
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PlanNodeTrackingVO extends OperationVO {

    @ApiModelProperty("主机厂")
    private String oem;

    @ApiModelProperty("车型代码")
    private String vehicleModelCode;

    @ApiModelProperty("制造订单齐套状态")
    private String orderKitStatus;

    @ApiModelProperty("标准工艺名称")
    private String standardStepName;

    @ApiModelProperty(value = "标准工艺代码")
    private String standardStepCode;

    @ApiModelProperty(value = "完工数量")
    private BigDecimal finishedQty;

    private String bomType;
    private String orderParentId;

}
