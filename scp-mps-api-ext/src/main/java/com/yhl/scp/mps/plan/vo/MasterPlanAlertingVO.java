package com.yhl.scp.mps.plan.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <code>MasterPlanAlertingVO</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-18 16:43:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "主生产计划-告警信息")
public class MasterPlanAlertingVO implements Serializable {


    private static final long serialVersionUID = 359477121399730854L;


    private String productId;

    private String productCode;

    private Integer sequenceNo;

    private String standardStepName;

    private String sourceProductCode;
}
