package com.yhl.scp.mps.plan.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>FulfillDetailVO</code>
 * <p>
 * 分配明细查询
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-16 15:54:04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FulfillDetailVO {

    @ApiModelProperty(value = "需求id")
    private String demandId;

    @ApiModelProperty(value = "分配明细id")
    private String fulfillmentId;

    @ApiModelProperty(value = "物品id")
    private String productId;

    @ApiModelProperty(value = "需求物品代码")
    private String productCode;

    @ApiModelProperty(value = "供应物品代码")
    private String supplyProductCode;

    @ApiModelProperty(value = "需求成品物品代码")
    private String finishedProductCode;

    @ApiModelProperty(value = "需求物品类型")
    private String productType;

    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "供应类型")
    private String supplyType;

    @ApiModelProperty(value = "分配数量")
    private BigDecimal fulfillmentQuantity;

    @ApiModelProperty(value = "缺口数量")
    private BigDecimal unFulfillQuantity;

    @ApiModelProperty(value = "单位转换比例")
    private BigDecimal unitConversionRatio;

    @ApiModelProperty(value = "净需求数量")
    private BigDecimal netDemandQuantity;

    @ApiModelProperty(value = "是否使用了替代料")
    private String altMaterialUsed;

    @ApiModelProperty(value = "需求时间")
    private Date demandTime;

    @ApiModelProperty(value = "供应时间")
    private Date supplyTime;

}
