package com.yhl.scp.mps.plan.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>PlanNodeTrackingRes</code>
 * <p>
 * PlanNodeTrackingRes
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-14 10:17:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlanNodeTrackingRes {

    // 主机厂
    private String oem;
    // 车型
    private String vehicleModelCode;
    // 物品
    private String productCode;
    private String semiProductCode;
    private String stockPointCode;
    // 当日发货量
    private Integer currentQuantity;
    // 2天发货量
    private Integer twoDaysQuantity;
    // 3天发货量
    private Integer threeDaysQuantity;
    // 7天发货量
    private Integer sevenDaysQuantity;
    // 当日已发货量
    private String onTheDayQuantity;
    // 成品量
    private String finishedQuantity;
    // 包装量
    private String packingQuantity;
    // 和片量
    private String mergeQuantity;
    // 成型后
    private String formingQuantity;

    // 包装工序信息
    private BigDecimal packingPlanQuantity;
    private String packingOperationId;
    private String packingOperationPlanTime;
    private String packingOperationQuantity;
    private Date packingOperationPlanDate;
    // 成型工序信息
    private String formingOperationPlanTime;
    private String formingOperationQuantity;

    // 齐套状态
    private String kitStatus;
    // 制造订单id，关联齐套状态使用
    private String orderId;

}
