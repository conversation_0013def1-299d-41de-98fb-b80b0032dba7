package com.yhl.scp.mds.routing.controller;

import java.util.List;

import javax.annotation.Resource;

import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.product.service.ProductCandidateResourceTimeService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mds.routing.dto.ProductCandidateResourceDTO;
import com.yhl.scp.mds.routing.service.NewProductCandidateResourceService;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>NewProductCandidateResourceController</code>
 * <p>
 * 物品候选资源控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-18 11:45:18
 */
@Slf4j
@Api(tags = "物品候选资源控制器")
@RestController
@RequestMapping("mdsProductCandidateResource")
public class MdsProductCandidateResourceController extends BaseController {

    @Resource
    private NewProductCandidateResourceService newProductCandidateResourceService;

    @Resource
    private ProductCandidateResourceTimeService productCandidateResourceTimeService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<ProductCandidateResourceVO>> page() {
        List<ProductCandidateResourceVO> productCandidateResourceList = newProductCandidateResourceService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<ProductCandidateResourceVO> pageInfo = new PageInfo<>(productCandidateResourceList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ProductCandidateResourceDTO productCandidateResourceDTO) {
        return newProductCandidateResourceService.doCreate(productCandidateResourceDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ProductCandidateResourceDTO productCandidateResourceDTO) {
        return newProductCandidateResourceService.doUpdate(productCandidateResourceDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
    	newProductCandidateResourceService.doLogicDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<ProductCandidateResourceVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, newProductCandidateResourceService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncMoldChangeTime() {
        return productCandidateResourceTimeService.syncMoldChangeTime(SystemHolder.getTenantCode(),SystemHolder.getScenario());
    }
}
