package com.yhl.scp.mds.bom.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.convertor.MdsProductBomConvertor;
import com.yhl.scp.mds.bom.domain.entity.ProductBomDO;
import com.yhl.scp.mds.bom.domain.service.MdsProductBomDomainService;
import com.yhl.scp.mds.bom.dto.ProductBomDTO;
import com.yhl.scp.mds.bom.infrastructure.dao.MdsProductBomDao;
import com.yhl.scp.mds.bom.infrastructure.po.ProductBomPO;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.vo.ProductBomAlternativeVO;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.enums.NewProductEnum;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.substitution.service.ProductSubstitutionRelationshipService;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import com.yhl.scp.mrp.extension.material.vo.ConsistencyWarningVO;
import com.yhl.scp.mrp.material.plan.enums.MaterialTypeEnum;
import com.yhl.scp.mrp.substitutionRelationship.vo.GlassSubstitutionRelationshipVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MdsProductBomServiceImpl extends AbstractService implements MdsProductBomService {

    @Resource
    private MdsProductBomDao mdsProductBomDao;

    @Resource
    private MdsProductBomDomainService mdsProductBomDomainService;

    @Resource
    private ProductSubstitutionRelationshipService productSubstitutionRelationshipService;

    @Resource
    private NewProductStockPointService newProductStockPointService;

    @Resource
    private MrpFeign mrpFeign;

    @Override
    public BaseResponse<Void> doCreate(ProductBomDTO productBomDTO) {
        // 0.数据转换
        ProductBomDO productBomDO = MdsProductBomConvertor.INSTANCE.dto2Do(productBomDTO);
        ProductBomPO productBomPO = MdsProductBomConvertor.INSTANCE.dto2Po(productBomDTO);
        // 1.数据校验
        mdsProductBomDomainService.validation(productBomDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(productBomPO);
        mdsProductBomDao.insert(productBomPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ProductBomDTO productBomDTO) {
        // 0.数据转换
        ProductBomDO productBomDO = MdsProductBomConvertor.INSTANCE.dto2Do(productBomDTO);
        ProductBomPO productBomPO = MdsProductBomConvertor.INSTANCE.dto2Po(productBomDTO);
        // 1.数据校验
        mdsProductBomDomainService.validation(productBomDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(productBomPO);
        mdsProductBomDao.update(productBomPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ProductBomDTO> list) {
        List<ProductBomPO> newList = MdsProductBomConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        mdsProductBomDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ProductBomDTO> list) {
        List<ProductBomPO> newList = MdsProductBomConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        mdsProductBomDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return mdsProductBomDao.deleteBatch(idList);
        }
        return mdsProductBomDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ProductBomVO selectByPrimaryKey(String id) {
        ProductBomPO po = mdsProductBomDao.selectByPrimaryKey(id);
        return MdsProductBomConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "PRODUCT_BOM")
    public List<ProductBomVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "PRODUCT_BOM")
    public List<ProductBomVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ProductBomVO> dataList = mdsProductBomDao.selectByCondition(sortParam, queryCriteriaParam);
        MdsProductBomServiceImpl target = SpringBeanUtils.getBean(MdsProductBomServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ProductBomVO> selectByParams(Map<String, Object> params) {
        List<ProductBomPO> list = mdsProductBomDao.selectByParams(params);
        return MdsProductBomConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ProductBomVO> selectVOByParams(Map<String, Object> params) {
        return mdsProductBomDao.selectVOByParams(params);
    }

    @Override
    public List<ProductBomVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<ProductBomVO> selectByBillBomVersionIds(List<String> bomVersionIds) {
        if (CollectionUtils.isNotEmpty(bomVersionIds)) {
            return MdsProductBomConvertor.INSTANCE.po2Vos(mdsProductBomDao.selectByBillBomVersionIds(bomVersionIds));
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public void doLogicDeleteBatch(List<RemoveVersionDTO> deleteProductBomList) {
        mdsProductBomDao.doLogicDeleteBatch(deleteProductBomList);

    }

    @Override
    public List<ProductBomVO> selectByComponentSequenceIds(List<String> componentSequenceIds) {
        if (CollectionUtils.isNotEmpty(componentSequenceIds)) {
            return MdsProductBomConvertor.INSTANCE.po2Vos(mdsProductBomDao.selectByComponentSequenceIds(componentSequenceIds));
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<ProductBomVO> invocation(List<ProductBomVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<String> selectIoProductIdsByBomVersionId(String bomVersionId) {
        return mdsProductBomDao.selectIoProductIdsByBomVersionId(bomVersionId);
    }

    @Override
    public List<ProductBomVO> selectComponentSequenceNotNull() {
        return mdsProductBomDao.selectComponentSequenceNotNull();
    }

    @Override
    public PageInfo<ProductBomAlternativeVO> selectAlternative(String productCode, Integer pageNum, Integer pageSize) {
        String scenario = SystemHolder.getScenario();

        List<ProductBomAlternativeVO> result = new ArrayList<>();

        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code", "product_category"))
                .queryParam(ImmutableMap.of("productCode", productCode))
                .build();
        List<NewProductStockPointVO> productStockPointVOList =
                newProductStockPointService.selectProductListByParamOnDynamicColumns(feignDynamicParam.getDynamicColumnParam(),feignDynamicParam.getQueryParam());
        String productCategory = productStockPointVOList.get(0).getProductCategory();

        if (StringUtils.isEmpty(productCategory)) throw new BusinessException("物料大类为空，无法区分类型");

        if (productCategory.equals(NewProductEnum.ORIGINAL_FILM.getCode())) {
            // 查询原片替代映射
            List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOList =
                    mrpFeign.selectGlassSubstitutionRelationshipByParams(scenario, ImmutableMap.of("rawProductCode", productCode));

            if (CollectionUtils.isEmpty(glassSubstitutionRelationshipVOList)) return new PageInfo<>();

            // 生产BOM
            result = glassSubstitutionRelationshipVOList.stream().map(data -> {
                ProductBomAlternativeVO productBomAlternativeVO = new ProductBomAlternativeVO();
                productBomAlternativeVO.setAlternativeProductCode(data.getProductionSubstituteProductCode());
                productBomAlternativeVO.setAlternativeQuantity(data.getProductionInputFactor());
                return productBomAlternativeVO;
            }).collect(Collectors.toList());

            // 替代BOM
            result.addAll(glassSubstitutionRelationshipVOList.stream().map(data -> {
                ProductBomAlternativeVO productBomAlternativeVO = new ProductBomAlternativeVO();
                productBomAlternativeVO.setAlternativeProductCode(data.getSubstituteProductCode());
                productBomAlternativeVO.setAlternativeQuantity(data.getSubstituteInputFactor());
                return productBomAlternativeVO;
            }).collect(Collectors.toList()));
        } else if (productCategory.equals(NewProductEnum.PVB.getCode()) || productCategory.equals(NewProductEnum.B_TYPE.getCode())){
            // 查询PVB/B类替代映射
            List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOList =
                    productSubstitutionRelationshipService.selectByParams(ImmutableMap.of("rawProductCode", productCode));

            if (CollectionUtils.isEmpty(productSubstitutionRelationshipVOList)) return new PageInfo<>();

            result = productSubstitutionRelationshipVOList.stream().map(data -> {
                ProductBomAlternativeVO productBomAlternativeVO = new ProductBomAlternativeVO();
                productBomAlternativeVO.setAlternativeProductCode(data.getSubstituteProductCode());
                productBomAlternativeVO.setAlternativeQuantity(data.getUnitSubstitute());
                productBomAlternativeVO.setAlternativeMethod(data.getSubstitutionTypeDesc());
                productBomAlternativeVO.setPriority(data.getPriority());
                return productBomAlternativeVO;
            }).collect(Collectors.toList());
        }

        // 去重（根据替代料编码）
        result = new ArrayList<>(result.stream()
                .collect(Collectors.toMap(
                        ProductBomAlternativeVO::getAlternativeProductCode,
                        Function.identity(),
                        (existing, replacement) -> existing)).values());

        // 排序（根据优先级）
        result.sort(Comparator.comparing(ProductBomAlternativeVO::getPriority, Comparator.nullsLast(Integer::compare)));

        // 计算分页的起始和结束位置
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, result.size());

        // 物理分页
        List<ProductBomAlternativeVO> pageData = Collections.emptyList();
        if (startIndex < result.size()) {
            pageData = result.subList(startIndex, endIndex);
        }

        // 构建PageInfo对象，包含分页信息和当前页数据
        PageInfo<ProductBomAlternativeVO> pageInfo = new PageInfo<>(pageData);
        pageInfo.setTotal(result.size());
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages((int) Math.ceil((double) result.size() / pageSize));
        pageInfo.setHasNextPage(endIndex < result.size());
        pageInfo.setHasPreviousPage(pageNum > 1);

        return pageInfo;
    }

}