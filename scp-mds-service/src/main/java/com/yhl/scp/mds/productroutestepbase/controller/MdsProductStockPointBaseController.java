package com.yhl.scp.mds.productroutestepbase.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.productroutestepbase.dto.MdsProductStockPointBaseDTO;
import com.yhl.scp.mds.productroutestepbase.service.MdsProductStockPointBaseService;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>MdsProductStockPointBaseController</code>
 * <p>
 * 产品工艺基础数据控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-19 11:29:01
 */
@Slf4j
@Api(tags = "产品工艺基础数据控制器")
@RestController
@RequestMapping("mdsProductStockPointBase")
public class MdsProductStockPointBaseController extends BaseController {

    @Resource
    private MdsProductStockPointBaseService mdsProductStockPointBaseService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MdsProductStockPointBaseVO>> page() {
        List<MdsProductStockPointBaseVO> mdsProductStockPointBaseList = mdsProductStockPointBaseService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MdsProductStockPointBaseVO> pageInfo = new PageInfo<>(mdsProductStockPointBaseList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MdsProductStockPointBaseDTO mdsProductStockPointBaseDTO) {
        return mdsProductStockPointBaseService.doCreate(mdsProductStockPointBaseDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MdsProductStockPointBaseDTO mdsProductStockPointBaseDTO) {
        return mdsProductStockPointBaseService.doUpdate(mdsProductStockPointBaseDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        mdsProductStockPointBaseService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MdsProductStockPointBaseVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, mdsProductStockPointBaseService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "导出")
    @GetMapping(value = "export")
    public void export(HttpServletResponse response) {
        mdsProductStockPointBaseService.export(response);
    }

    @ApiOperation(value = "产品工艺基础数据同步")
    @GetMapping(value = "sync")
    public BaseResponse<Void> syncData() {
        return mdsProductStockPointBaseService.syncData(SystemHolder.getTenantCode(), SystemHolder.getScenario());
    }

    @ApiOperation(value = "数据更新")
    @PostMapping(value = "refresh")
    public BaseResponse<Void> processSelectedProducts(@RequestBody List<String> selectedProductCodes){
        return mdsProductStockPointBaseService.processSelectedProducts(selectedProductCodes);
    }
}
