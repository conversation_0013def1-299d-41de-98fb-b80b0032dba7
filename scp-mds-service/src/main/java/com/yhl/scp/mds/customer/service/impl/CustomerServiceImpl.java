package com.yhl.scp.mds.customer.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpCustomer;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.customer.convertor.CustomerConvertor;
import com.yhl.scp.mds.customer.domain.entity.CustomerDO;
import com.yhl.scp.mds.customer.domain.service.CustomerDomainService;
import com.yhl.scp.mds.customer.dto.CustomerDTO;
import com.yhl.scp.mds.customer.dto.OemCustomerAddressInventoryLogDTO;
import com.yhl.scp.mds.customer.infrastructure.dao.CustomerDao;
import com.yhl.scp.mds.customer.infrastructure.dao.OemCustomerAddressInventoryLogDao;
import com.yhl.scp.mds.customer.infrastructure.po.CustomerPO;
import com.yhl.scp.mds.customer.infrastructure.po.OemCustomerAddressInventoryLogPO;
import com.yhl.scp.mds.customer.service.CustomerService;
import com.yhl.scp.mds.customer.service.OemCustomerAddressInventoryLogService;
import com.yhl.scp.mds.customer.vo.CustomerVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CustomerServiceImpl</code>
 * <p>
 * erp客户应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-02 19:11:59
 */
@Slf4j
@Service
public class CustomerServiceImpl extends AbstractService implements CustomerService {

    @Resource
    private CustomerDao customerDao;

    @Resource
    private CustomerDomainService customerDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private OemCustomerAddressInventoryLogDao oemCustomerAddressInventoryLogDao;

    @Resource
    private OemCustomerAddressInventoryLogService oemCustomerAddressInventoryLogService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(CustomerDTO customerDTO) {
        // 0.数据转换
        CustomerDO customerDO = CustomerConvertor.INSTANCE.dto2Do(customerDTO);
        CustomerPO customerPO = CustomerConvertor.INSTANCE.dto2Po(customerDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        customerDomainService.validation(customerDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(customerPO);
        customerDao.insert(customerPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(CustomerDTO customerDTO) {
        // 0.数据转换
        CustomerDO customerDO = CustomerConvertor.INSTANCE.dto2Do(customerDTO);
        CustomerPO customerPO = CustomerConvertor.INSTANCE.dto2Po(customerDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        customerDomainService.validation(customerDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(customerPO);
        customerDao.update(customerPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CustomerDTO> list) {
        List<CustomerPO> newList = CustomerConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        customerDao.insertBatch(newList);
    }

    public void doCreateBatchKeys(List<CustomerDTO> list) {
        List<CustomerPO> newList = CustomerConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        customerDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<CustomerDTO> list) {
        List<CustomerPO> newList = CustomerConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        customerDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return customerDao.deleteBatch(idList);
        }
        return customerDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public CustomerVO selectByPrimaryKey(String id) {
        CustomerPO po = customerDao.selectByPrimaryKey(id);
        return CustomerConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "CUSTOMER")
    public List<CustomerVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "CUSTOMER")
    public List<CustomerVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<CustomerVO> dataList = customerDao.selectByCondition(sortParam, queryCriteriaParam);
        CustomerServiceImpl target = springBeanUtils.getBean(CustomerServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CustomerVO> selectByParams(Map<String, Object> params) {
        List<CustomerPO> list = customerDao.selectByParams(params);
        return CustomerConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CustomerVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> syncCustomer(String tenantId,String scenario) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("ebsOuId", "324");
        params.put("sitePurpose", "SHIP_TO");
        params.put("scenario", scenario);
        // 调用远程的客户信息
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.CUSTOMER_DATA.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleCustomer(List<ErpCustomer> o) {
        if (CollectionUtils.isEmpty(o)) {
            return BaseResponse.success();
        }
        List<CustomerDTO> insertDTOS = new ArrayList<>();
        List<CustomerDTO> updateDTOS = new ArrayList<>();
        Map<String, CustomerDTO> oldDTOsCustomer = new HashMap<>();
        Map<String, OemCustomerAddressInventoryLogDTO> oldDTOsCustomerAddress = new HashMap<>();
        List<OemCustomerAddressInventoryLogDTO> insertAddressDTOS = new ArrayList<>();
        List<OemCustomerAddressInventoryLogDTO> updateAddressDTOS = new ArrayList<>();
        Set<Integer> customerIdInts =
                o.stream().map(ErpCustomer::getEbsCustomerId).collect(Collectors.toSet());
        Set<String> customerIds = customerIdInts.stream()
                .map(String::valueOf)
                .collect(Collectors.toSet());
        HashMap<String, Object> map = MapUtil.newHashMap(3);
        map.put("customerIds", customerIds);
        List<CustomerPO> oldPos = customerDao.selectByParams(map);
        Map<String, CustomerPO> oldPosMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(
                        Collectors.toMap(CustomerPO::getCustomerId, Function.identity(), (v1, v2) -> v1));
        List<OemCustomerAddressInventoryLogPO> oemCustomerAddressInventoryLogPOS = oemCustomerAddressInventoryLogDao.selectByEbsSiteIdShipToSiteUseId(o);
        Map<String, OemCustomerAddressInventoryLogPO> oemCustomerAddressInventoryLogPOSMap = CollectionUtils.isEmpty(oemCustomerAddressInventoryLogPOS) ?
                MapUtil.newHashMap() :
                oemCustomerAddressInventoryLogPOS.stream().collect(Collectors.toMap(t -> t.getEbsOuId() + "_" + t.getShipToSiteUseId(),
                        Function.identity(), (v1, v2) -> v1));
        for (ErpCustomer erpCustomer : o) {
            CustomerDTO dto = new CustomerDTO();
            String enabled = "A".equals(erpCustomer.getCustomerStatus()) ? YesOrNoEnum.YES.getCode() :
                    YesOrNoEnum.NO.getCode();
            String ebsCustomerId = erpCustomer.getEbsCustomerId() + "";
            if (oldPosMap.containsKey(ebsCustomerId)) {
                if (!oldDTOsCustomer.containsKey(ebsCustomerId)) {
                    CustomerPO oldPo = oldPosMap.get(ebsCustomerId);
                    BeanUtils.copyProperties(oldPo, dto);
                    dto.setCustomerCode(erpCustomer.getCustomerNumber());
                    dto.setCustomerName(erpCustomer.getCustomerName());
                    dto.setEnabled(enabled);
                    oldDTOsCustomer.put(ebsCustomerId, dto);
                    updateDTOS.add(dto);
                }
            } else {
                if (!oldDTOsCustomer.containsKey(ebsCustomerId)) {
                    dto.setCustomerCode(erpCustomer.getCustomerNumber());
                    dto.setCustomerName(erpCustomer.getCustomerName());
                    dto.setEnabled(enabled);
                    dto.setCustomerId(erpCustomer.getEbsCustomerId() + "");
                    dto.setId(UUIDUtil.getUUID());
                    oldDTOsCustomer.put(ebsCustomerId, dto);
                    insertDTOS.add(dto);
                }
            }
            String addressId = erpCustomer.getEbsOuId() + "_" + erpCustomer.getShipToSiteUseId();
            OemCustomerAddressInventoryLogDTO customerAddressDTO = new OemCustomerAddressInventoryLogDTO();
            if (oemCustomerAddressInventoryLogPOSMap.containsKey(addressId)) {
                if (!oldDTOsCustomerAddress.containsKey(addressId)) {
                    OemCustomerAddressInventoryLogPO oldAddressPo = oemCustomerAddressInventoryLogPOSMap.get(addressId);
                    BeanUtils.copyProperties(oldAddressPo, customerAddressDTO);
                    addressDTO(erpCustomer, customerAddressDTO,oldDTOsCustomer.get(ebsCustomerId));
                    oldDTOsCustomerAddress.put(addressId, customerAddressDTO);
                    updateAddressDTOS.add(customerAddressDTO);
                }
            } else {
                if (!oldDTOsCustomerAddress.containsKey(addressId)) {
                    addressDTO(erpCustomer, customerAddressDTO,oldDTOsCustomer.get(ebsCustomerId));
                    oldDTOsCustomerAddress.put(addressId, customerAddressDTO);
                    insertAddressDTOS.add(customerAddressDTO);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(insertDTOS)) {
            doCreateBatchKeys(insertDTOS);
        }
        if (CollectionUtils.isNotEmpty(updateDTOS)) {
            doUpdateBatch(updateDTOS);
        }
        if (CollectionUtils.isNotEmpty(insertAddressDTOS)) {
            oemCustomerAddressInventoryLogService.doCreateBatch(insertAddressDTOS);
        }
        if (CollectionUtils.isNotEmpty(updateAddressDTOS)) {
            oemCustomerAddressInventoryLogService.doUpdateBatch(updateAddressDTOS);
        }
        return BaseResponse.success();
    }

    private void addressDTO(ErpCustomer erpCustomer,  OemCustomerAddressInventoryLogDTO customerAddressDTO, CustomerDTO customerDTO) {
        customerAddressDTO.setCustomerId(customerDTO.getId());
        customerAddressDTO.setEbsOuId(erpCustomer.getEbsOuId() + "");
        customerAddressDTO.setEbsSiteId(erpCustomer.getEbsSiteId() + "");
        customerAddressDTO.setSiteNumber(erpCustomer.getSiteNumber());
        customerAddressDTO.setShipToSiteUseId(erpCustomer.getShipToSiteUseId() + "");
        customerAddressDTO.setBillToSiteUseId(erpCustomer.getBillToSiteUseId());
        customerAddressDTO.setSitePurpose(erpCustomer.getSitePurpose());
        customerAddressDTO.setAddress1(erpCustomer.getAddress1());
        customerAddressDTO.setAddress2(erpCustomer.getAddress2());
        customerAddressDTO.setAddress3(erpCustomer.getAddress3());
        customerAddressDTO.setAddress4(erpCustomer.getAddress4());
        customerAddressDTO.setSiteCountry(erpCustomer.getSiteCountry());
        customerAddressDTO.setCounty(erpCustomer.getCounty());
        customerAddressDTO.setState(erpCustomer.getState());
        customerAddressDTO.setEdiLocation(erpCustomer.getEdiLocation());
        customerAddressDTO.setPostalCode(erpCustomer.getPostalCode());
        customerAddressDTO.setAttribute1(erpCustomer.getAttribute1());
        customerAddressDTO.setAttribute2(erpCustomer.getAttribute2());
        customerAddressDTO.setAttribute3(erpCustomer.getAttribute3());
        customerAddressDTO.setAttribute4(erpCustomer.getAttribute4());
        customerAddressDTO.setAttribute5(erpCustomer.getAttribute5());
        customerAddressDTO.setAttribute6(erpCustomer.getAttribute6());
        customerAddressDTO.setAttribute7(erpCustomer.getAttribute7());
        customerAddressDTO.setPaymentMethod(erpCustomer.getPaymentMethod());
        customerAddressDTO.setPaymentTerms(erpCustomer.getPaymentTerms());
        String ediFlag = "Y".equals(erpCustomer.getEdiFlag()) ? YesOrNoEnum.YES.getCode() :
                YesOrNoEnum.NO.getCode();
        customerAddressDTO.setEdiFlag(ediFlag);
        customerAddressDTO.setObjectVersionNumber(erpCustomer.getObjectVersionNumber() + "");
        customerAddressDTO.setCreationDate(erpCustomer.getCreationDate());
        customerAddressDTO.setLastUpdateDate(erpCustomer.getLastUpdateDate());
        String enabled = "A".equals(erpCustomer.getSiteStatus()) ? YesOrNoEnum.YES.getCode() :
                YesOrNoEnum.NO.getCode();
        customerAddressDTO.setEnabled(enabled);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CUSTOMER.getCode();
    }

    @Override
    public List<CustomerVO> invocation(List<CustomerVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
