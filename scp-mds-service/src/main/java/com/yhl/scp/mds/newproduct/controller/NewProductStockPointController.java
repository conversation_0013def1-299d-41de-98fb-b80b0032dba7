package com.yhl.scp.mds.newproduct.controller;

import java.awt.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.yhl.scp.ips.system.entity.Scenario;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.basic.routing.vo.RoutingStepBasicVO;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.routing.infrastructure.po.RoutingStepOutputPO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.newproduct.dto.NewProductStockPointDTO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointBaseVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.newproduct.vo.ProductMassProductionVO;
import com.yhl.scp.mds.routing.infrastructure.dao.RoutingStepOutputDao;
import com.yhl.scp.mds.routing.service.RoutingService;
import com.yhl.scp.mds.routing.service.RoutingStepService;
import com.yhl.scp.mds.util.LabelValueThree;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>ProductStockPointController</code>
 * <p>
 * 物品控制器
 * </p>
 *
 * @version 1.0
 * @since 2024-07-30 16:02:03
 */
@Slf4j
@Api(tags = "新-物品控制器")
@RestController
@RequestMapping("newProductStockPoint")
public class NewProductStockPointController extends BaseController {

    @Resource
    private NewProductStockPointService newProductStockPointService;


    @Resource
    private RoutingService routingService;
    @Resource
    private RoutingStepService routingStepService;
    @Resource
    private RoutingStepOutputDao routingStepOutputDao;
    @Resource
    private IpsNewFeign ipsNewFeign;

    @ApiOperation(value = "test")
    @GetMapping(value = "test")
    public BaseResponse<String> test() {

        List<RoutingVO> routingVOS = routingService.selectAll();
        List<RoutingStepVO> routingStepVOS = routingStepService.selectAll();
        Map<String, List<RoutingStepVO>> routingStepMap = routingStepVOS.stream().collect(Collectors.groupingBy(RoutingStepBasicVO::getRoutingId));

        List<RoutingStepOutputPO> result = new ArrayList<>();
        Date date = new Date();
        for (RoutingVO routingVO : routingVOS) {
            String id = routingVO.getId();
            List<RoutingStepVO> stepVOS = routingStepMap.get(id);

            RoutingStepVO routingStepVO = stepVOS.stream().sorted(Comparator.comparing(RoutingStepVO::getSequenceNo)
                    .reversed()).collect(Collectors.toList()).get(0);

            RoutingStepOutputPO routingStepOutputDO = new RoutingStepOutputPO();
            routingStepOutputDO.setId(UUIDUtil.getUUID());
            routingStepOutputDO.setRoutingId(id);
            routingStepOutputDO.setRoutingStepId(routingStepVO.getId());
            routingStepOutputDO.setOutputProductId(routingVO.getProductId());
            routingStepOutputDO.setStockPointId(routingVO.getStockPointId());
            routingStepOutputDO.setOutputFactor(BigDecimal.ONE);
            routingStepOutputDO.setYield(BigDecimal.ONE);
            routingStepOutputDO.setMainProduct(YesOrNoEnum.YES.getCode());
            routingStepOutputDO.setModifier("dev-test");
            routingStepOutputDO.setCreator("dev-test");
            routingStepOutputDO.setCreateTime(date);
            routingStepOutputDO.setModifyTime(date);
            result.add(routingStepOutputDO);
        }
        routingStepOutputDao.insertBatch(result);
        log.info("创建输出物品数量");
        return BaseResponse.success(BaseResponse.OP_SUCCESS, "success");
    }


    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<NewProductStockPointVO>> page() {
        List<NewProductStockPointVO> productStockPointList = newProductStockPointService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<NewProductStockPointVO> pageInfo = new PageInfo<>(productStockPointList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody NewProductStockPointDTO newProductStockPointDTO) {
        return newProductStockPointService.doCreate(newProductStockPointDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody NewProductStockPointDTO newProductStockPointDTO) {
        return newProductStockPointService.doUpdate(newProductStockPointDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<RemoveVersionDTO> removeVersionDTOS) {
        newProductStockPointService.deleteBatchVersion(removeVersionDTOS);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<NewProductStockPointVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, newProductStockPointService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "本厂编码下拉")
    @PostMapping(value = "selectProductCode")
    public BaseResponse<List<NewProductStockPointVO>> selectProductCode(@RequestBody List<String> vehicleModelCodeList,
                                                                        @RequestParam(value = "productCode", required = false) String productCode) {
        return BaseResponse.success(newProductStockPointService.selectProductCode(vehicleModelCodeList, productCode));
    }

    @ApiOperation(value = "本厂编码下拉(模糊搜索)")
    @GetMapping(value = "selectProductCodeLike")
    public BaseResponse<List<LabelValue<String>>> selectProductCodeLike(@RequestParam(value = "productCode", required = false) String productCode) {
        List<NewProductStockPointVO> list = newProductStockPointService.selectProductCodeLike(null, productCode,
                new ArrayList<>());
        List<LabelValue<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setLabel(x.getProductCode());
                labelValue.setValue(x.getId());
                result.add(labelValue);
            });
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "本厂编码下拉（模糊搜索去重）")
    @GetMapping(value = "selectProductCodeLikeDistinct")
    public BaseResponse<List<LabelValue<String>>> selectProductCodeLikeDistinct(
            @RequestParam(value = "productCode", required = false) String productCode) {

        List<NewProductStockPointVO> list = newProductStockPointService.selectProductCodeLike(null, productCode,
                new ArrayList<>());
        List<LabelValue<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setLabel(x.getProductCode());
                labelValue.setValue(x.getProductCode());
                result.add(labelValue);
            });
        }
        return BaseResponse.success(result.stream().distinct().collect(Collectors.toList()));
    }


    @ApiOperation(value = "本厂编码下拉(模糊搜索),编码+名称")
    @GetMapping(value = "selectProductCodeLikeName")
    public BaseResponse<List<LabelValueThree<String>>> selectProductCodeLikeName(
    		@RequestParam(value = "stockPointCode", required = false) String stockPointCode,
    		@RequestParam(value = "productCode", required = false) String productCode,
            @RequestParam(value = "logicContains", required = false) String logicContains,
    		@RequestParam(value = "productTypes", required = false, defaultValue = "YES") String productTypes) {
        List<String> productTypeList = StringUtils.isBlank(productTypes)
                ? new ArrayList<>() : Lists.newArrayList(productTypes.split(","));
        List<String> codeList = YesOrNoEnum.YES.getCode().equals(logicContains)
                ? productTypeList : EnumUtils.getCodeList(ProductTypeEnum.class).stream()
            .filter(x -> !productTypeList.contains(x)).collect(Collectors.toList());
        List<NewProductStockPointVO> list = newProductStockPointService.selectProductCodeLike(stockPointCode,
                productCode, codeList);
        List<LabelValueThree<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            result = removeDuplicateLabels(list);
        }
        return BaseResponse.success(result);
    }

    private List<LabelValueThree<String>> removeDuplicateLabels(List<NewProductStockPointVO> list) {
        Set<String> uniqueLabels = new HashSet<>();
        List<LabelValueThree<String>> result = Lists.newArrayList();
        for (NewProductStockPointVO x : list) {
            String label = x.getProductName() + "(" + x.getProductCode() + ")";
            if (uniqueLabels.add(label)) {
                LabelValueThree<String> labelValueTree = new LabelValueThree<>();
                labelValueTree.setLabel(label);
                labelValueTree.setName(x.getProductName());
                labelValueTree.setValue(x.getProductCode());
                result.add(labelValueTree);
            }
        }
        return result;
    }

    @ApiOperation(value = "本厂编码下拉(模糊搜索),编码+名称")
    @GetMapping(value = "productCodeDropDown")
    public BaseResponse<List<LabelValueThree<String>>> productCodeDropDown(@RequestParam(value = "productCode",
            required = false) String productCode) {
        List<NewProductStockPointVO> list = newProductStockPointService.selectProductCodeLike(null, productCode,
                new ArrayList<>());
        List<LabelValueThree<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                LabelValueThree<String> labelValueTree = new LabelValueThree<>();
                labelValueTree.setLabel(x.getProductName() + "(" + x.getProductCode() + ")");
                labelValueTree.setName(x.getProductName());
                labelValueTree.setValue(x.getId());
                result.add(labelValueTree);
            });
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "本厂编码下拉-带ItemID(模糊搜索)")
    @GetMapping(value = "productCodeAndIdDropDown")
    public BaseResponse<List<LabelValueThree<String>>> productCodeAndIdDropDown(@RequestParam(value = "productCode",
            required = false) String productCode) {
        List<NewProductStockPointVO> list = newProductStockPointService.selectProductCodeLike(null, productCode,
                new ArrayList<>());
        List<LabelValueThree<String>> result = Lists.newArrayList();
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange =
                ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(),
                "SALE_ORGANIZATION", "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                if (rangeData.equals(x.getStockPointCode())) {
                    LabelValueThree<String> labelValueTree = new LabelValueThree<>();
                    labelValueTree.setLabel(x.getInventoryItemId());
                    labelValueTree.setValue(x.getProductCode());
                    labelValueTree.setName(x.getProductName());
                    result.add(labelValueTree);
                }
            });
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "生产计划物料编码查询")
    @GetMapping(value = "masterPlanProductDropDown")
    public BaseResponse<List<LabelValue<String>>> masterPlanProductDropdown(
            @RequestParam(value = "productCode", required = false) String productCode) {
        List<NewProductStockPointVO> list = newProductStockPointService.selectProductCodeLike(null, productCode,
                        new ArrayList<>())
                .stream().filter(p -> StringUtils.isNotEmpty(p.getProductType())
                        && !p.getProductType().equals("P")).collect(Collectors.toList());
        List<LabelValue<String>> result = Lists.newArrayList();
        Set<String> productCodeSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                if (!productCodeSet.contains(x.getProductCode())) {
                    productCodeSet.add(x.getProductCode());
                    LabelValue<String> labelValue = new LabelValue<>();
                    labelValue.setLabel(x.getId());
                    labelValue.setValue(x.getProductCode());
                    result.add(labelValue);
                }
            });
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "生产计划物料编码查询（新）")
    @GetMapping(value = "masterPlanProductDropdown")
    public BaseResponse<PageInfo<LabelValue<String>>> masterPlanProductDropdown1(
            @RequestParam(value = "productCode", required = false) String productCode) {
        List<NewProductStockPointVO> list = newProductStockPointService.selectPageByProductCodeLike(productCode,
                getPagination());
        PageInfo<NewProductStockPointVO> pageInfo = new PageInfo<>(list);
        List<LabelValue<String>> dataList = pageInfo.getList().stream()
            .map(item -> new LabelValue<>(item.getProductName(), item.getProductCode()))
                .collect(Collectors.toList());
        dataList.sort(Comparator.comparing(LabelValue::getValue));
        PageInfo<LabelValue<String>> result = new PageInfo<>(dataList);
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setTotal(pageInfo.getTotal());
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "物品类型下拉（模糊搜索）")
    @GetMapping(value = "selectProductTypeLike")
    public BaseResponse<List<LabelValue<String>>> selectProductTypeLike(@RequestParam(value = "productType",
            required = false) String productType) {
        List<NewProductStockPointVO> list = newProductStockPointService.selectProductTypeLike(productType)
                .stream()
                .filter(data -> data != null && StringUtils.isNotEmpty(data.getProductType()))
                .distinct().collect(Collectors.toList());
        List<LabelValue<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setLabel(x.getProductType());
                labelValue.setValue(x.getProductType());
                result.add(labelValue);
            });
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "供应分类下拉（模糊搜索）")
    @GetMapping(value = "getSupplyTypeLike")
    public BaseResponse<List<LabelValue<String>>> getSupplyTypeLike(@RequestParam(value = "supplyType",
            required = false) String supplyType) {
        List<NewProductStockPointVO> list = newProductStockPointService.selectSupplyTypeLike(supplyType)
                .stream()
                .filter(data -> data != null && StringUtils.isNotEmpty(data.getSupplyType()))
                .distinct()
                .collect(Collectors.toList());

        List<LabelValue<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setLabel(x.getSupplyType());
                labelValue.setValue(x.getSupplyType());
                result.add(labelValue);
            });
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "颜色下拉（模糊搜索）")
    @GetMapping(value = "selectProductColorLike")
    public BaseResponse<List<LabelValue<String>>> selectProductColorLike(@RequestParam(value = "productColor",
            required = false) String productColor) {
        List<NewProductStockPointVO> list = newProductStockPointService.selectProductColorLike(productColor)
                .stream()
                .filter(data -> data != null && StringUtils.isNotEmpty(data.getProductColor()))
                .distinct()
                .collect(Collectors.toList());

        List<LabelValue<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setLabel(x.getProductColor());
                labelValue.setValue(x.getProductColor());
                result.add(labelValue);
            });
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "厚度下拉")
    @GetMapping(value = "selectProductThickness")
    public BaseResponse<List<LabelValue<BigDecimal>>> selectProductThickness() {
        List<NewProductStockPointVO> newProductStockPointVOS = newProductStockPointService.selectProductListByParamOnDynamicColumns(Collections.singletonList("product_thickness"), new HashMap<>());
        List<NewProductStockPointVO> list = newProductStockPointVOS
                .stream()
                .filter(data -> data != null && null != data.getProductThickness())
                .distinct()
                .collect(Collectors.toList());

        List<LabelValue<BigDecimal>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                LabelValue<BigDecimal> labelValue = new LabelValue<>();
                labelValue.setLabel(x.getProductThickness().toString());
                labelValue.setValue(x.getProductThickness());
                result.add(labelValue);
            });
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "全部物料编码下拉")
    @GetMapping(value = "dropDownAll")
    public BaseResponse<List<LabelValue<String>>> dropDownAll() {
        return newProductStockPointService.dropDownAll();
    }

    @ApiOperation(value = "物料信息同步")
    @GetMapping(value = "sync")
    public BaseResponse<Void> syncNewProductStockPoints(@RequestParam("stockPoint") String stockPoint) {
        Scenario scenario=new Scenario();
        scenario.setTenantId( SystemHolder.getTenantId());
        scenario.setDataBaseName(SystemHolder.getScenario());
        return newProductStockPointService.syncProductStockPoints(scenario,stockPoint);
    }

    @ApiOperation(value = "下拉查询字段")
    @GetMapping(value = "dropDownField")
    public BaseResponse<List<String>> dropDownField(@RequestParam("field") String field) {
        List<String> newStockPointVOS = newProductStockPointService.selectFields(field);
        return BaseResponse.success(newStockPointVOS);
    }

    @ApiOperation(value = "本厂编码下拉-根据库存点代码")
    @GetMapping(value = "dropDownByStockPointCodes")
    public BaseResponse<List<NewProductStockPointBaseVO>> selectByStockPointCodes(@RequestParam("stockPointCodes") List<String> stockPointCodes) {
        return BaseResponse.success(newProductStockPointService.selectBaseInfoByStockPointCodes(stockPointCodes));
    }

    @ApiOperation(value = "获取车型编码-根据本厂编码")
    @GetMapping(value = "getVehicleModelCode")
    public List<LabelValue<String>> getVehicleModelCode(@RequestParam(value = "stockPointCode", required = false) String stockPointCode
            , @RequestParam(value = "productCode") String productCode) {
        return newProductStockPointService.getVehicleModelCode(stockPointCode, productCode);
    }

    @ApiOperation(value = "获取取数位置-查询装车位置(小类)")
    @GetMapping(value = "getLoadingPositionSub")
    public BaseResponse<List<LabelValue<String>>> getLoadingPositionSub() {
        List<LabelValue<String>> result = new ArrayList<>();
        List<String> list = newProductStockPointService.getLoadingPositionSub();
        if (CollectionUtils.isNotEmpty(list)) {
            result = list.stream()
                    .map(x -> new LabelValue<>(x, x))
                    .collect(Collectors.toList());
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "获取下拉-装车位置")
    @GetMapping(value = "getLoadingPositionType")
    public BaseResponse<List<LabelValue<String>>> getLoadingPositionType() {
        List<LabelValue<String>> result = new ArrayList<>();
        List<String> list = newProductStockPointService.getLoadingPositionType();
        if (CollectionUtils.isNotEmpty(list)) {
            result = list.stream()
                    .map(x -> new LabelValue<>(x, x))
                    .collect(Collectors.toList());
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "本厂编码与主机厂关系")
    @GetMapping(value = "getOemByProductCode")
    public List<LabelValue<String>> getOemByProductCode(@RequestParam(value = "productCode") String productCode) {
        return newProductStockPointService.getOemByProductCode(productCode);
    }

    @ApiOperation(value = "本厂编码与主机厂关系-通过车型编码")
    @GetMapping(value = "getOemByProductCodeAndVehicleCode")
    public List<LabelValue<String>> getOemByProductCodeAndVehicleCode(@RequestParam(value = "productCode") String productCode) {
        return newProductStockPointService.getOemByProductCodeAndVehicle(productCode);
    }

    @ApiOperation(value = "人员交接")
    @PostMapping(value = "handover")
    public BaseResponse<Void> handover(@RequestParam(value = "plannerType") String plannerType,
                                       @RequestParam(value = "originalPlanner") String originalPlanner,
                                       @RequestParam(value = "currentPlanner") String currentPlanner,
                                       @RequestBody List<String> productIds) {
        newProductStockPointService.doHandover(plannerType, originalPlanner, currentPlanner, productIds);
        return BaseResponse.success();
    }

    @ApiOperation(value = "物料编码下拉(模糊搜索)-过滤P和MBPL类型并去重")
    @GetMapping(value = "selectProductCodeByType")
    public BaseResponse<List<LabelValue<String>>> selectProductCodeByType(@RequestParam(value = "productCode", required = false) String productCode) {
        List<NewProductStockPointVO> newProductStockPointVOS = newProductStockPointService.selectProductCodeLike(null
                , productCode, new ArrayList<>());
        List<NewProductStockPointVO> filteredAndDistinctVOS = newProductStockPointVOS.stream()
                .filter(vo -> !"P".equals(vo.getProductType()) && !"MBPL".equals(vo.getProductType()))
                .distinct()
                .collect(Collectors.toList());
        List<LabelValue<String>> list = filteredAndDistinctVOS.stream()
                .map(x -> new LabelValue<>(x.getProductName(), x.getProductCode()))
                .collect(Collectors.toList());
        return BaseResponse.success(list);
    }

    @ApiOperation(value = "内部车型编码下拉")
    @GetMapping(value = "selectVehicleModelCode")
    public BaseResponse<List<String>> selectVehicleModelCode() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, newProductStockPointService.selectVehicleModelCode());
    }


    @ApiOperation(value = "成品物料编码下拉（模糊搜索）")
    @GetMapping(value = "fgProductCodeDropDown")
    public BaseResponse<List<LabelValue<String>>> fgProductCodeDropDown(@RequestParam(value = "productCode", required = false) String productCode) {
        // 模糊搜索
        List<NewProductStockPointVO> list = newProductStockPointService.selectProductCodeLike(null, productCode, new ArrayList<>());

        // 过滤出FG物料即成品物料
        list = list.stream().filter(data -> data.getProductType().equals(ProductTypeEnum.FG.getCode())).collect(Collectors.toList());

        // 构建下拉框结构
        List<LabelValue<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setLabel(x.getProductCode() + "(" + x.getProductName() + ")");
                labelValue.setValue(x.getProductCode());
                result.add(labelValue);
            });
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "校验产品理货单模式")
    @GetMapping(value = "checkTallyOrderMode")
    public BaseResponse<String> checkTallyOrderMode(@RequestParam(value = "productCode") String productCode) {
    	return BaseResponse.success(BaseResponse.OP_SUCCESS, newProductStockPointService.checkTallyOrderMode(productCode));
    }
    @ApiOperation(value = "销售组织本厂编码下拉(模糊搜索)")
    @GetMapping(value = "productCodeForSaleOrganizationDropDown")
    public BaseResponse<List<LabelValueThree<String>>> productCodeForSaleOrganizationDropDown(@RequestParam(value = "productCode",
            required = false) String productCode) {
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange =
                ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(),
                "SALE_ORGANIZATION", "INTERNAL", null);
        List<NewProductStockPointVO> list =
                newProductStockPointService.selectProductCodeLike(scenarioBusinessRange.getData().getRangeData(),
                productCode, new ArrayList<>());
        List<LabelValueThree<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                LabelValueThree<String> labelValueTree = new LabelValueThree<>();
                labelValueTree.setLabel(x.getProductCode() + "(" + x.getProductName() + ")");
                labelValueTree.setValue(x.getId());
                result.add(labelValueTree);
            });
        }
        return BaseResponse.success(result);
    }
    
    @ApiOperation(value = "根据车型获取物料下拉框")
    @GetMapping(value = "selectDropDownByVehicleModelCode")
    public BaseResponse<List<LabelValue<String>>> selectDropDownByVehicleModelCode(@RequestParam(value = "vehicleModelCode") String vehicleModelCode) {
    	List<LabelValue<String>> list = newProductStockPointService.selectDropDownByVehicleModelCode(vehicleModelCode);
        return BaseResponse.success(list);
    }
    
    @ApiOperation(value = "获取物料属性信息（量产移交）")
    @PostMapping(value = "selectProductMassProduction")
    public BaseResponse<List<ProductMassProductionVO>> selectProductMassProduction(@RequestBody List<String> productCodes) {
    	List<ProductMassProductionVO> list = newProductStockPointService.selectProductMassProduction(productCodes);
        return BaseResponse.success(list);
    }
    
    @ApiOperation(value = "获取车型编码-根据本厂编码(销售组织)")
    @GetMapping(value = "getSaleVehicleModelCode")
    public BaseResponse<String> getSaleVehicleModelCode(@RequestParam(value = "productCode") String productCode) {
        return BaseResponse.success(null, newProductStockPointService.getSaleVehicleModelCode(productCode));
    }

    @ApiOperation(value = "导出（当前）")
    @GetMapping(value = "exportCurrent")
    public void export(HttpServletResponse response) {
        newProductStockPointService.exportCurrent(response, getPagination(),getSortParam(), getQueryCriteriaParam());
    }

    @ApiOperation(value = "获取总成物品下面所有对应的半品物料")
    @PostMapping(value = "getAllSemiFinishedProducts")
    public BaseResponse<List<NewProductStockPointVO>> getAllSemiFinishedProducts(
            @RequestBody List<NewProductStockPointVO> newProductStockPointVOS) {
        List<NewProductStockPointVO> result = newProductStockPointService.getAllSemiFinishedProducts(newProductStockPointVOS);
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "测试获取半品物料功能")
    @GetMapping(value = "testGetSemiFinishedProducts")
    public BaseResponse<List<NewProductStockPointVO>> testGetSemiFinishedProducts(
            @RequestParam(value = "productCodes") String productCodes) {
        try {
            String[] codes = productCodes.split(",");
            List<String> productCodeList = Arrays.asList(codes);

            // 查询物料信息
            List<NewProductStockPointVO> productList = newProductStockPointService.selectByParams(ImmutableMap.of(
                    "productCodes", productCodeList,
                    "enabled", YesOrNoEnum.YES.getCode()));

            if (CollectionUtils.isEmpty(productList)) {
                return BaseResponse.error("未找到指定的物料信息");
            }

            // 调用获取半品物料方法
            List<NewProductStockPointVO> result = newProductStockPointService.getAllSemiFinishedProducts(productList);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("测试获取半品物料功能失败", e);
            return BaseResponse.error("测试失败：" + e.getMessage());
        }
    }
}
