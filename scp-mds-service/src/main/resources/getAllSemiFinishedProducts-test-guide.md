# 获取总成物品下面所有对应的半品物料 - 测试指南

## 测试准备

### 1. 数据库修改验证
确认以下XML文件已经正确添加了批量查询支持：

**NewRoutingDao.xml**
```xml
<if test="params.stockPointIds != null and params.stockPointIds.size() > 0">
    and stock_point_id IN
    <foreach collection="params.stockPointIds" item="stockPointId" open="(" separator="," close=")">
        #{stockPointId,jdbcType=VARCHAR}
    </foreach>
</if>
```

**NewRoutingStepDao.xml**
```xml
<if test="params.routingIds != null and params.routingIds.size() > 0">
    and routing_id IN
    <foreach collection="params.routingIds" item="routingId" open="(" separator="," close=")">
        #{routingId,jdbcType=VARCHAR}
    </foreach>
</if>
```

### 2. 接口验证
确认以下接口已经正确添加：

**NewMdsFeign.java**
```java
@ApiOperation(value = "获取总成物品下面所有对应的半品物料")
@PostMapping("newProductStockPoint/getAllSemiFinishedProducts")
BaseResponse<List<NewProductStockPointVO>> getAllSemiFinishedProducts(@RequestHeader("scenario") String scenario, @RequestBody List<NewProductStockPointVO> newProductStockPointVOS);
```

**NewMdsFeignController.java**
```java
@Override
public BaseResponse<List<NewProductStockPointVO>> getAllSemiFinishedProducts(String scenario, List<NewProductStockPointVO> newProductStockPointVOS) {
    List<NewProductStockPointVO> result = newProductStockPointService.getAllSemiFinishedProducts(newProductStockPointVOS);
    return BaseResponse.success(result);
}
```

## 测试步骤

### 1. 单元测试 - 直接调用服务方法

```java
@Test
public void testGetAllSemiFinishedProducts() {
    // 准备测试数据
    List<NewProductStockPointVO> inputProducts = new ArrayList<>();
    
    NewProductStockPointVO product = new NewProductStockPointVO();
    product.setId("test-product-id");
    product.setProductCode("TEST001");
    product.setPoCategory("S1.P1"); // 销售组织.生产组织
    inputProducts.add(product);
    
    // 调用方法
    List<NewProductStockPointVO> result = newProductStockPointService.getAllSemiFinishedProducts(inputProducts);
    
    // 验证结果
    assertNotNull(result);
    System.out.println("获取到物料总数量: " + result.size());

    // 验证是否包含输入的总成物料本身
    boolean containsInputProduct = result.stream()
            .anyMatch(p -> "TEST001".equals(p.getProductCode()));
    assertTrue("结果应包含输入的总成物料", containsInputProduct);

    for (NewProductStockPointVO product : result) {
        System.out.println("物料: " + product.getProductCode() + " - " + product.getProductName() +
                          " (供应类型: " + product.getSupplyType() + ", 分类: " + product.getProductClassify() + ")");

        // 验证物料属性
        if (!"TEST001".equals(product.getProductCode())) {
            // 对于半品物料的验证
            assertEquals("制造", product.getSupplyType());
            assertNotEquals("C0.0", product.getProductClassify());
        }
    }
}
```

### 2. 接口测试 - 通过HTTP调用

**测试接口**
```
POST /newProductStockPoint/getAllSemiFinishedProducts
Content-Type: application/json
scenario: test-scenario
```

**请求体示例**
```json
[
    {
        "id": "product-id-1",
        "productCode": "PRODUCT001",
        "productName": "总成产品1",
        "poCategory": "S1.P1",
        "supplyType": "制造",
        "productClassify": "C1.0"
    }
]
```

**预期响应**
```json
{
    "code": "200",
    "message": "操作成功",
    "data": [
        {
            "id": "product-id-1",
            "productCode": "PRODUCT001",
            "productName": "总成产品1",
            "poCategory": "S1.P1",
            "supplyType": "制造",
            "productClassify": "C1.0",
            "stockPointCode": "P1"
        },
        {
            "id": "semi-product-id-1",
            "productCode": "SEMI001",
            "productName": "半品物料1",
            "poCategory": "S1.P1",
            "supplyType": "制造",
            "productClassify": "C2.0"
        }
    ]
}
```

### 3. 集成测试 - 在SdsOrdWorkOrderSupplementaryPublishLogServiceImpl中验证

**测试位置**: 第570行附近
```java
// 获取总成物品下面所有对应的半品物料
List<NewProductStockPointVO> semiFinishedProducts = new ArrayList<>();
try {
    BaseResponse<List<NewProductStockPointVO>> semiFinishedResponse = 
        mdsFeign.getAllSemiFinishedProducts(scenario, newProductStockPointVOS);
    if (semiFinishedResponse != null && semiFinishedResponse.isSuccess() && 
        CollectionUtils.isNotEmpty(semiFinishedResponse.getData())) {
        semiFinishedProducts = semiFinishedResponse.getData();
        log.info("获取到{}个半品物料", semiFinishedProducts.size());
    } else {
        log.warn("获取半品物料失败或结果为空");
    }
} catch (Exception e) {
    log.error("调用获取半品物料方法失败", e);
}
```

## 性能测试

### 1. 批量查询性能验证

**测试场景**: 输入100个物料，每个物料有3层BOM结构
**预期结果**: 
- 数据库查询次数 < 10次
- 响应时间 < 5秒
- 内存使用合理

**监控指标**:
```java
long startTime = System.currentTimeMillis();
List<NewProductStockPointVO> result = newProductStockPointService.getAllSemiFinishedProducts(inputProducts);
long endTime = System.currentTimeMillis();
System.out.println("执行时间: " + (endTime - startTime) + "ms");
System.out.println("结果数量: " + result.size());
```

### 2. 内存使用测试

```java
Runtime runtime = Runtime.getRuntime();
long beforeMemory = runtime.totalMemory() - runtime.freeMemory();

List<NewProductStockPointVO> result = newProductStockPointService.getAllSemiFinishedProducts(inputProducts);

long afterMemory = runtime.totalMemory() - runtime.freeMemory();
System.out.println("内存使用增加: " + (afterMemory - beforeMemory) / 1024 / 1024 + "MB");
```

## 故障排查

### 1. 常见问题

**问题1**: 查询结果为空
- 检查po_category格式是否正确（销售组织.生产组织）
- 检查工艺路径数据是否存在
- 检查物料的供应类型和产品分类

**问题2**: 性能问题
- 检查是否使用了批量查询
- 查看数据库执行计划
- 监控内存使用情况

**问题3**: 循环引用
- 检查processedProductIds是否正常工作
- 查看日志中的递归深度

### 2. 日志分析

**关键日志**:
```
INFO - 获取到X个半品物料
WARN - 物料编码：XXX，采购类别为空，跳过处理
WARN - 物料编码：XXX，采购类别格式不正确：XXX，跳过处理
ERROR - 批量查询数据时发生异常
ERROR - 批量递归查询产品编码：XXX的半品物料时发生异常
```

### 3. 数据验证

**验证SQL**:
```sql
-- 验证工艺路径数据
SELECT * FROM mds_rou_routing WHERE product_code = 'XXX' AND stock_point_id = 'XXX';

-- 验证工艺步骤数据
SELECT * FROM mds_rou_routing_step WHERE routing_id = 'XXX';

-- 验证BOM数据
SELECT * FROM mds_rou_routing_step_input WHERE routing_id = 'XXX';

-- 验证物料数据
SELECT * FROM mds_new_product_stock_point WHERE supply_type = '制造' AND product_classify != 'C0.0';
```

## 测试清单

- [ ] XML文件修改正确
- [ ] 接口方法添加正确
- [ ] 服务方法实现正确
- [ ] 单元测试通过
- [ ] 接口测试通过
- [ ] 集成测试通过
- [ ] 性能测试满足要求
- [ ] 错误处理正常
- [ ] 日志记录完整
- [ ] 数据验证正确

## 部署注意事项

1. **数据库变更**: 确保XML文件的修改已经部署
2. **接口变更**: 确保Feign接口的修改在所有相关服务中同步
3. **配置检查**: 确保相关的配置参数正确
4. **监控设置**: 设置相关的性能监控和告警
5. **回滚准备**: 准备回滚方案，以防出现问题
