# 获取总成物品下面所有对应的半品物料 - 使用说明

## 功能描述

该功能用于根据物料列表对象，通过物料的po_category找到对应的销售组织跟生产组织，然后递归查询工艺路径，获取总成物品下面所有对应的半品物料。

**功能特点**:
1. **包含总成物料本身**: 返回结果中包含输入的总成物料在对应生产组织下的物料信息
2. **递归查询半品**: 递归查询总成物料下面所有对应的半品物料
3. **性能优化**: 采用批量查询策略，先一次性查询所有相关数据到内存，然后在内存中进行匹配和递归处理，大幅提升查询性能

## API接口

### 接口地址
```
POST /newProductStockPoint/getAllSemiFinishedProducts
```

### 请求参数
- **Content-Type**: application/json
- **Body**: List<NewProductStockPointVO> - 物料列表对象

### 响应结果
- **返回类型**: BaseResponse<List<NewProductStockPointVO>>
- **返回内容**: 半品物料列表

## 实现逻辑

### 批量查询优化策略
1. **数据预处理**: 收集所有有效的产品编码和库存点ID
2. **批量数据查询**: 一次性查询所有相关的工艺路径、工艺步骤、BOM信息
3. **内存数据组织**: 将查询结果按关键字段分组，建立内存索引
4. **内存递归处理**: 在内存中进行匹配和递归查询，避免重复数据库访问

### 具体处理步骤
1. **解析采购类别**: 根据物料的po_category字段，用"."分隔，取出对应的生产组织
2. **添加总成物料**: 查询并添加输入的总成物料在对应生产组织下的物料信息
3. **批量查询工艺路径**: 按库存点分组批量查询mds_rou_routing表的工艺路径头信息
4. **批量查询工艺步骤**: 一次性查询所有工艺路径对应的mds_rou_routing_step表数据
5. **批量查询BOM信息**: 一次性查询所有工艺路径对应的mds_rou_routing_step_input表数据
6. **批量查询物料信息**: 一次性查询所有可能的输入物料基础信息
7. **内存递归处理**: 在内存中递归查询每个半品物料的下级半品物料
8. **筛选半品**: 筛选出供应类型为"制造"且产品分类不是"C0.0"的物料

## 使用示例

### Java代码示例

```java
@Resource
private NewProductStockPointService newProductStockPointService;

public void example() {
    // 准备输入数据
    List<NewProductStockPointVO> inputProducts = new ArrayList<>();
    
    NewProductStockPointVO product = new NewProductStockPointVO();
    product.setId("product-id-1");
    product.setProductCode("PRODUCT001");
    product.setPoCategory("S1.S2"); // 销售组织.生产组织
    inputProducts.add(product);
    
    // 调用方法获取半品物料
    List<NewProductStockPointVO> semiFinishedProducts = 
        newProductStockPointService.getAllSemiFinishedProducts(inputProducts);
    
    // 处理结果
    for (NewProductStockPointVO semiProduct : semiFinishedProducts) {
        System.out.println("半品物料编码: " + semiProduct.getProductCode());
        System.out.println("半品物料名称: " + semiProduct.getProductName());
    }
}
```

### HTTP请求示例

```json
POST /newProductStockPoint/getAllSemiFinishedProducts
Content-Type: application/json

[
    {
        "id": "product-id-1",
        "productCode": "PRODUCT001",
        "productName": "总成产品1",
        "poCategory": "S1.S2",
        "supplyType": "制造",
        "productClassify": "C1.0"
    }
]
```

### 响应示例

```json
{
    "code": "200",
    "message": "操作成功",
    "data": [
        {
            "id": "product-id-1",
            "productCode": "PRODUCT001",
            "productName": "总成产品1",
            "poCategory": "S1.S2",
            "supplyType": "制造",
            "productClassify": "C1.0",
            "stockPointCode": "S2"
        },
        {
            "id": "semi-product-id-1",
            "productCode": "SEMI001",
            "productName": "半品物料1",
            "poCategory": "S1.S2",
            "supplyType": "制造",
            "productClassify": "C2.0"
        },
        {
            "id": "semi-product-id-2",
            "productCode": "SEMI002",
            "productName": "半品物料2",
            "poCategory": "S1.S2",
            "supplyType": "制造",
            "productClassify": "C3.0"
        }
    ]
}
```

## 性能优化特性

1. **批量查询**: 采用批量查询策略，减少数据库访问次数
2. **内存处理**: 将数据加载到内存后进行处理，避免重复查询
3. **数据分组**: 按关键字段对查询结果进行分组，提高查找效率
4. **一次性加载**: 预先加载所有可能用到的数据，减少递归过程中的数据库访问

## 注意事项

1. **循环引用防护**: 方法内部使用Set<String>来防止循环引用，避免无限递归
2. **异常处理**: 对每个物料的处理都有异常捕获，单个物料处理失败不会影响其他物料
3. **数据去重**: 最终结果会根据物料ID进行去重
4. **日志记录**: 关键步骤都有日志记录，便于问题排查
5. **内存使用**: 批量查询会占用更多内存，适合中等规模的数据处理
6. **数据完整性**: 需要确保po_category字段格式正确（销售组织.生产组织）
7. **权限控制**: 调用此方法需要有相应的数据访问权限
8. **数据一致性**: 确保工艺路径、BOM等基础数据的完整性和准确性

## 相关表结构

- **mds_rou_routing**: 工艺路径头表
- **mds_rou_routing_step**: 工艺路径步骤表  
- **mds_rou_routing_step_input**: 工艺路径步骤输入物品表
- **mds_new_product_stock_point**: 物料基础信息表

## 关联关系

```
mds_rou_routing_step.routing_id = mds_rou_routing.id
mds_rou_routing_step_input.routing_id = mds_rou_routing.id
mds_rou_routing_step_input.routing_step_id = mds_rou_routing_step.id
```
