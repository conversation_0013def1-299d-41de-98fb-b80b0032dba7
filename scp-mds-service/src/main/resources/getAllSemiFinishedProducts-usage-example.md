# 获取总成物品下面所有对应的半品物料 - 使用说明

## 功能描述

该功能用于根据物料列表对象，通过物料的po_category找到对应的销售组织跟生产组织，然后递归查询工艺路径，获取总成物品下面所有对应的半品物料。

## API接口

### 接口地址
```
POST /newProductStockPoint/getAllSemiFinishedProducts
```

### 请求参数
- **Content-Type**: application/json
- **Body**: List<NewProductStockPointVO> - 物料列表对象

### 响应结果
- **返回类型**: BaseResponse<List<NewProductStockPointVO>>
- **返回内容**: 半品物料列表

## 实现逻辑

1. **解析采购类别**: 根据物料的po_category字段，用"."分隔，取出对应的生产组织
2. **查询工艺路径**: 通过生产组织和物料id去mds_rou_routing表找到对应的工艺路径头信息
3. **查询工艺步骤**: 去mds_rou_routing_step表找到对应的工艺路径行信息
4. **查询BOM信息**: 去mds_rou_routing_step_input表找到工艺路径对应的所有bom信息
5. **递归查询**: 对于每个半品物料，继续递归查询其下级半品物料
6. **筛选半品**: 筛选出供应类型为"制造"且产品分类不是"C0.0"的物料

## 使用示例

### Java代码示例

```java
@Resource
private NewProductStockPointService newProductStockPointService;

public void example() {
    // 准备输入数据
    List<NewProductStockPointVO> inputProducts = new ArrayList<>();
    
    NewProductStockPointVO product = new NewProductStockPointVO();
    product.setId("product-id-1");
    product.setProductCode("PRODUCT001");
    product.setPoCategory("S1.S2"); // 销售组织.生产组织
    inputProducts.add(product);
    
    // 调用方法获取半品物料
    List<NewProductStockPointVO> semiFinishedProducts = 
        newProductStockPointService.getAllSemiFinishedProducts(inputProducts);
    
    // 处理结果
    for (NewProductStockPointVO semiProduct : semiFinishedProducts) {
        System.out.println("半品物料编码: " + semiProduct.getProductCode());
        System.out.println("半品物料名称: " + semiProduct.getProductName());
    }
}
```

### HTTP请求示例

```json
POST /newProductStockPoint/getAllSemiFinishedProducts
Content-Type: application/json

[
    {
        "id": "product-id-1",
        "productCode": "PRODUCT001",
        "productName": "总成产品1",
        "poCategory": "S1.S2",
        "supplyType": "制造",
        "productClassify": "C1.0"
    }
]
```

### 响应示例

```json
{
    "code": "200",
    "message": "操作成功",
    "data": [
        {
            "id": "semi-product-id-1",
            "productCode": "SEMI001",
            "productName": "半品物料1",
            "poCategory": "S1.S2",
            "supplyType": "制造",
            "productClassify": "C2.0"
        },
        {
            "id": "semi-product-id-2",
            "productCode": "SEMI002",
            "productName": "半品物料2",
            "poCategory": "S1.S2",
            "supplyType": "制造",
            "productClassify": "C3.0"
        }
    ]
}
```

## 注意事项

1. **循环引用防护**: 方法内部使用Set<String>来防止循环引用，避免无限递归
2. **异常处理**: 对每个物料的处理都有异常捕获，单个物料处理失败不会影响其他物料
3. **数据去重**: 最终结果会根据物料ID进行去重
4. **日志记录**: 关键步骤都有日志记录，便于问题排查
5. **性能考虑**: 递归深度有限制，避免过深的递归调用
6. **库存点映射**: 方法会根据po_category中的生产组织代码查找对应的库存点ID
7. **数据完整性**: 需要确保po_category字段格式正确（销售组织.生产组织）
8. **权限控制**: 调用此方法需要有相应的数据访问权限

## 相关表结构

- **mds_rou_routing**: 工艺路径头表
- **mds_rou_routing_step**: 工艺路径步骤表  
- **mds_rou_routing_step_input**: 工艺路径步骤输入物品表
- **mds_new_product_stock_point**: 物料基础信息表

## 关联关系

```
mds_rou_routing_step.routing_id = mds_rou_routing.id
mds_rou_routing_step_input.routing_id = mds_rou_routing.id
mds_rou_routing_step_input.routing_step_id = mds_rou_routing_step.id
```
